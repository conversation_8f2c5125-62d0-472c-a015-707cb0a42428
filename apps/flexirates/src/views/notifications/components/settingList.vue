<script setup lang="ts">
import { computed, ref } from 'vue'
import BaseDataTable from '@/components/common/BaseDataTable.vue'

const props = defineProps<{
  tab: { label: string, value: number }
  dataList: any[]
  events: any[]
  loading: boolean
}>()

const emits = defineEmits<{
  (e: 'updateData', value: { id: string, [key: string]: string }): void
}>()

const list = computed(() => {
  if (props?.dataList?.length === 0) {
    return []
  }
  return props?.dataList?.filter((item: { category: number | string }) => item?.category === props?.tab?.value)?.[0]?.events
})

const failureMessage = ref('false')
const notificationPaymentsTable = ref('false')

// 列配置
const columns = ref<TableColumnItem[]>([
  { field: 'name', header: props?.tab?.label || 'title', template: 'name', style: { width: '260px' } },
  { field: 'event', header: '', template: 'abstract', style: { width: 'auto' } },
])
// eslint-disable-next-line array-callback-return
props?.events?.map((item: { label: string, value: string }) => {
  columns.value.push({ field: item?.value, header: item?.label, template: item?.value, style: { width: '80px' } })
})
const handleEvent = (data: Record<string, any>, event: string, value: boolean) => {
  console.log(data, event, value)
  emits('updateData', { ...data, id: data.id, [event]: value ? '1' : '0' })
}
</script>

<template>
  <div class="notification-page">
    <BaseDataTable
      ref="notificationPaymentsTable" table-class="notification-table" :show-gridlines="false"
      :striped-rows="false" :show-search-bar="false" :value="list" :columns="columns" :scrollable="true"
      :show-multiple-column="false" :loading="loading" :paginator="false" :lazy="true" data-key="id" :failed="false"
      :failure-message="failureMessage" style="--frozen-column-border-bottom : -8px"
    >
      <template #name="{ data }">
        <div class="title-wrapper">
          <ToggleSwitch
            :model-value="!!data.is_enable" :style="{ verticalAlign: 'middle', marginRight: '6px' }"
            @update:model-value="handleEvent(data, 'is_enable', $event)"
          />
          <div class="title" :title="data.name">
            {{ data.name }}
          </div>
        </div>
      </template>
      <template #abstract="{ data }">
        <div class="abstract" :title="data?.event">
          {{ data?.event }}
        </div>
      </template>
      <template v-for="item in props.events" #[item.value]="{ data }" :key="item.value">
        <div class="check-box">
          <Checkbox
            binary :default-value="!!data[item.value]" :disabled="!data.is_enable"
            @update:model-value="handleEvent(data, item.value, $event)"
          />
        </div>
      </template>
    </BaseDataTable>
  </div>
</template>

<style lang="scss" scoped>
.notification-page {
  .check-box {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title-wrapper {
    display: flex;
    align-items: center;
    width: 100%;

    .title {
      margin-left: 0.85rem;
      width: 220px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 500;
      font-size: 1.25rem;
    }
  }

  .abstract {
    width: 40vw;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  :deep(.p-datatable-tbody > tr > td) {

    &:first-of-type {
      padding-left: 0;
    }

    &:last-of-type {
      padding-right: 0px;
    }
  }

  :deep(.p-datatable .p-datatable-table-container) {
    border: 1px solid transparent;
    --p-datatable-header-cell-background: transparent;
  }

  :deep(.p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd) {
    background: transparent;
  }

  :deep(.p-datatable .p-datatable-table-container .p-datatable-tbody tr td) {
    border-bottom: 1px solid var(--color-gray-50);
  }

  :deep(.p-datatable .p-datatable-table-container .p-datatable-thead) {
    .p-datatable-column-header-content {
      border-right: 1px solid transparent;
    }

    >tr {
      .p-datatable-header-cell {
        border-bottom: 1px solid var(--color-gray-50);
        background: transparent;

        .p-datatable-column-header-content {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        &:first-of-type {
          .p-datatable-column-header-content {
            justify-content: flex-start;
          }

          .p-datatable-column-header-content {
            padding-left: 0;
            font-weight: 400;
            font-size: 1.32rem;
            color: var(--color-indigo-950);
          }
        }

        &:last-of-type .p-datatable-column-header-content {
          padding-right: 0;
        }
      }
    }

    >th {
      &:first-child {
        border-radius: 0px;
      }

      &:last-child {
        border-radius: 0px;
      }
    }
  }

  :deep(.p-toggleswitch.p-toggleswitch-checked .p-toggleswitch-slider) {
    background: var(--color-orange-500);
    border-color: var(--color-orange-500);
  }

  :deep(.p-checkbox.p-disabled .p-checkbox-box) {
    background: var(--color-gray-50);
    border-color: var(--color-gray-50);
  }

  :deep(.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover).p-toggleswitch-checked .p-toggleswitch-slider) {
    background: var(--color-orange-500);
    border-color: var(--color-orange-500);
  }

  :deep(.p-checkbox-checked .p-checkbox-box) {
    background: var(--color-orange-500);
    border-color: var(--color-orange-500);

  }

  :deep(.p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-box) {
    background: var(--color-orange-500);
    border-color: var(--color-orange-500);
  }
}
</style>
