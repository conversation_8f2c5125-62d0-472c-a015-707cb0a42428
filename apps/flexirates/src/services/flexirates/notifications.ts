import { GET, POST } from '@/services/http'

export const getNotifyList = (params: Api.FlexiratesNotifyListReq) => GET('/notify/list', { params })

export const deleteNotify = (data: Api.FlexiratesNotifyDeleteReq) => POST('/notify/delete', data)

export const updateNotify = (data: Api.FlexiratesNotifyDeleteReq) => POST('/notify/updateRead', data)

export const getUnreadCount = () => GET('/notify/unreadCount')

export const markNotify = (data: Api.EditNotifyRef) => POST('/notify/markReadStatus', data)

export const getPreferencesList = (params?: any) => GET('/notify/preferences', { params })

export const updatePreferences = (data?: Api.PreferencesUpdateRef) => POST('/notify/preferencesUpdate', data)

export const updateNotification = (data: Api.NotificationReq) => POST<CommonRes<Api.NotificationListRes>>('/communicationConfig/update', data)
