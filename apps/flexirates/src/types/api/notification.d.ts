declare namespace Api {

  interface NotificationReq {
    content?: null | string
    logo: string
    /**
     * 通知种类：1：Successful payment with recipt 2：Upcoming payment 3：Retry payment 4:Subscription
     * Confirmation 5:Payment method update（详情查询字典notification_type）
     */
    notification_type: number
    theme: string
  }
  interface NotificationListTemplate {
    content?: string
    created_at?: string
    deleted_at?: null
    id?: number
    merchant_id?: string
    /**
     * 通知种类
     */
    notification_type?: number
    /**
     * 通知类型（1：Email 2：SMS）
     */
    type?: number
    updated_at?: null

  }
  interface NotificationListRes {
    current_page: number
    data: NotificationListTemplate[]
    per_page: number
    total: number
  }
  export interface FlexiratesNotifyListReq {
    category?: number
    /**
     * YYYY-MM-DD hh:mm:ss
     */
    end_date?: string
    event?: number
    /**
     * 可选：0，1
     */
    is_read?: number
    keyword?: string
    page: number
    page_size: number
    /**
     * YYYY-MM-DD hh:mm:ss
     */
    start_date?: string
    [property: string]: any
  }
}
