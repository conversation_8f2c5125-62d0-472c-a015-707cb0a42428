<script setup lang="ts">
import type { AxiosProgressEvent } from 'axios'
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import userDefaultImage from '@/assets/flexirates/header/profile-icon.png'
import noticeImage from '@/assets/merchant/notification-bell.png'
import BasePopover from '@/components/common/BasePopover.vue'
// import ThemeToggle from '@/components/common/ThemeToggle.vue'
import { user as userApi } from '@/services/flexirates'
import { getNotifyList } from '@/services/flexirates/notifications'
import { useAppStore } from '@/store/modules/app'
import { useLayoutStore } from '@/store/modules/layout'
import { useNotificationStore } from '@/store/modules/notifications'
import { useUserStore } from '@/store/modules/user'
import Breadcrumbs from './appBreadcrumbs.vue'

const router = useRouter()
const userStore = useUserStore()

const { user } = storeToRefs(userStore)

const appStore = useAppStore()

const layoutStore = useLayoutStore()
const { isSidebarSlim } = storeToRefs(layoutStore)

const uploadAvatarVisible = ref(true)
const uploadAvatarProgress = ref(0)
const { locale } = storeToRefs(appStore)

watch(locale, (newValue) => {
  appStore.setLocale(newValue as 'en' | 'zh')
})

const avatarUrl = computed(() => {
  return user.value?.avatar || userDefaultImage
})

const notificationStore = useNotificationStore()
const { unreadCount } = storeToRefs(notificationStore)
// const hasUnreadNotice = computed(() => unreadCount.value > 0)
const noticePopoverRef = ref()
const noticePopoverList = ref<any[]>([])
const notice_is_read = ref<number | string>('ALL')
const noticeLoading = ref<boolean>(true)
onMounted(() => {
  notificationStore.startPolling()
})
onUnmounted(() => {
  notificationStore.stopPolling()
})

const uploadAvatar = () => {
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.accept = 'image/png, image/jpeg, image/jpg, image/gif'

  fileInput.onchange = async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) { return }

    try {
      window.$toast.add({
        severity: 'info',
        summary: 'Uploading avatar',
        group: 'headless',
      })

      uploadAvatarProgress.value = 0

      // 创建FormData对象
      const formData = new FormData()
      formData.append('upload_files[]', file)

      const response = await userApi.uploadFile(formData, {
        onUploadProgress: (progressEvent: AxiosProgressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            uploadAvatarProgress.value = percentCompleted
          }
        },
      })

      uploadAvatarProgress.value = 100

      if (response && response.data && response.data.file_path && response.data.file_path.length > 0) {
        const url = response.data.file_path[0]

        await userStore.updateUserInfo({
          email: userStore.user?.email || '',
          mfa_check: userStore.user?.mfa_check || 0,
          avatar: url,
        })

        window.$toast.add({
          severity: 'success',
          summary: 'Avatar uploaded successfully',
        })

        userStore.getUserInfo()
      }

      window.$toast.removeGroup('headless')
    }
    catch (error) {
      window.$toast.removeGroup('headless')
      window.$toast.add({
        severity: 'error',
        summary: 'Avatar upload failed',
        detail: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  fileInput.click()
}

const toggleSidebar = () => {
  layoutStore.setSidebarMode(isSidebarSlim.value ? 'expanded' : 'slim')
}

const toggleMobileMenu = () => {
  appStore.toggleMobileMenu()
}

const handleNotification = (is_read: number | string, event?: any) => {
  notice_is_read.value = is_read
  !!event && noticePopoverRef.value.toggle(event)
  noticeLoading.value = true
  getNotifyList({
    page: 1,
    page_size: 8,
    is_read: is_read === 'ALL' ? undefined : is_read as number,
  }).then((resp: { data: { data: any[] } }) => {
    noticePopoverList.value = resp?.data?.data
  }).finally(() => { noticeLoading.value = false })
}

const handleViewAllNotification = (event: any) => {
  router.push('/notification')
  noticePopoverRef.value.toggle(event)
}
</script>

<template>
  <div class="app-header">
    <Toast position="top-center" group="headless" @close="uploadAvatarVisible = false">
      <template #container="{ message, closeCallback }">
        <section class="flex flex-col p-4 gap-4 w-full rounded-xl text-white" style="background-color: rgba(0,0,0,.5);">
          <div class="flex items-center gap-5">
            <i class="pi pi-cloud-upload text-white dark:text-black text-2xl" />
            <span class="font-bold text-base text-white dark:text-black">{{ message.summary }}</span>
          </div>
          <div class="flex flex-col gap-2">
            <ProgressBar
              :value="uploadAvatarProgress" :show-value="false" :style="{ height: '4px' }"
              pt:value:class="!bg-primary-50 dark:!bg-primary-900" class="!bg-primary/80"
            />
            <label class="text-sm font-bold text-white dark:text-black">{{ uploadAvatarProgress }}% uploaded</label>
          </div>
          <div class="flex gap-4 mb-4 justify-end">
            <Button label="Close" size="small" @click="closeCallback" />
          </div>
        </section>
      </template>
    </Toast>

    <div class="header-start">
      <Button class="mobile-menu-toggle" severity="secondary" @click="toggleMobileMenu">
        <i class="pi pi-bars" />
      </Button>
      <Button class="sidebar-toggle" severity="secondary" @click="toggleSidebar">
        <i class="pi" :class="isSidebarSlim ? 'pi-angle-right' : 'pi-angle-left'" />
      </Button>
      <Divider layout="vertical" />
      <Breadcrumbs />
    </div>
    <div class="header-end">
      <!-- <ThemeToggle /> -->
      <BasePopover trigger="click" placement="bottom-end" popper-class="user-setting-popper" width="340px">
        <template #reference="{ isOpen }">
          <div class="user-info">
            <div class="user-nickname">
              {{ `${user?.first_name} ${user?.last_name}` }}
            </div>
            <div class="flex items-center justify-center transition-transform duration-300" :class="{ 'rotate-180': isOpen }">
              <img style="width: 24px;" src="@/assets/flexirates/profile-header-dropdown.png" alt="avatar">
            </div>
            <div class="user-profile">
              <Avatar :image="avatarUrl" shape="circle" class="user-avatar" />
            </div>
          </div>
        </template>
        <div class="user-setting-popover">
          <div class="user-setting-content">
            <div class="user-setting-avatar">
              <Avatar :image="avatarUrl" shape="circle" class="user-avatar" />
              <div class="user-profile-info" @click="uploadAvatar">
                Add Photo
              </div>
            </div>
            <div class="user-setting-profile">
              <div class="user-setting-profile">
                <div class="user-setting-profile-name">
                  {{ user?.first_name }} {{ user?.last_name }}
                </div>
                <div class="user-setting-profile-email">
                  {{ user?.email }}
                </div>
                <!-- edit profile -->
                <div class="user-setting-profile-edit">
                  <i class="pi pi-pencil" />
                  <span @click="$router.push({ name: 'profile' })">Edit Profile</span>
                </div>
              </div>
            </div>
          </div>
          <Button label="LOGOUT" severity="warn" class="user-setting-logout" @click="userStore.logout()" />
        </div>
      </BasePopover>
      <!-- notification -->
      <Popover ref="noticePopoverRef">
        <div class="notice-pop">
          <template v-if="noticeLoading">
            <BaseEmpty :loading="noticeLoading" message="No more Information" />
          </template>
          <template v-else-if="!noticeLoading">
            <div v-if="unreadCount" class="notice-pop-btn">
              <Button
                :severity="notice_is_read === 1 ? 'warn' : undefined"
                :variant="notice_is_read !== 1 ? 'outlined' : undefined" label="Unread"
                @click="() => { handleNotification(1) }"
              />
              <Button
                :severity="notice_is_read === 'ALL' ? 'warn' : undefined"
                :variant="notice_is_read !== 'ALL' ? 'outlined' : undefined" label="All"
                @click="() => { handleNotification('ALL') }"
              />
            </div>
            <div class="notice-content">
              <template v-if="!!noticePopoverList?.length">
                <div v-for="notice in noticePopoverList" :key="notice?.id" class="notice-pop-item">
                  <div class="notice-title">
                    <div class="title-wrapper">
                      <span v-if="notice.is_read === 0" class="spot" />
                      <span class="title" :class="notice.is_fail === 1 ? 'fail' : ''" :title="notice?.title || 'Title'">{{ notice?.title || 'Title'
                      }}</span>
                    </div>
                    <span class="time">{{ notice?.created_at }}</span>
                  </div>
                  <div class="desc">
                    {{ notice?.abstract }}
                  </div>
                </div>
              </template>
              <BaseEmpty v-else-if="!noticePopoverList?.length" message="No more Information" />
            </div>
          </template>
          <Button
            label="View All Notifications >" variant="link" severity="warn" class="notice-pop-more"
            @click="($event) => { handleViewAllNotification($event) }"
          />
        </div>
      </Popover>
      <div class="user-notice">
        <div class="user-notice">
          <OverlayBadge v-if="!!unreadCount" :value="unreadCount > 99 ? '99+' : unreadCount" severity="danger" size="small" @click="(e: any) => { handleNotification(1, e) }">
            <img class="notice-image" :src="noticeImage" alt="notice">
          </OverlayBadge>
          <img v-else-if="!unreadCount" class="notice-image" :src="noticeImage" alt="notice" @click="(e) => { handleNotification('ALL', e) }">
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.mobile-menu-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-setting-popper.base-popover {
  border-radius: 14px;
  background-color: #F5F5FF !important;
  padding: 1.5rem 2rem;

  .base-popover-arrow {
    background-color: #F5F5FF !important;
  }

  .dark & {
    background-color: #1f2937 !important;

    .base-popover-arrow {
      background-color: #1f2937 !important;
    }
  }

  .user-setting-popover {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .user-setting-content {
    color: var(--color-gray-500);
    width: 200px;
    display: flex;
    gap: 1.5rem;

    .user-setting-avatar {
      .user-avatar {
        width: 70px;
        height: 70px;
      }
    }

    .user-profile-info {
      text-align: center;
      color: #000;
      text-decoration: underline;
      cursor: pointer;

      .dark & {
        color: #f9fafb;
      }
    }

    .user-setting-profile {
      flex: 1;

      .user-setting-profile-name {
        font-size: 18px;
        font-weight: 600;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .user-setting-profile-email {
        font-size: 14px;
        font-weight: 400;
      }

      .user-setting-profile-edit {
        width: max-content;
        margin-top: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
        cursor: pointer;
        color: var(--color-gray-500);

        span {
          margin-top: 4px;
          text-decoration: underline;
        }
      }
    }
  }

  .user-setting-logout {
    flex: 1;
    width: 100%;
    line-height: 24px;
    font-size: 16px;
  }
}
.notice-pop {
  max-height: 60vh;
  overflow: hidden;
  padding: 10px;
  width: 480px;

  .notice-content {
    max-height: calc(60vh - 100px);
    overflow-y: auto;

    /* 设置整个滚动条的样式 */
    &::-webkit-scrollbar {
      width: 4px;
      /* 滚动条的宽度 */
      height: 4px;
      /* 滚动条的高度 */
    }

    /* 滚动条轨道 */
    &::-webkit-scrollbar-track {
      background: transparent;
      /* 轨道背景色 */
      border-radius: 10px;
      /* 圆角 */
    }

    /* 滚动条滑块 */
    &::-webkit-scrollbar-thumb {
      background: #f1f1f1;
      /* 滑块颜色 */
      border-radius: 10px;
      /* 圆角 */
    }

    /* 当鼠标悬停在滑块上 */
    &::-webkit-scrollbar-thumb:hover {
      background: #555;
      /* 悬停效果颜色 */
    }
  }

  .notice-pop-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 22px;

    >button {
      width: 178px;
    }
  }

  .notice-pop-item {
    border-bottom: 1px solid #000000;
    padding: 18px 0;
    margin-bottom: 4px;

    .notice-title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-wrapper {
        display: flex;
        align-items: center;
        flex: 1;

        .spot {
          display: inline-block;
          width: 10px;
          height: 10px;
          flex-shrink: 0;
          border-radius: 50%;
          margin-right: 5px;
          background: var(--color-orange-500);
          overflow: hidden;
        }

        .title {
          cursor: default;
          max-width: 300px;
          display: inline-block;
          font-weight: 600;
          font-size: 1.125rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          border-bottom: 0.0625rem solid transparent;
          margin-bottom: - 0.0625rem;
          transition: border-bottom 0.2s ease;

          &.fail {
            font-family: Jost;
            font-weight: 600;
            font-style: SemiBold;
            font-size: 1.125rem;
            line-height: 100%;
            letter-spacing: 0%;
            color: #EB001B;

            &:hover {
              border-bottom: 0.0625rem solid #EB001B;
            }
          }

          &:hover {
            border-bottom: 0.0625rem solid #545454;
          }

          &.fail {
            color: #EB001B;
          }
        }
      }

      .time {
        width: 150px;
        flex-shrink: 0;
      }
    }

    .desc {
      color: #545454;
      font-family: Jost;
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-top: 6px;

    }
  }

  .notice-pop-more {
    color: var(--p-button-warn-background);
    width: 100%;
  }

}
</style>
