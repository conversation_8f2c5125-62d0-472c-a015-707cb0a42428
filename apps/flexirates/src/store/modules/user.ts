import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import router from '@/router'
import { user as userApi } from '@/services/flexirates/index'
import { handleKeepAlive, setRouter } from '@/utils/router'

export const useUserStore = defineStore('user', () => {
  const token = ref<string | null>(null)
  const refresh_token = ref<string | null>(null)
  const user = ref<FlexiratesUser.Info | null>(null)
  const rememberMe = ref(false)
  const userMenu = ref<Menu.Item[]>([])
  const isNeed2FA = ref(true)
  // token 过期时间
  const expiresAt = ref<number | null>(null)

  const registerAccountInfo = ref<Api.FlexiratesGetPropertyListRes | null>(null)

  const setToken = (newToken: string) => {
    token.value = newToken
  }

  // actions
  const login = async (dto: Api.FlexiratesUserLoginReq) => {
    try {
      const { data, code } = await userApi.login(dto)
      if (code === 1) {
        throw new Error('Invalid credentials')
      }
      const { access_token, refresh_token: r_token, expires_in = 3600 } = data
      expiresAt.value = Date.now() + expires_in * 1000
      token.value = access_token
      refresh_token.value = r_token
      rememberMe.value = dto.rememberMe
      await getUserInfo()
      await getMenus()
    }
    catch {
      throw new Error('Invalid credentials')
    }
  }

  const register = async (data: Api.FlexiratesUserRegisterReq): Promise<boolean> => {
    const { code, data: resData } = await userApi.register(data)
    token.value = resData.access_token
    refresh_token.value = resData.refresh_token
    rememberMe.value = false
    await getUserInfo()
    await getMenus()
    return code === 0
  }

  const forgotPassword = async (email: string) => {
    console.log('email', email)
    try {
      // await userApi.forgotPassword({ email })
    }
    catch {
      throw new Error('Failed to send password reset email')
    }
  }

  const logout = () => {
    userApi.logout()
    setTimeout(() => {
      setToken('')
      router.replace('/login')
      user.value = null
      rememberMe.value = false
      location.reload()
    }, 300)
  }

  const initializeFromStorage = () => {
    return Promise.all([
      getUserInfo(),
      getMenus(),
    ])
  }

  // 转换 menu 菜单
  const transformMenu = (menu: Api.RouterItem): Menu.Item => {
    return {
      path: menu.path,
      name: menu.name,
      redirect: menu.redirect as string || '',
      children: menu.children?.map(transformMenu) || [],
      meta: {
        isSeparator: menu?.isSeparator,
        breadcrumbTitle: menu?.breadcrumbTitle,
        isHideBreadcrumb: menu?.isHideBreadcrumb,
        keepAlive: menu?.isKeepAlive,
        i18nKey: menu.i18nKey,
        icon: menu.icon,
      },
    }
  }

  // 获取菜单
  const getMenus = () => {
    return new Promise<boolean>((resolve) => {
      const keeps: string[] = []
      // 模拟菜单数据
      const routes: Api.RouterItem[] = [
        {
          i18nKey: 'menu.propertyList',
          path: '/home',
          name: 'propertyList',
          // component: 'views/home/<USER>',
          icon: 'flexirates/menu-icons/home.png',
          isHide: false,
          breadcrumbTitle: 'Property List',
          redirect: {
            name: 'propertyListDashboard',
          },
          children: [
            {
              i18nKey: 'menu.propertyList',
              path: 'list',
              name: 'propertyListDashboard',
              component: 'views/home/<USER>',
              icon: 'flexirates/menu-icons/home.png',
              isHide: true,
              isHideBreadcrumb: true,
            },
            {
              i18nKey: 'menu.propertyDetail',
              path: 'detail/:id',
              name: 'propertyDetail',
              component: 'views/home/<USER>',
              icon: 'flexirates/menu-icons/home.png',
              isHide: true,
            },
          ],
        },
        {
          i18nKey: 'menu.schedules',
          path: '/schedules',
          name: 'schedules',
          icon: 'flexirates/menu-icons/schedule.png',
          isHide: false,
          redirect: {
            name: 'schedulesList',
          },
          children: [
            {
              i18nKey: 'menu.schedulesList',
              path: 'list',
              name: 'schedulesList',
              component: 'views/schedules/list.vue',
              icon: 'flexirates/menu-icons/schedule.png',
              isHide: false,
              isHideBreadcrumb: true,
              isKeepAlive: false,
            },
            {
              i18nKey: 'menu.schedulesDetail',
              path: 'detail/:id',
              name: 'schedulesDetail',
              component: 'views/schedules/details.vue',
              icon: 'flexirates/menu-icons/schedule.png',
              isHide: true,
            },
          ],
        },
        {
          i18nKey: 'menu.transactions',
          path: '/transactions',
          name: 'transactions',
          component: 'views/transactions/index.vue',
          icon: 'flexirates/menu-icons/transactions.png',
          isHide: false,
          isKeepAlive: true,
        },
        {
          i18nKey: 'menu.paymentMethods',
          path: '/paymentMethods',
          name: 'paymentMethods',
          icon: 'flexirates/menu-icons/payment.png',
          isHide: false,
          redirect: {
            name: 'flexiratesPaymentMethodList',
          },
          children: [
            {
              i18nKey: 'menu.paymentMethods',
              path: 'list',
              name: 'flexiratesPaymentMethodList',
              component: 'views/paymentMethods/index.vue',
              icon: 'flexirates/menu-icons/payment.png',
              isHide: true,
              isHideBreadcrumb: true,
              isKeepAlive: true,
            },
            {
              i18nKey: 'menu.flexiratesMerchantPaymentsDetail',
              path: 'cardDetail/:id',
              name: 'paymentMethodsCardDetail',
              component: 'views/paymentMethods/components/cardDetail.vue',
              icon: 'flexirates/menu-icons/payment.png',
              isHide: true,
            },
            {
              i18nKey: 'menu.flexiratesMerchantPaymentsDetail',
              path: 'bankDetail/:id',
              name: 'paymentMethodsBankDetail',
              component: 'views/paymentMethods/components/bankDetail.vue',
              // icon: 'pi pi-ticket',
              isHide: true,
            },
          ],
        },

        {
          i18nKey: 'menu.flexiratesProfile',
          path: '/profile',
          name: 'profile',
          component: 'views/profile/index.vue',
          icon: 'flexirates/menu-icons/profile.png',
          isHide: false,
        },
        {
          i18nKey: 'menu.security',
          path: '/security',
          name: 'security',
          // component: 'views/security/index.vue',
          icon: 'pi pi-cog',
          isHide: false,
          redirect: {
            name: 'securityDashboard',
          },
          children: [
            {
              i18nKey: 'menu.security',
              path: '/security',
              name: 'securityDashboard',
              component: 'views/security/index.vue',
              icon: 'pi pi-lock',
              isHide: true,
              isHideBreadcrumb: true,
            },
            {
              i18nKey: 'menu.security',
              path: '/security/update',
              name: 'securityUpdatePassword',
              component: 'views/security/updatePassword.vue',
              icon: 'pi pi-lock',
              breadcrumbTitle: 'Update Password',
              isHide: true,
            },
          ],
        },
        // {
        //   i18nKey: 'menu.security',
        //   path: '/security/update',
        //   name: 'securityUpdatePassword',
        //   component: 'views/security/updatePassword.vue',
        //   icon: 'pi pi-lock',
        //   isHide: true,
        // },
        {
          i18nKey: 'menu.flexiratesNotification',
          path: '/notification',
          name: 'notification',
          component: 'views/notifications/list.vue',
          icon: 'merchant/menu-icons/notification.png',
          isHide: false,
          // redirect: {
          //   name: 'list',
          // },
          // children: [
          //   {
          //     i18nKey: 'menu.flexiratesNotificationList',
          //     path: 'list',
          //     name: 'notificationList',
          //     component: 'views/notifications/list.vue',
          //     icon: 'pi-list',
          //     isHide: false,
          //     isHideBreadcrumb: false,
          //   },
          //   {
          //     i18nKey: 'menu.flexiratesNotificationSetting',
          //     path: 'setting',
          //     name: 'notificationSetting',
          //     component: 'views/notifications/setting.vue',
          //     icon: 'pi-cog',
          //     isHide: true,
          //     isHideBreadcrumb: false,
          //   },
          // ],
        },
        {
          i18nKey: 'menu.support',
          path: '/support',
          name: 'support',
          // component: 'views/support/index.vue',
          icon: 'merchant/menu-icons/question.png',
          isHide: false,
          redirect: {
            name: 'supportDashboard',
          },
          children: [
            {
              path: '/support',
              i18nKey: 'menu.support',
              icon: 'pi pi-plus',
              name: 'supportDashboard',
              component: 'views/support/index.vue',
              isHide: true,
              isHideBreadcrumb: true,
            },
            {
              path: 'cancellation',
              i18nKey: 'menu.support',
              icon: 'pi pi-plus',
              name: 'supportCancellation',
              component: 'views/support/cancellation.vue',
              breadcrumbTitle: 'Cancellation',
              isHide: true,
            },
          ],
        },
        // {
        //   i18nKey: 'menu.support',
        //   path: '/support/cancellation',
        //   name: 'supportCancellation',
        //   component: 'views/support/cancellation.vue',
        //   icon: 'pi pi-question-circle',
        //   isHide: true,
        // },

        // {
        //   i18nKey: 'menu.ratePayers',
        //   path: '/ratePayers',
        //   name: 'ratePayers',
        //   icon: 'pi pi-thumbtack',
        //   isHide: false,
        //   redirect: {
        //     name: 'ratePayersList',
        //   },
        //   children: [
        //     {
        //       path: 'create',
        //       i18nKey: 'menu.ratePayersCreate',
        //       // icon: 'pi pi-plus',
        //       name: 'ratePayersCreate',
        //       component: 'views/ratePayers/create.vue',
        //       isHide: false,
        //     },
        //     {
        //       path: 'list',
        //       i18nKey: 'menu.ratePayersList',
        //       name: 'ratePayersList',
        //       component: 'views/ratePayers/list.vue',
        //       isHide: false,
        //       isKeepAlive: true,
        //       isHideBreadcrumb: true,
        //     },
        //   ],
        // },
        // {
        //   i18nKey: 'menu.properties',
        //   path: '/properties',
        //   name: 'properties',
        //   component: 'views/properties/index.vue',
        //   icon: 'pi pi-warehouse',
        //   isHide: false,
        //   redirect: {
        //     name: 'ratePayersList',
        //   },
        //   children: [
        //     {
        //       path: 'list',
        //       i18nKey: 'menu.ratePayersList',
        //       name: 'ratePayersList',
        //       component: 'views/ratePayers/list.vue',
        //       isHide: false,
        //       isKeepAlive: true,
        //       isHideBreadcrumb: true,
        //     },
        //   ],
        // },
        // {
        //   i18nKey: 'menu.payments',
        //   path: '/payments',
        //   name: 'payments',
        //   component: 'views/payments/index.vue',
        //   icon: 'pi pi-wallet',
        //   isHide: false,
        // },
        // {
        //   i18nKey: 'menu.reports',
        //   path: '/reports',
        //   name: 'reports',
        //   component: 'views/reports/index.vue',
        //   icon: 'pi pi-calendar-minus',
        //   isHide: false,
        // },
        // {
        //   i18nKey: 'menu.settings',
        //   path: '/settings',
        //   name: 'settings',
        //   component: 'views/settings/index.vue',
        //   icon: 'pi pi-sliders-v',
        //   isHide: false,
        // },

      ] as Api.RouterItem[]

      // 递归过滤和转换菜单
      const filterAndTransformMenu = (items: Api.RouterItem[]): Menu.Item[] => {
        return items
          .filter((item) => {
            if (item?.isKeepAlive) {
              keeps.push(item.name)
            }
            return !item.isHide
          })
          .map((item) => {
            const menuItem = transformMenu(item)
            if (item.children && item.children.length > 0) {
              menuItem.children = filterAndTransformMenu(item.children)
            }
            return menuItem
          })
      }

      // 设置路由
      setRouter(routes)
      // 设置菜单
      userMenu.value = filterAndTransformMenu(routes)
      // 设置 keepAlive 名称
      handleKeepAlive(keeps)
      resolve(true)
    })
  }

  const getUserInfo = async (): Promise<FlexiratesUser.Info> => {
    const data = await userApi.getUserInfo()
    isNeed2FA.value = data.code === 403
    user.value = data.data
    return user.value
  }

  const updateUserInfo = async (updateData: FlexiratesUser.UserInfoUpdateReq) => {
    const { data, code } = await userApi.updateUserInfo(updateData)
    if (code === 0) {
      user.value = data
    }
    return user.value
  }

  const getRegisterAccountInfo = async (data: Api.RegisterAccountInfoReq) => {
    const { data: res, code } = await userApi.getRegisterAccountInfo(data)
    return { data: res, code }
  }

  const setRegisterAccountInfo = (data: Api.FlexiratesGetPropertyListRes) => {
    registerAccountInfo.value = data
  }

  // getters
  const isLoggedIn = computed(() => !!token.value)
  const currentUsername = computed(() => user.value?.name)

  return {
    // state
    token,
    user,
    rememberMe,
    userMenu,
    isNeed2FA,
    expiresAt,
    setToken,
    registerAccountInfo,

    // actions
    login,
    register,
    forgotPassword,
    logout,
    initializeFromStorage,
    getMenus,
    getUserInfo,
    getRegisterAccountInfo,
    updateUserInfo,

    setRegisterAccountInfo,

    // getters
    isLoggedIn,
    currentUsername,
  }
}, {
  persist: {
    key: 'user',
    omit: ['userMenu', 'isLogin', 'user', 'isNeed2FA'],
  },
})
