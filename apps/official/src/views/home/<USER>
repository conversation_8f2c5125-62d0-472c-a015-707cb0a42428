<script setup lang="ts">
import type { Swiper as SwiperType } from 'swiper'
import { getAssetsFiles } from '@/utils/getAssetsFile'
import { Autoplay, Navigation, Pagination } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// No additional logic needed

import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/autoplay'

// 添加 swiper 实例引用
const testimonialSwiper = ref<SwiperType | null>(null)
const prevEl = ref(null)
const nextEl = ref(null)

const router = useRouter()

const reviewList = [
  {
    id: 1,
    comment: 'It is so easy to use! I have complete control of my clients scheduled online.',
    author: 'Karin<PERSON>',
    occupation: 'Fitness Industry',
  },
  {
    id: 2,
    comment: 'The payments go in easy with no troubles, the fees are low and everything is smooth running. ',
    author: 'Giselle',
    occupation: 'Freelancer',
  },
  {
    id: 3,
    comment: 'The whole process is so seamless and their product offers multiple payment options. I love their reporting which gives me great insights!',
    author: '<PERSON>',
    occupation: 'Retail Industry',
  },
]

// 支付方式数据
const paymentMethods = [
  {
    id: 1,
    title: 'Direct Debit',
    description: 'Get paid on time! With your customer\'s authority, funds are debited from their account when scheduled.',
    icon: getAssetsFiles('official/products/DIRECT DEBIT/dd-icon.png'),
    iconStyle: 'width: 20px;height:20px;',
    link: '/DirectDebit',
  },
  {
    id: 2,
    title: 'BPAY',
    description: 'Customers use their financial institution\'s Internet or Phone banking to make payments at any time from their cheque, savings, or credit card account.',
    icon: getAssetsFiles('official/products/BPAY/BPay.png'),
    iconStyle: 'width: 22px;height:22px;',
    link: '/BPay',
  },
  {
    id: 3,
    title: 'WebPay',
    description: 'Customers can make their payments on a secure website using their credit card and a validated customer reference number 24 hours per day.',
    icon: getAssetsFiles('official/products/WEBPAY/WebPay Icon.png'),
    iconStyle: 'width: 25px;height:25px;',
    link: '/WebPay',
  },
  {
    id: 4,
    title: 'PayMyInvoice',
    description: 'Simplify invoice payments with a secure link sent via email or SMS,offering a seamless oline experience withe multiple payment options available 24/7.',
    icon: getAssetsFiles('official/products/PAYMYINVOICE/PayMyInvoice Icon.png'),
    iconStyle: 'width: 25px;height:25px;',
    link: '/PayMyInvoice',
  },
]

const paymentSwiperModules = [Pagination, Autoplay]

const swiperModules = [Navigation, Pagination, Autoplay]

const jumpToPage = (link: string) => {
  router.push({ path: link })
}
</script>

<template>
  <div class="home">
    <!-- Hero Section -->
    <section class="hero">
      <div class="left" />
      <div class="right" />
      <div class="hero-content">
        <h1>
          <span class="text-primary">Unified</span> Payments,<br>
          <span class="text-secondary">Secured</span> Transactions,<br>
          <span class="text-accent">Simplified</span> Experience.
        </h1>
        <div class="hero-buttons">
          <router-link to="/biller-registration" class="btn btn-primary">
            BILLER REGISTRATION
          </router-link>
          <router-link to="/Contact-Us" class="btn btn-secondary">
            BOOK A CALL
          </router-link>
        </div>
      </div>
    </section>

    <!-- Payment Options Section -->
    <section class="payment-options">
      <div class="container flex ">
        <h2>Flexible Payment Options<span class="accent-dot">.</span></h2>
        <div class="flex flex-col payment-logos-container">
          <div class="payment-logos">
            <div class="flex justify-center">
              <img src="@/assets/official/home/<USER>/visa.png" alt="Visa">
            </div>
            <div class="flex justify-center">
              <img src="@/assets/official/home/<USER>/mastercard.png" alt="Mastercard">
            </div>
            <div class="flex justify-center">
              <img src="@/assets/official/home/<USER>/amex.png" alt="American Express">
            </div>
            <div class="flex justify-center">
              <img src="@/assets/official/home/<USER>/bpay.png" alt="BPay">
            </div>
            <div class="flex justify-center">
              <img src="@/assets/official/home/<USER>/direct-debit.png" alt="Direct Debit">
            </div>
            <div class="flex justify-center">
              <img src="@/assets/official/home/<USER>/xero.png" alt="Xero">
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Solutions Section -->
    <section class="solutions">
      <div class="container">
        <h2>Solutions<span class="accent-dot">.</span></h2>
        <div class="solutions-grid">
          <!-- <div class="solution-card">
          <p>Check what the "Bill Buddy" transaction on your credit card statement is for.</p>
          <div class="solution-card-btn">
            <h3>Transaction Enquiry</h3>
            <router-link to="/transaction-enquiry" class="btn btn-check solutions-btn">
              CHECK
            </router-link>
          </div>
        </div> -->
          <div class="solution-card">
            <p>Apply online for registration as a Bill Buddy Biller.</p>
            <div class="solution-card-btn">
              <h3>Biller Registration</h3>
              <router-link to="/biller-registration" class="btn btn-register solutions-btn">
                REGISTER
              </router-link>
            </div>
          </div>
          <div class="solution-card">
            <p>Provide your authority for us to debit your account at the direction of your Biller.</p>
            <div class="solution-card-btn">
              <h3>Online DDR Authority</h3>
              <a href="https://site.billbuddy.com/index/online-ddr-authority" class="btn btn-authorize solutions-btn">
                AUTHORISE
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Company Section -->
    <section class="company relative">
      <div class="container">
        <div class="company-content">
          <div class="company-text">
            <h2>Our Company<span class="accent-dot">.</span></h2>
            <p>
              Since 2003, Bill Buddy has been simplifying payments for businesses with secure,
              automated solutions, including direct debit, BPAY, and invoicing tools. Whether
              you're managing subscriptions or invoices, our platform makes transactions
              effortless—all you need is an ABN and an Australian bank account to get started.
            </p>
            <div class="flex justify-end">
              <router-link to="/About-Us" class="read-more">
                Read more <i class="pi pi-chevron-right" style="color: #fe4c1c;margin-left: 2rem;" />
              </router-link>
            </div>
          </div>
        </div>
      </div>
      <div class="company-image">
        <img src="@/assets/official/ourCompany.png" alt="our company" style="object-fit: contain;">
      </div>
    </section>

    <!-- Quick Info Section -->
    <section class="quick-info">
      <div class="container">
        <div class="quick-info-wrap">
          <div class="mb-10">
            <p class="quick-info-header">
              How can Bill Buddy help you?
            </p>
            <div class="quick-info-title-wrapper">
              <div class="quick-info-title">
                <h2>
                  IN A RUSH<span style="color: #181349;">?</span>
                  <br>READ THIS<span class="text-[#181349]">.</span>
                </h2>
              </div>
              <div class="quick-info-description">
                <p>
                  <span class="font-semibold">Tired of manual billing?</span>
                  Bill Buddy automates payments,from subscriptions to invoices,integrating seamlessly with your existing
                  systems.
                </p>
              </div>
            </div>
          </div>
          <div class="quick-info-content">
            <Swiper
              :modules="paymentSwiperModules" :slides-per-view="1" :space-between="20"
              :pagination="{ clickable: true }" :autoplay="{ delay: 3000, disableOnInteraction: false }" :breakpoints="{
                576: {
                  slidesPerView: 1,
                  spaceBetween: 20,
                },
                768: {
                  slidesPerView: 2,
                  spaceBetween: 20,
                },
                992: {
                  slidesPerView: 3,
                  spaceBetween: 20,
                },
                1200: {
                  slidesPerView: 4,
                  spaceBetween: 20,
                },
              }" class="payment-methods-swiper"
            >
              <SwiperSlide v-for="method in paymentMethods" :key="method.id" class="payment-method-slide">
                <div class="payment-method" @click="jumpToPage(method.link)">
                  <div class="flex justify-between items-center mb-3">
                    <div class="flex items-center">
                      <img :src="method.icon" :alt="method.title" :style="method.iconStyle">
                      <h3 class="ml-2" style="line-height: 1.5;">
                        {{ method.title }}
                      </h3>
                    </div>
                    <router-link :to="method.link" class="method-link">
                      <i class="pi pi-chevron-right" style="font-size: 20px;" />
                    </router-link>
                  </div>
                  <p>
                    {{ method.description }}
                  </p>
                </div>
              </SwiperSlide>
            </Swiper>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
      <div class="container">
        <div class="cta-content">
          <div>
            <h2>TALK TO A BIZ DEV MANAGER<span class="accent-dot">.</span></h2>
            <p>
              Have questions? Get expert guidance, talk to a payment expert, and see how Bill Buddy can help your
              business.
            </p>
          </div>
          <div class="cta-buttons flex ">
            <router-link
              to="/biller-registration" class="talk-to-us-button register"
            >
              BILLER REGISTRATION
            </router-link>
            <router-link to="/Contact-Us" class="talk-to-us-button request-call">
              REQUEST A CALL
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials relative">
      <div class="container">
        <div class="testimonials-wrap">
          <h2>
            Chosen by Businesses<span class="accent-dot">, </span>Loved by Clients<span class="accent-dot">.</span>
          </h2>
          <div class="testimonial-slider relative">
            <!-- 添加自定义导航箭头 -->
            <div ref="prevEl" class="testimonial-nav-prev">
              <i class="pi pi-chevron-left" />
            </div>
            <div ref="nextEl" class="testimonial-nav-next">
              <i class="pi pi-chevron-right" />
            </div>

            <Swiper
              ref="testimonialSwiper" :modules="swiperModules" :slides-per-view="1" :space-between="25"
              :navigation="{ prevEl, nextEl }" :pagination="{ clickable: true }"
              :autoplay="{ delay: 5000, disableOnInteraction: false }" :breakpoints="{
                576: {
                  slidesPerView: 1,
                  spaceBetween: 8,
                },
                768: {
                  slidesPerView: 2,
                  spaceBetween: 16,
                },
              }" class="testimonial-swiper"
            >
              <SwiperSlide v-for="review in reviewList" :key="review.id" class="testimonial-slide">
                <div class="testimonial">
                  <div>
                    <img src="@/assets/official/Testimonial Icon.png" style="width: 65px;" alt="">
                  </div>
                  <div class="quote">
                    {{ review.comment }}
                  </div>
                  <div class="author">
                    <div class="author-name">
                      {{ review.author }}
                    </div>
                    <div class="author-title">
                      {{ review.occupation }}
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            </Swiper>
          </div>
        </div>
      </div>
      <div class="chosen-image">
        <img src="@/assets/official/portal/Homepage Image 2.png" alt="chosen by business">
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.hero {
  background-color: #18144b;
  color: white;
  padding: 6rem 2rem;
  text-align: center;
  position: relative;
  height: 550px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  @include media-breakpoint-down(lg) {
    padding: 4rem 1.5rem;
    height: 500px;
  }

  @include media-breakpoint-down(md) {
    padding: 3rem 1rem;
    height: 450px;
  }

  @include media-breakpoint-down(sm) {
    padding: 2rem 1rem;
    height: auto;
    min-height: 400px;
  }
}

.hero-content {
  max-width: 800px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 90%;

  @include media-breakpoint-down(md) {
    width: 95%;
  }
}

.right::after {
  content: '';
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  /* opacity: 0.21; */
  background-image: url('@/assets/official/bg/ultra_HOMEPAGE BG.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-blend-mode: multiply;
}

.hero h1 {
  font-size: 4.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2rem;

  @include media-breakpoint-down(xl) {
    font-size: 4rem;
  }

  @include media-breakpoint-down(lg) {
    font-size: 3.5rem;
  }

  @include media-breakpoint-down(md) {
    font-size: 2.8rem;
    margin-bottom: 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 2.2rem;
    margin-bottom: 1.2rem;
  }
}

.text-primary {
  color: #00c8e5;
}

.text-secondary {
  color: #e1ffa9;
}

.text-accent {
  color: #ff5722;
}

.accent-dot {
  color: #ff5722;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 16px;

  @include media-breakpoint-down(md) {
    padding: 0.6rem 1.25rem;
    font-size: 14px;
  }

  &.btn-register {
    background-color:#f5f5ff;
    font-weight: 700;
    margin-right: 2rem;
    color: #181349;
    &:hover {
      background-color: hsl(16, 100%, 70%);
    }
    &:active {
      background-color: hsl(16, 100%, 60%);
    }
  }

  &.btn-request-call {
    background-color: #fe4c1c;
    font-weight: 700;
    color: white;
    &:hover {
      background-color: hsl(16, 100%, 70%);
    }
    &:active {
      background-color: hsl(16, 100%, 60%);
    }
  }
}

.btn-primary {
  background-color: #ffe3e8;
  color: #0a1a2a;
  font-weight: 600;
  width: 240px;
  height: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;

  @include media-breakpoint-down(md) {
    width: 220px;
    height: 3rem;
  }

  @include media-breakpoint-down(sm) {
    width: 200px;
    height: 2.75rem;
  }
}

.btn-primary:hover {
  background-color: #f0f0f0;
}

.btn-secondary {
  background-color: #e1ffa9;
  color: #0a1a2a;
  font-weight: 600;
  width: 220px;
  height: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;

  @include media-breakpoint-down(md) {
    width: 200px;
    height: 3rem;
  }

  @include media-breakpoint-down(sm) {
    width: 200px;
    height: 2.75rem;
  }
}

.btn-secondary:hover {
  background-color: #7cb342;
}

.payment-options {
  padding: 6rem 0;

  @include media-breakpoint-down(lg) {
    padding: 5rem 0;
  }

  @include media-breakpoint-down(md) {
    padding: 4rem 0;
  }

  @include media-breakpoint-down(sm) {
    padding: 3rem 0;
  }

  .container {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @include media-breakpoint-down(lg) {
      padding: 4rem 1.5rem;
    }

    @include media-breakpoint-down(md) {
      padding: 2rem 1rem;
      flex-direction: column;
    }

    @include media-breakpoint-down(sm) {
      padding: 1.5rem 1rem;
    }
  }

}

.payment-options h2 {
  font-size: 4rem;
  color: #181349;
  margin-bottom: 2rem;
  font-weight: 700;

  @include media-breakpoint-down(lg) {
    font-size: 3.5rem;
    margin-bottom: 2rem;
  }

  @include media-breakpoint-down(md) {
    font-size: 3rem;
    margin-bottom: 3rem;
    width: 100%;
    text-align: center;
  }

  @include media-breakpoint-down(sm) {
    font-size: 2.5rem;
    margin-bottom: 3rem;
  }
}

.payment-logos {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 2rem;
  margin-bottom: 2rem;

  @include media-breakpoint-down(lg) {
    gap: 1.5rem;
  }

  @include media-breakpoint-down(md) {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 1.25rem;
  }

  @include media-breakpoint-down(sm) {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 1rem;
  }
}

.payment-logos img {
  height: 80px;
  width: auto;

  @include media-breakpoint-down(lg) {
    height: 70px;
  }

  @include media-breakpoint-down(md) {
    height: 60px;
  }

  @include media-breakpoint-down(sm) {
    height: 50px;
  }
}

@media(prefers-color-scheme: dark) {
  .payment-options {
    background-color: #f5f5ff;
  }
}

.solutions {
  background-color: #181349;
  padding: 2rem 2rem;

  @include media-breakpoint-down(md) {
    padding: 2rem 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    padding: 1.5rem 1rem;
  }
}

.solutions h2 {
  text-align: center;
  font-size: 52px;
  font-weight: 700;
  color: #e1ffa9;
  margin-bottom: 2rem;
  margin-top: 0;

  @include media-breakpoint-down(lg) {
    font-size: 46px;
  }

  @include media-breakpoint-down(md) {
    font-size: 40px;
    margin-bottom: 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 32px;
    margin-bottom: 1.25rem;
  }
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;

  @include media-breakpoint-down(lg) {
    gap: 1.5rem;
  }

  @include media-breakpoint-down(md) {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  @include media-breakpoint-down(sm) {
    gap: 1rem;
  }
}

.solution-card {
  background: #ffe3e8;
  padding: 1rem 1.2rem;
  border-radius: 22px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #fe4c1c;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  @include media-breakpoint-down(md) {
    padding: 1rem;
    border-radius: 18px;
  }

  @include media-breakpoint-down(sm) {
    padding: 0.875rem;
    border-radius: 16px;
  }
}

.solution-card h3 {
  font-size: 17px;
  color: #181349;

  @include media-breakpoint-down(md) {
    font-size: 16px;
  }

  @include media-breakpoint-down(sm) {
    font-size: 15px;
  }
}

.solution-card p {
  color: #181349;
  margin-bottom: 1rem;
  text-align: left;
  margin-top: .75rem;
  font-size: 16px;

  @include media-breakpoint-down(md) {
    font-size: 15px;
    margin-top: 0.5rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 14px;
    margin-bottom: 0.75rem;
  }
}

.btn-check,
.btn-register,
.btn-authorize {
  display: inline-block;
  padding: 0.5rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  color: white;
  font-size: 16px;

  @include media-breakpoint-down(md) {
    padding: 0.4rem 1.25rem;
    font-size: 15px;
  }

  @include media-breakpoint-down(sm) {
    padding: 0.3rem 1rem;
    font-size: 14px;
  }
}

.btn-check {
  background-color: #ff9800;
}

.btn-register {
  background-color: #ff5722;

  &:hover {
    background-color: hsl(16, 100%, 70%);
  }

  &:active {
    background-color: hsl(16, 100%, 60%);
  }
}

.btn-authorize {
  background-color: #f44336;

  &:hover {
    background-color: hsl(16, 100%, 70%);
  }

  &:active {
    background-color: hsl(16, 100%, 60%);
  }
}

.solutions-btn {
  background-color: #fe4c1c;
  color: white;
  font-weight: 700;

  &:hover {
    background-color: hsl(16, 100%, 70%);
  }

  &:active {
    background-color: hsl(16, 100%, 60%);
  }
}

.solution-card-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 700;

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;

    h3 {
      margin-bottom: 0.5rem;
    }
  }
}

.company {
  width: 100%;
  height: 52.2857rem;
  background-color: #f5f5ff;

  .container {
    display: flex;
    justify-content: flex-end;
    z-index: 1;
    position: relative;

    .company-content {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }

  @media screen and (max-width: 1600px) {
    height: 608px;
  }

  @include media-breakpoint-down(xxl) {
    height: 558px;
  }

  @include media-breakpoint-down(lg) {
    height: 488px;
    padding: 2rem 0;
  }

  .company-text {
    @include media-breakpoint-down(lg) {
      width: 100%;
      padding-top: 2rem;
    }

    h2 {
      @include media-breakpoint-down(lg) {
        font-size: 3.5rem;
        margin-bottom: 2rem;
      }

      @include media-breakpoint-down(md) {
        font-size: 3rem;
        margin-bottom: 1.5rem;
      }

      @include media-breakpoint-down(sm) {
        font-size: 2.5rem;
        margin-bottom: 1.25rem;
      }
    }
  }

  .company-image {
    @include media-breakpoint-down(lg) {
      height: 600px;
    }

    @include media-breakpoint-down(md) {
      height: 500px;
    }

    @include media-breakpoint-down(sm) {
      height: 400px;
    }

    img {
      @include media-breakpoint-down(lg) {
        height: 600px;
      }

      @include media-breakpoint-down(md) {
        height: 500px;
      }

      @include media-breakpoint-down(sm) {
        height: 400px;
      }
    }
  }
}

.company-image {
  width: 100%;
  height: 668px;
  position: absolute;
  bottom: 0;
}

.company-image img {
  width: 100%;
  height: 100%;
  object-position: bottom;
}

.company-text {
  max-width: 630px;
  padding-top: 5rem;
}

.company-text h2 {
  font-size: 4rem;
  color: #181349;
  font-weight: 800;
  margin-bottom: 2.5rem;
}

.company-text p {
  color: #181349;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 16px;
}

.read-more {
  color: #181349;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  display: inline-block;
  padding: 1rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
}

.read-more:hover {
  color: #181349;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  display: inline-block;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  background-color: #ffe3e8;
}

.company-image {
  flex: 1;
}

.placeholder-image {
  width: 100%;
  height: 300px;
  background-color: #f0f0f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-image i {
  font-size: 4rem;
  color: #ccc;
}

.quick-info {
  padding: 60px 0;
  background-color: #f5f5ff;

  :deep(.swiper-pagination) {
    display: none;
  }

  @include media-breakpoint-down(sm) {
    max-width: 100%;
    padding: 40px 0 0;

    :deep(.swiper-pagination) {
      display: block;
    }
  }

  .quick-info-wrap {
    .quick-info-header {
      font-size: 18px;
      margin-bottom: 10px;

      @media (max-width: 768px) {
        font-size: 16px;
      }
    }

    .quick-info-title-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      @media (max-width: 992px) {
        flex-direction: column;
      }

      .quick-info-title {
        width: 40%;

        @media (max-width: 992px) {
          width: 100%;
          margin-bottom: 20px;
        }

        h2 {
          font-size: 4rem;
          font-weight: 900;
          color: #ff5722;
          line-height: 1.2;

          @media (max-width: 992px) {
            font-size: 32px;
          }

          @media (max-width: 768px) {
            font-size: 28px;
          }

          @media (max-width: 576px) {
            font-size: 24px;
          }
        }
      }

      .quick-info-description {
        width: 60%;

        @media (max-width: 992px) {
          width: 100%;
        }

        p {
          font-size: 18px;
          line-height: 1.6;

          @media (max-width: 768px) {
            font-size: 16px;
          }

          @media (max-width: 576px) {
            font-size: 14px;
          }
        }
      }
    }
  }
}

.payment-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  grid-template-rows: auto;

  @include media-breakpoint-down(lg) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }
}

.payment-method {
  background: #e1ffa9;
  padding: 2rem;
  padding-bottom: 1.5rem;
  border-radius: 22px;
  border: 1px solid rgba(179, 179, 178, 0.8);
  position: relative;
  color: #181349;
  height: 98%;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  &:hover{
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  @include media-breakpoint-down(lg) {
    padding: 1.75rem;
    padding-bottom: 1.25rem;
  }

  @include media-breakpoint-down(md) {
    padding: 1.5rem;
    padding-bottom: 1rem;
    border-radius: 18px;
  }

  @include media-breakpoint-down(sm) {
    padding: 1.25rem;
    border-radius: 16px;
  }

  h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 16px;
    color: #181349;
    font-weight: 700;

    @include media-breakpoint-down(md) {
      font-size: 15px;
    }

    img {
      width: 20px;
      height: 20px;

      @include media-breakpoint-down(sm) {
        width: 18px;
        height: 18px;
      }
    }
  }

  p {
    color: #181349;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 16px;
    flex-grow: 1;

    @include media-breakpoint-down(md) {
      font-size: 15px;
      margin-bottom: 1.5rem;
    }

    @include media-breakpoint-down(sm) {
      font-size: 14px;
      margin-bottom: 1rem;
    }
  }
}

.method-link {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff5722;
  text-decoration: none;

  i {
    font-size: 20px;

    @include media-breakpoint-down(sm) {
      font-size: 18px;
    }
  }
}

.cta {
  background-color: #f5f5ff;
  padding: 4rem 0;
  position: relative;
  z-index: 2;

  @include media-breakpoint-down(lg) {
    padding: 3rem 1.5rem;
  }

  @include media-breakpoint-down(md) {
    padding: 2.5rem 1rem;
  }
}

.cta-content {
  margin: 0 auto;
  background-color: #ffe3e8;
  border-radius: 16px;
  padding: 2.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @include media-breakpoint-down(lg) {
    flex-direction: column;
    text-align: center;
    padding: 2rem;
  }
}

.cta h2 {
  font-size: 2.6rem;
  color: #18134b;
  margin-bottom: 1rem;
  font-weight: 800;

  @include media-breakpoint-down(lg) {
    font-size: 2.2rem;
  }

  @include media-breakpoint-down(md) {
    font-size: 2rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 1.8rem;
  }
}

.cta p {
  color: #18134b;
  margin-bottom: 2rem;
  width: 70%;
  font-style: italic;
  line-height: 1.6;
  font-size: 16px;

  @include media-breakpoint-down(lg) {
    width: 100%;
    margin-bottom: 1.5rem;
  }
}

.cta-buttons {
  @include media-breakpoint-down(md) {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  a {
    height: 50px;
    width: 240px;
    display: flex;
    align-items: center;
    justify-content: center;

    @include media-breakpoint-down(md) {
      width: 100%;
      margin-right: 0 !important;
    }
  }
}

.testimonials {
  padding: 4rem 2rem;
  background-color: #f5f5ff;
  height: 600px;

  @include media-breakpoint-down(lg) {
    height: auto;
    padding: 3rem 1.5rem;
  }

  @include media-breakpoint-down(md) {
    padding: 2.5rem 1rem;
  }
}

.testimonials-wrap {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
  position: relative;
  margin: auto;
  z-index: 2;
}

.chosen-image {
  position: absolute;
  right: -180px;
  bottom: 0;
  overflow: hidden;
  z-index: 1
}

.chosen-image img {
  max-width: 92%;
  height: auto;
  transform: scale(0.6, 0.6) translate(560px, 370px);
}

.testimonials h2 {
  text-align: left;
  font-size: 3.5rem;
  font-weight: 900;
  color: #181349;
  margin-bottom: 3rem;

  @include media-breakpoint-down(lg) {
    font-size: 3rem;
    margin-bottom: 2.5rem;
  }

  @include media-breakpoint-down(md) {
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }
}

.testimonial-slider {
  position: relative;
  max-width: 700px;
  padding: 0 60px;

  :deep(.swiper-pagination) {
    display: none;
  }

  @include media-breakpoint-down(xl) {
    max-width: 100%;
    padding: 0 60px;
  }

  @include media-breakpoint-down(sm) {
    max-width: 100%;
    padding: 0;

    :deep(.swiper-pagination) {
      display: block;
    }
  }
}

.testimonial-nav-prev,
.testimonial-nav-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  cursor: pointer;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  i {
    font-size: 58px;
    color: #fe4c1c;
    transition: all 0.3s ease;

    @include media-breakpoint-down(xl) {
      font-size: 50px;
    }

    @include media-breakpoint-down(lg) {
      font-size: 45px;
    }

    @include media-breakpoint-down(md) {
      font-size: 40px;
    }
  }

  &:not(.swiper-button-disabled):hover i {
    color: hsl(16, 100%, 70%);
  }

  &:not(.swiper-button-disabled):active i {
    color: hsl(16, 100%, 60%);
  }

  @include media-breakpoint-down(md) {
    display: none; // 在小屏幕上隐藏自定义箭头
  }
}

.testimonial-nav-prev {
  left: 0px;
}

.testimonial-nav-next {
  right: 0;
}

.swiper-button-disabled {
  opacity: 0.6;
  cursor: default;
}

:deep(.testimonial-slider .p-button-icon-only.p-button-rounded) {
  height: auto;
}

.quote {
  font-size: 16px;
  color: #181349;
  margin-bottom: 5rem;
  margin-top: 1rem;
}

.author {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
}

.author-name {
  font-weight: 600;
  color: #181349;
  font-size: 16px;
}

.author-title {
  color: #181349;
  font-style: italic;
}

.testimonial-image {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 40%;
  max-width: 500px;
}

.chosen-image {
  @include media-breakpoint-down(lg) {
    display: none;
  }
}

.payment-logos-container {
  @include media-breakpoint-down(md) {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }

  @include media-breakpoint-down(sm) {
    max-width: 400px;
  }
}

/* Swiper 基础样式 */
.payment-methods-swiper,
.testimonial-swiper {
  width: 100%;
  height: 100%;

  :deep(.swiper-button-next),
  :deep(.swiper-button-prev) {
    color: #fe4c1c;
    width: 40px;
    height: 40px;

    &:after {
      font-size: 24px;
      font-weight: bold;
    }

    @media (max-width: 768px) {
      display: none;
    }
  }
}

/* 只为评价轮播添加分页器样式 */
.testimonial-swiper {
  :deep(.swiper-pagination) {
    bottom: 0;

    .swiper-pagination-bullet {
      width: 10px;
      height: 10px;
      background: #ccc;
      opacity: 0.5;

      &-active {
        background: #fe4c1c;
        opacity: 1;
      }
    }
  }
}

/* 媒体查询适配 */
@media (max-width: 992px) {
  .payment-methods-swiper {
    padding: 20px 5px 40px;
  }

  .testimonial-swiper {
    padding: 15px 5px 35px;
  }

  .payment-method,
  .testimonial {
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .payment-methods-swiper {
    padding: 10px 0 40px;
  }

  .testimonial-swiper {
    padding: 10px 0 30px;
  }

  .payment-method,
  .testimonial {
    padding: 12px;

    h3 {
      font-size: 16px;
    }

    p {
      font-size: 14px;
    }
  }
}

.payment-method-slide,
.testimonial-slide {
  height: auto;
  display: flex;
}

.testimonial {
  padding: 1.8rem;
  border-radius: 22px;
  border: 1px solid #09deff;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 4px #0000001a;

  .author {

    &-name {
      font-weight: 700;
      font-size: 18px;
    }

    &-title {
      color: #666;
      font-size: 14px;
    }
  }
}
</style>
