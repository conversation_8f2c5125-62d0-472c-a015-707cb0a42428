<script setup lang="ts">
import divide from '@/components/divide/index.vue'
import GoogleRecaptcha from '@/components/googleRecaptchaV2/index.vue'
import { submit } from '@/services/api/billerRegistration'
import { message } from '@/utils/message'
import { toTypedSchema } from '@vee-validate/zod'
import { Field, Form as VeeForm } from 'vee-validate'
import { nextTick, reactive, ref } from 'vue'
import { z } from 'zod'

const questionnaireSection = ref<HTMLElement | null>(null)
const services = ref({
  directDebit: false,
  bPay: false,
  webPay: false,
})

const scrollToQuestionnaire = async () => {
  if (questionnaireSection.value) {
    await nextTick()
    questionnaireSection.value.scrollIntoView({ behavior: 'smooth' })
  }
}
const initialValues = reactive({
  abn: '',
  business_category: '',
  average_sales_amount_per_order: '',
  percentage_of_orders_prepaid: '',
  estimate_monthly_turnover: '',
  average_of_days_prepays: '',
  proposed_services: [],
  website: '',
  email: '',
  phone: '',
  contact_name: '',
  google_token: '',
  recaptcha: false,
})
// reCAPTCHA refs and state
const recaptchaRef = ref<InstanceType<typeof GoogleRecaptcha> | null>(null)
const recaptchaVerified = ref(false)

// reCAPTCHA handlers
const onRecaptchaVerify = (response: string) => {
  if (response) {
    initialValues.google_token = response
    recaptchaVerified.value = true
  }
}

const onRecaptchaExpired = () => {
  recaptchaVerified.value = false
}

const onRecaptchaError = () => {
  recaptchaVerified.value = false
}
const isLoading = ref(false)
const validationSchema = toTypedSchema(z.object({
  abn: z.string().min(1, { message: 'ABN is required.' }),
  business_category: z.string().min(1, { message: 'Business Industry is required.' }),
  average_sales_amount_per_order: z.string().min(1, { message: 'Average Sales Amount Per Order is required.' }),
  percentage_of_orders_prepaid: z.string().min(1, { message: 'Percentage Of Orders Prepaid is required.' }),
  estimate_monthly_turnover: z.string().min(1, { message: 'Estimate Monthly Turnover is required.' }),
  average_of_days_prepays: z.string().min(1, { message: 'Average of Days Prepays is required.' }),
  website: z.string().min(1, { message: 'Website is required.' }),
  email: z.string().min(1, { message: 'Email is required.' }).email({ message: 'PLEASE ENTER A VALID EMAIL ADDRESS' }),
  phone: z.string().min(1, { message: 'Phone is required.' }).regex(/^\d+(-\d+)*$/, { message: 'PLEASE ENTER VALID PHONE NUMBER' }),
  contact_name: z.string().min(1, { message: 'Contact Name is required.' }),
  // proposed_services: z.array(z.string()).min(1, { message: 'At least one ingredient must be selected.' }),
  // Add recaptcha validation
  google_token: z.string({
    required_error: 'Please complete the reCAPTCHA verification',
  }),
}))
const questionSubmit = async (values: Record<string, any>) => {
  if (!recaptchaVerified.value) {
    initialValues.recaptcha = false
  }

  if (!values.google_token) {
    message({
      message: 'Please complete the CAPTCHA verification.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
    return
  }
  const serviceIndices = []
  for (const key in services.value) {
    if (Object.prototype.hasOwnProperty.call(services.value, key)) {
      console.log(key)

      if (services.value[key as keyof typeof services.value]) {
        switch (key) {
          case 'directDebit':
            serviceIndices.push('0')
            break
          case 'bPay':
            serviceIndices.push('1')
            break
          case 'webPay':
            serviceIndices.push('2')
            break
        }
        break
      }
    }
  }
  if (serviceIndices.length === 0) {
    message({
      message: 'Please select at least one Proposed Service.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
    return
  }

  isLoading.value = true

  try {
    const sendData = {
      abn: initialValues.abn,
      business_category: initialValues.business_category,
      average_sales_amount_per_order: initialValues.average_sales_amount_per_order,
      percentage_of_orders_prepaid: initialValues.percentage_of_orders_prepaid,
      estimate_monthly_turnover: initialValues.estimate_monthly_turnover,
      average_of_days_prepays: initialValues.average_of_days_prepays,
      website: initialValues.website,
      email: initialValues.email,
      phone: initialValues.phone,
      contact_name: initialValues.contact_name,
      google_token: initialValues.google_token,
      proposed_services: serviceIndices,
    }
    const res = await submit(sendData)
    if (res.code === 0) {
      message({
        message: 'Thank you for your submission! We"ll get in touch with you SHORTLY.',
        type: 'success',
        duration: 3000,
        closable: false,
      })
    }
  }
  catch {
    message({
      message: 'form submission failed. please review information & try again.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
  }
  finally {
    isLoading.value = false
  }
}
const veeformRef = ref()
</script>

<template>
  <div class="biller-registration">
    <section class="start">
      <div class="start-wrap">
        <div class="title">
          <h2>Receive a full</h2>
          <h2>Biller Application Kit<span class="accent-dot">.</span></h2>
        </div>
        <div class="description">
          <p>
            Complete our quick pre-application questionnaire to help us understand your business and the solutions
            you're interested in. One of
            our Business Development Managers will be in touch shortly to guide you through the next steps.
          </p>
        </div>
        <div>
          <button class="btn text-white" @click="scrollToQuestionnaire">
            PROCEED WITH QUESTIONNAIRE
          </button>
        </div>
      </div>
    </section>
    <section ref="questionnaireSection" class="questionnaire">
      <div class="questionnaire-title">
        <h2 class="text-[36px] py-[6rem] text-center text-white">
          MERCHANT QUESTIONNAIRE
        </h2>
      </div>
      <div class="questionnaire-form">
        <div class="my-10">
          <VeeForm ref="veeformRef" :validation-schema="validationSchema" @submit="questionSubmit">
            <Field v-slot="{ errorMessage }" v-model="initialValues.abn" name="abn">
              <div class="flex  gap-1 mb-6">
                <div class="w-full">
                  <InputText
                    v-model="initialValues.abn" class="custom-input  w-[80%]" name="abn" type="text"
                    placeholder="*ABN"
                  />
                  <Message v-if="errorMessage" class="ml-4 mt-2" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </div>
                <!-- <button class="search-btn">
                SEARCH
              </button> -->
              </div>
            </Field>
            <Field v-slot="{ errorMessage }" v-model="initialValues.business_category" name="business_category">
              <div class="flex justify-between items-center gap-1 mb-10">
                <div class="w-full">
                  <InputText
                    v-model="initialValues.business_category" class="custom-input w-[80%]"
                    name="business_category" type="text" placeholder="*Business Industry"
                  />
                  <Message v-if="errorMessage" class="ml-4 mt-2" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </div>

                <!-- <button class="search-btn">
                SEARCH
              </button> -->
              </div>
            </Field>
            <div class="mb-10">
              <divide />
            </div>
            <div class="sub-form mb-4">
              <Field
                v-slot="{ errorMessage }" v-model="initialValues.average_sales_amount_per_order"
                name="average_sales_amount_per_order"
              >
                <div>
                  <InputText
                    v-model="initialValues.average_sales_amount_per_order" class="custom-input w-full"
                    name="average_sales_amount_per_order" type="text"
                    placeholder="*Average Sales Amount Per Order ($)"
                  />
                  <Message v-if="errorMessage" class="ml-4 mt-2" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </div>
              </Field>
              <Field
                v-slot="{ errorMessage }" v-model="initialValues.percentage_of_orders_prepaid"
                name="percentage_of_orders_prepaid"
              >
                <div>
                  <InputText
                    v-model="initialValues.percentage_of_orders_prepaid" class="custom-input w-full"
                    name="percentage_of_orders_prepaid" type="text" placeholder="*Percentage of Orders Prepaid (%)"
                  />
                  <Message v-if="errorMessage" class="ml-4 mt-2" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </div>
              </Field>
              <Field
                v-slot="{ errorMessage }" v-model="initialValues.estimate_monthly_turnover"
                name="estimate_monthly_turnover"
              >
                <div>
                  <InputText
                    v-model="initialValues.estimate_monthly_turnover" class="custom-input w-full"
                    name="estimate_monthly_turnover" type="text" placeholder="*Estimate Monthly Turnover ($)"
                  />
                  <Message v-if="errorMessage" class="ml-4 mt-2" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </div>
              </Field>
              <Field
                v-slot="{ errorMessage }" v-model="initialValues.average_of_days_prepays"
                name="average_of_days_prepays"
              >
                <div>
                  <InputText
                    v-model="initialValues.average_of_days_prepays" class="custom-input w-full"
                    name="average_of_days_prepays" type="text" placeholder="*Estimate Monthly Turnover ($)"
                  />
                  <Message v-if="errorMessage" class="ml-4 mt-2" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </div>
              </Field>
            </div>
            <div class="service mt-4 mb-10">
              <div class="service-title">
                *Proposed Service
              </div>
              <Field v-slot="{ errorMessage }" name="proposed_services">
                <div class="check-box flex">
                  <div class="flex items-center gap-2 mr-16">
                    <Checkbox v-model="services.directDebit" class="custom-checkbox " binary />
                    <label> Direct Debit </label>
                  </div>
                  <div class="flex items-center gap-2 mr-16">
                    <Checkbox v-model="services.bPay" class="custom-checkbox" binary />
                    <label> BPay </label>
                  </div>
                  <div class="flex items-center gap-2 mr-16">
                    <Checkbox v-model="services.webPay" class="custom-checkbox" binary />
                    <label> WebPay </label>
                  </div>
                </div>
                <Message v-if="errorMessage" class="ml-4 mt-2" severity="error" size="small" variant="simple">
                  <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                    <span class="text-[#ff3131]">
                      {{
                        errorMessage }}
                    </span>
                  </div>
                </Message>
              </Field>
            </div>

            <div class="mb-10">
              <divide style="transform: scale(-1);" />
            </div>

            <div class="contact ">
              <div class="contact-title mt-4 mb-6">
                Contact
              </div>

              <Field v-slot="{ errorMessage }" v-model="initialValues.contact_name" name="contact_name">
                <div class="mb-4">
                  <InputText
                    v-model="initialValues.contact_name" class="custom-input w-full" name="contact_name"
                    type="text" placeholder="*Contact Name"
                  />
                  <Message v-if="errorMessage" class="ml-4 -mt-2" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </div>
              </Field>
              <Field v-slot="{ errorMessage }" v-model="initialValues.phone" name="phone">
                <div class="mb-4">
                  <InputText
                    v-model="initialValues.phone" class="custom-input w-full" name="phone" type="text"
                    placeholder="*Phone"
                  />
                  <Message v-if="errorMessage" class="ml-4 -mt-2" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </div>
              </Field>
              <Field v-slot="{ errorMessage }" v-model="initialValues.email" name="email">
                <div class="mb-4">
                  <InputText
                    v-model="initialValues.email" class="custom-input w-full" name="email" type="text"
                    placeholder="*Email"
                  />
                  <Message v-if="errorMessage" class="ml-4 -mt-2" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </div>
              </Field>
              <Field v-slot="{ errorMessage }" v-model="initialValues.website" name="website">
                <div class="mb-4">
                  <InputText
                    v-model="initialValues.website" class="custom-input w-full" name="website" type="text"
                    placeholder="*Website"
                  />
                  <Message v-if="errorMessage" class="ml-4 -mt-2" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </div>
              </Field>
            </div>
            <div class="captcha mt-4">
              <div class="contact-title mb-4">
                *CAPTCHA
              </div>
              <Field v-slot="{ errorMessage }" v-model="initialValues.google_token" name="google_token">
                <div class="field mb-4">
                  <GoogleRecaptcha
                    ref="recaptchaRef" class="mb-2" @verify="onRecaptchaVerify"
                    @expired="onRecaptchaExpired" @error="onRecaptchaError"
                  />
                  <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
            </div>

            <Button class="btn mt-2" severity="warn" :disabled="isLoading" type="submit" label="SUBMIT">
              SUBMIT
            </Button>
          </VeeForm>
        </div>
      </div>
    </section>

    <section class="cta">
      <div class="cta-content">
        <div>
          <h2>TALK TO A BIZ DEV MANAGER<span class="accent-dot">.</span></h2>
          <p>
            Have questions? Get expert guidance, talk to a payment expert, and see how Bill Buddy can help
            your
            business.
          </p>
        </div>
        <div class="cta-buttons flex ">
          <router-link to="/biller-registration" class="talk-to-us-button register">
            BILLER REGISTRATION
          </router-link>
          <router-link to="/Contact-Us" class="talk-to-us-button request-call">
            REQUEST A CALL
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.biller-registration {
  width: 100%;
  background-color: #f5f5ff;
  padding-bottom: 2rem;
}

.start {
  background-color: #e1ffa9;
  background-image: url(@/assets/official/cutBg.png);
  background-repeat: no-repeat;
  background-position: right;
  background-size: 50% 102%;
  padding-bottom: 6rem;

  @include media-breakpoint-down(lg) {
    background-size: 40% 100%;
    padding-bottom: 4rem;
  }

  @include media-breakpoint-down(md) {
    background-size: 30% 100%;
    padding-bottom: 3rem;
  }

  @include media-breakpoint-down(sm) {
    background-image: none;
    padding-bottom: 2rem;
  }
}

.start-wrap {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;

  @include media-breakpoint-down(md) {
    padding: 0 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    padding: 0 1rem;
  }
}

.title {
  padding: 6rem 0 2rem 0;

  @include media-breakpoint-down(lg) {
    padding: 4rem 0 1.5rem 0;
  }

  @include media-breakpoint-down(md) {
    padding: 3rem 0 1rem 0;
  }

  @include media-breakpoint-down(sm) {
    padding: 2rem 0 1rem 0;
  }

  h2 {
    font-size: 4rem;
    color: #181349;
    font-weight: 700;

    @include media-breakpoint-down(lg) {
      font-size: 3rem;
    }

    @include media-breakpoint-down(md) {
      font-size: 2.5rem;
    }

    @include media-breakpoint-down(sm) {
      font-size: 2rem;
    }
  }
}

.description p {
  font-size: 16px;
  opacity: 0.9;
  text-align: left;
  line-height: 30px;
  width: 45%;

  @include media-breakpoint-down(lg) {
    width: 60%;
  }

  @include media-breakpoint-down(md) {
    width: 80%;
  }

  @include media-breakpoint-down(sm) {
    width: 100%;
    line-height: 24px;
    font-size: 14px;
  }
}

.accent-dot {
  color: #ff5722;
}

.btn {
  display: inline-block;
  padding: 1rem;
  background-color: #fe4c1c;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 700;
  margin-top: 1.5rem;
  width: 320px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: hsl(16, 100%, 70%);
  }

  &:active {
    background-color: hsl(16, 100%, 60%);
  }

  @include media-breakpoint-down(md) {
    width: 280px;
    padding: 0.8rem;
    font-size: 14px;
  }

  @include media-breakpoint-down(sm) {
    width: 100%;
    max-width: 260px;
    padding: 0.7rem;
  }
}

.btn-submit {
  width: 250px;
  @include media-breakpoint-down(sm) {
    width: 100%;
    max-width: 200px;
  }
}

.search-btn {
  display: inline-block;
  padding: 1rem;
  background-color: #fe4c1c;
  font-size: 1rem;
  border-radius: 8px;
  font-weight: 700;
  color: #fff;
  width: 220px;
  cursor: pointer;

  @include media-breakpoint-down(md) {
    width: 180px;
    padding: 0.8rem;
    font-size: 14px;
  }

  @include media-breakpoint-down(sm) {
    width: 100%;
    padding: 0.7rem;
  }
}

.questionnaire-title {
  background: url('@/assets/official/bg/ultra_BILLER REG BG.png') no-repeat center;
  background-position: center;
  background-size: 100% 180%;

  @include media-breakpoint-down(md) {
    background-size: cover;
  }

  h2 {
    @include media-breakpoint-down(lg) {
      font-size: 32px;
      padding: 4rem 0;
    }

    @include media-breakpoint-down(md) {
      font-size: 28px;
      padding: 3rem 0;
    }

    @include media-breakpoint-down(sm) {
      font-size: 24px;
      padding: 2rem 0;
    }
  }
}

.questionnaire-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;

  @include media-breakpoint-down(md) {
    padding: 0 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    padding: 0 1rem;
  }
}

.custom-input {
  height: 45px;
  border: 2px solid #181349;
  border-radius: 20px;
  padding-left: 30px;
  font-size: 1.2rem;

  @include media-breakpoint-down(md) {
    height: 40px;
    font-size: 1rem;
    padding-left: 20px;
  }

  @include media-breakpoint-down(sm) {
    height: 38px;
    font-size: 0.9rem;
  }
}

:deep(.p-inputtext::placeholder) {
  color: #b3b8ca;
  font-weight: 600;
}

.sub-form {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;

  @include media-breakpoint-down(md) {
    gap: 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.custom-checkbox {
  --p-checkbox-width: 28px;
  --p-checkbox-height: 28px;
  --p-checkbox-border-color: #181349;

  @include media-breakpoint-down(sm) {
    --p-checkbox-width: 24px;
    --p-checkbox-height: 24px;
  }
}

@media(prefers-color-scheme: dark) {
  .custom-checkbox {
    --p-checkbox-background: #f5f5ff;
    --p-checkbox-border-color: #181349;
    --p-checkbox-icon-checked-color: #f5f5ff;
  }

  .btn-submit {
    color: #fff;
  }
}

.service-title {
  font-size: 1.5rem;
  font-weight: 700;
  padding-top: 2rem;
  padding-bottom: 1rem;

  @include media-breakpoint-down(md) {
    font-size: 1.3rem;
    padding-top: 1.5rem;
    padding-bottom: 0.8rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 1.2rem;
    padding-top: 1rem;
  }
}

.check-box {
  margin-top: 10px;

  @include media-breakpoint-down(md) {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  label {
    color: #181349;
    margin-left: 1rem;

    @include media-breakpoint-down(sm) {
      font-size: 14px;
      margin-left: 0.5rem;
    }
  }
}

.contact {
  display: grid;
  grid-template-rows: repeat(4, 1fr);

  input {
    margin-bottom: 1em;
  }
}

.contact-title {
  font-size: 1.5rem;
  font-weight: 700;

  @include media-breakpoint-down(md) {
    font-size: 1.3rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 1.2rem;
  }
}

.cta {
  background-color: #f5f5ff;
  padding: 4rem 2rem;

  @include media-breakpoint-down(lg) {
    padding: 3rem 1.5rem;
  }

  @include media-breakpoint-down(md) {
    padding: 2rem 1rem;
  }
}

.cta-content {
  max-width: 1200px;
  margin: 0 auto;
  background-color: #ffe3e8;
  border-radius: 16px;
  padding: 2.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @include media-breakpoint-down(lg) {
    padding: 2rem;
    flex-direction: column;
    text-align: center;
  }

  @include media-breakpoint-down(md) {
    padding: 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    padding: 1.25rem;
  }

  h2 {
    font-size: 2.5rem;
    color: #18134b;
    margin-bottom: 1rem;
    font-weight: 700;

    @include media-breakpoint-down(lg) {
      font-size: 2rem;
    }

    @include media-breakpoint-down(md) {
      font-size: 1.75rem;
    }

    @include media-breakpoint-down(sm) {
      font-size: 1.5rem;
    }
  }

  p {
    color: #18134b;
    margin-bottom: 2rem;
    width: 70%;
    font-size: 16px;
    font-style: italic;
    line-height: 1.6;

    @include media-breakpoint-down(lg) {
      width: 100%;
      margin-bottom: 1.5rem;
    }

    @include media-breakpoint-down(sm) {
      font-size: 14px;
    }
  }
}

.cta-buttons {
  @include media-breakpoint-down(sm) {
    flex-direction: column;
    gap: 1rem;
    align-items: center;

    a {
      margin-right: 0 !important;
    }
  }

  a {
    height: 50px;
    width: 220px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;

    @include media-breakpoint-down(md) {
      height: 45px;
      width: 200px;
      font-size: 14px;
    }

    @include media-breakpoint-down(sm) {
      width: 100%;
      min-width: 180px;
    }
  }
}
</style>
