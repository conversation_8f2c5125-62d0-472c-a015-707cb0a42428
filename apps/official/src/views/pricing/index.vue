<script setup lang="ts">
// No additional logic needed
</script>

<template>
  <div class="pricing">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-content">
        <h1>Simple, Transparent Pricing</h1>
        <p>Choose the plan that's right for your business</p>
      </div>
    </section>

    <!-- Pricing Plans -->
    <section class="pricing-plans">
      <div class="container">
        <div class="pricing-grid">
          <!-- Basic Plan -->
          <div class="pricing-card">
            <div class="pricing-header">
              <h2>Basic</h2>
              <div class="price">
                <span class="amount">$29</span>
                <span class="period">/month</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul>
                <li><i class="pi pi-check" /> Up to 5 users</li>
                <li><i class="pi pi-check" /> Basic analytics</li>
                <li><i class="pi pi-check" /> Email support</li>
                <li><i class="pi pi-check" /> 5GB storage</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <router-link to="/merchant/register" class="btn btn-primary">
                Get Started
              </router-link>
            </div>
          </div>

          <!-- Pro Plan -->
          <div class="pricing-card featured">
            <div class="pricing-header">
              <div class="popular-badge">
                Most Popular
              </div>
              <h2>Pro</h2>
              <div class="price">
                <span class="amount">$79</span>
                <span class="period">/month</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul>
                <li><i class="pi pi-check" /> Up to 20 users</li>
                <li><i class="pi pi-check" /> Advanced analytics</li>
                <li><i class="pi pi-check" /> Priority support</li>
                <li><i class="pi pi-check" /> 50GB storage</li>
                <li><i class="pi pi-check" /> Custom integrations</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <router-link to="/merchant/register" class="btn btn-primary">
                Get Started
              </router-link>
            </div>
          </div>

          <!-- Enterprise Plan -->
          <div class="pricing-card">
            <div class="pricing-header">
              <h2>Enterprise</h2>
              <div class="price">
                <span class="amount">Custom</span>
                <span class="period">/month</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul>
                <li><i class="pi pi-check" /> Unlimited users</li>
                <li><i class="pi pi-check" /> Custom analytics</li>
                <li><i class="pi pi-check" /> 24/7 support</li>
                <li><i class="pi pi-check" /> Unlimited storage</li>
                <li><i class="pi pi-check" /> Custom integrations</li>
                <li><i class="pi pi-check" /> Dedicated account manager</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <router-link to="/Contact-Us" class="btn btn-primary">
                Contact Sales
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Comparison -->
    <section class="features-comparison">
      <div class="container">
        <h2>Compare Features</h2>
        <div class="comparison-table">
          <table>
            <thead>
              <tr>
                <th>Features</th>
                <th>Basic</th>
                <th>Pro</th>
                <th>Enterprise</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Users</td>
                <td>Up to 5</td>
                <td>Up to 20</td>
                <td>Unlimited</td>
              </tr>
              <tr>
                <td>Storage</td>
                <td>5GB</td>
                <td>50GB</td>
                <td>Unlimited</td>
              </tr>
              <tr>
                <td>Analytics</td>
                <td>Basic</td>
                <td>Advanced</td>
                <td>Custom</td>
              </tr>
              <tr>
                <td>Support</td>
                <td>Email</td>
                <td>Priority</td>
                <td>24/7</td>
              </tr>
              <tr>
                <td>Custom Integrations</td>
                <td><i class="pi pi-times" /></td>
                <td><i class="pi pi-check" /></td>
                <td><i class="pi pi-check" /></td>
              </tr>
              <tr>
                <td>Dedicated Account Manager</td>
                <td><i class="pi pi-times" /></td>
                <td><i class="pi pi-times" /></td>
                <td><i class="pi pi-check" /></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq">
      <div class="container">
        <h2>Frequently Asked Questions</h2>
        <div class="faq-grid">
          <div class="faq-item">
            <h3>Can I change plans later?</h3>
            <p>Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.</p>
          </div>
          <div class="faq-item">
            <h3>What payment methods do you accept?</h3>
            <p>We accept all major credit cards, PayPal, and bank transfers for Enterprise plans.</p>
          </div>
          <div class="faq-item">
            <h3>Is there a free trial?</h3>
            <p>Yes, all plans come with a 14-day free trial. No credit card required.</p>
          </div>
          <div class="faq-item">
            <h3>What happens if I exceed my plan limits?</h3>
            <p>You'll be notified when you're approaching your limits and can upgrade your plan at any time.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
      <div class="container">
        <h2>Ready to Get Started?</h2>
        <p>Join thousands of businesses that trust our platform.</p>
        <router-link to="/merchant/register" class="btn btn-primary">
          Start Free Trial
        </router-link>
      </div>
    </section>
  </div>
</template>

<style scoped>
.pricing {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  padding: 6rem 2rem;
  text-align: center;
  border-radius: 8px;
  margin-bottom: 4rem;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.hero p {
  font-size: 1.25rem;
  opacity: 0.9;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
}

.pricing-plans {
  padding: 4rem 0;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.pricing-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  text-align: center;
  position: relative;
  transition: transform 0.3s ease;
}

.pricing-card.featured {
  transform: scale(1.05);
  border: 2px solid #007bff;
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #007bff;
  color: white;
  padding: 0.25rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
}

.pricing-header {
  margin-bottom: 2rem;
}

.pricing-header h2 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.price {
  font-size: 2.5rem;
  color: #333;
  font-weight: 700;
}

.price .period {
  font-size: 1rem;
  color: #666;
}

.pricing-features {
  margin-bottom: 2rem;
}

.pricing-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pricing-features li {
  margin-bottom: 1rem;
  color: #666;
}

.pricing-features i {
  color: #007bff;
  margin-right: 0.5rem;
}

.pricing-footer {
  margin-top: auto;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.features-comparison {
  padding: 4rem 0;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 4rem 0;
}

.features-comparison h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 2rem;
}

.comparison-table {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

th, td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

td {
  color: #666;
}

td i {
  color: #007bff;
}

.faq {
  padding: 4rem 0;
}

.faq h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 3rem;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.faq-item {
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.faq-item h3 {
  font-size: 1.25rem;
  color: #333;
  margin-bottom: 1rem;
}

.faq-item p {
  color: #666;
  line-height: 1.6;
}

.cta {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
  border-radius: 8px;
  margin: 4rem 0;
}

.cta h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.cta p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .hero {
    padding: 4rem 1rem;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .pricing-card.featured {
    transform: none;
  }

  .faq-grid {
    grid-template-columns: 1fr;
  }
}
</style>
