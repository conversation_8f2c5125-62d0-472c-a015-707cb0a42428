<script setup lang="ts">
// No additional logic needed
</script>

<template>
  <div class="about">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-content flex items-center justify-between">
        <div class="w-1/2 flex flex-col items-start">
          <h1>About Us<span class="accent-dot">.</span></h1>
          <p style="line-height: 32px;">
            Established in 2003, <PERSON> is a proudly 100% Australian-
          </p>
          <p style="line-height: 32px;">
            owned and operated company. For over two decades,
          </p>
          <p style="line-height: 32px;">
            we've built a strong reputation for trust, performance, and
          </p>
          <p style="line-height: 32px;">
            personalised service.
          </p>
          <p style="line-height: 32px;">
            Our mission is simple: to provide reliable and flexible
          </p>
          <p style="line-height: 32px;">
            customer payment solutions tailored to the unique needs
          </p>
          <p style="line-height: 32px;">
            of Australian businesses.
          </p>
        </div>
        <div class="hero-image w-1/2">
          <!-- <img src="@/assets/official/homeBg.png" alt="about us" style="height: 100%; width: 100%;"> -->
        </div>
      </div>
    </section>

    <section class="profile">
      <div class="container">
        <div class="profile-wrap">
          <div class="flex flex-col items-start mr-8 mt-6">
            <h2>Company Profile<span class="accent-dot">.</span></h2>
            <div>
              <p>
                Bill Buddy is 100% Australian owned and operated and
                since its inception has built a reputation on trust,
                performance, and personal service, serving many
                Australian businesses and their customers.
              </p>
              <p>
                Our product range is designed to allow any Australian
                business the ability to offer multiple payment options to their
                customers without incurring the costly establishment ond
                operational costs usually associated with such banking
                facilities.Bill Buddy can supply a series of solutions
                allowing the existing billing/finance solutions of an
                organisation to be integrated with our payment systems to
                improve the cash flow and productivity of the organisation.
              </p>
              <p>
                To use Bill Buddy's services an orgonisation only needs an
                ABN and an Australian bank account.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="credentials">
      <div class="credentials-wrap">
        <div class="credentials-content">
          <h2>Our Credentials<span class="accent-dot">.</span></h2>
          <div>
            <p>
              Any organisation who is involving a third party in facilitating
              their customer payments needs to be assured of the security of
              the organisation they are working with:
            </p>
            <ul>
              <li>Bill Buddy has proven stability and integrity since 2003</li>
              <li>
                Bill Buddy transacts millions of dollars each month for
                Australian businesses
              </li>

              <li>Bill Buddy is fully ASIC compliant</li>
              <li>
                Bill Buddy's systems are located in one of Australia's
                leading tier one data centres featuring multiple levels of
                physical, fire, and data security.
              </li>
              <li>
                Bill Buddy's websites that involve customer or transaction
                data use 1,024 bit encryption and meet Payment Card
                Industry (PCI) Data Security Standards. These are
                standards that were developed by major credit card
                companies to safeguard customer card information.
              </li>
              <li>
                Bill Buddy leverages latest technologies which are adaptive in
                nature for fraud monitoring to reduce the risks associated with bank accounts and credit
                card payments
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
    <!-- CTA Section -->
    <!-- <section class="cta">
      <div class="container">
        <h2>Join Our Journey</h2>
        <p>Be part of our mission to transform businesses worldwide.</p>
        <router-link to="/merchant/register" class="btn btn-primary">
          Get Started
        </router-link>
      </div>
    </section> -->
    <section class="cta">
      <div class="cta-content">
        <div>
          <h2>TALK TO A BIZ DEV MANAGER<span class="accent-dot">.</span> </h2>
          <p>
            Have questions? Get expert guidance, talk to a payment expert, and see how Bill Buddy can help your
            business.
          </p>
        </div>
        <div class="cta-buttons flex">
          <router-link
            to="/biller-registration" class="talk-to-us-button register"
          >
            BILLER REGISTRATION
          </router-link>
          <router-link to="/Contact-Us" class="talk-to-us-button request-call">
            REQUEST A CALL
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/mixins/breakpoints' as *;

.about {
  background-color: #f5f5ff;
}

.accent-dot {
  color: #ff5722;
}

.hero {
  background-color: #e1ffa9;
  padding: 7.9rem 2rem;
  text-align: center;
  border-radius: 8px;
  margin-bottom: 2rem;

  background-image: url(@/assets/official/cutBg.png);
  background-repeat: no-repeat;
  background-position: right;
  background-size: 50% 102%;

  @include media-breakpoint-down(md) {
    padding: 2rem;
    background-size: 100% 102%;
  }

  .hero-content {
    max-width: 1200px;
    margin: 0 auto;
    color: #181349;

    h1 {
      font-size: 4rem;
      margin-bottom: 1rem;
      font-weight: 900;
      text-align: left;
    }

    p {
      font-size: 16px;
      opacity: 0.9;
      text-align: left;
      line-height: 35px;
      width: 70%;
    }

    @include media-breakpoint-down(md) {
      .flex {
        width: 100%;
      }

      .hero-image {
        display: none;
      }

      h1 {
        font-size: 3rem;
        margin: 1rem 0;
        font-weight: 900;
        text-align: left;
      }

      p {
        width: 100%;
      }
    }

  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.profile {
  padding: 1rem 0;
  text-align: center;

  p {
    font-size: 16px;
    color: #181349;
    line-height: 1.6;
    max-width: 500px;
    margin: 0 auto;
    text-align: left;
    margin-bottom: 2rem;
  }

  h2 {
    font-size: 48px;
    color: #181349;
    font-weight: 900;
    margin-bottom: 2rem;

    @include media-breakpoint-down(md) {
      font-size: 2rem;
      margin-top: 1rem;
      margin-bottom: 2rem;
      white-space: nowrap;
    }
  }

  .profile-wrap {
    background-color: #ffe3e8;
    background-image: url('@/assets/official/portal/About Us Image 2.png');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: left;
    height: 600px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 12px;
    border: 1px solid #b2b2b2;
    padding: 0 30px;

    &::before {
      content: '';
      width: 30%;

      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    .flex {
      @include media-breakpoint-down(md) {
        margin: 0 !important;
      }
    }

    @include media-breakpoint-down(md) {
      background-image: none;
      height: 700px;
    }
  }

}

.values {
  padding: 4rem 0;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.values h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 3rem;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.value-card {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.value-card i {
  font-size: 2.5rem;
  color: #007bff;
  margin-bottom: 1rem;
}

.value-card h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.value-card p {
  color: #666;
  line-height: 1.6;
}

.team {
  padding: 4rem 0;
}

.team h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 3rem;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.team-member {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.team-member img {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  margin-bottom: 1rem;
  object-fit: cover;
}

.team-member h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.team-member .position {
  color: #007bff;
  font-weight: 500;
  margin-bottom: 1rem;
}

.team-member .bio {
  color: #666;
  line-height: 1.6;
}

.credentials {
  background-color: #f5f5ff;
  width: 100%;

  .credentials-wrap {
    max-width: 1200px;
    /* height: 800px; */
    margin: 2rem auto;
    background-image: url(@/assets/official/about-us-2.png);
    background-repeat: no-repeat;
    /* background-size: contain; */
    background-size: cover;
    background-position: right bottom;
    display: flex;
    justify-content: space-between;
    border: 1px solid #b2b2b2;
    border-radius: 22px;
    padding: 2rem;

    @include media-breakpoint-down(md) {
      background-image: none;
      height: 900px;
    }

    /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
    &::after {
      content: '';
      width: 50%;

      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    .credentials-content {
      width: 50%;

      @include media-breakpoint-down(md) {
        width: 100%;
      }

      h2 {
        font-size: 48px;
        /* color: #333; */
        color: #181349;
        font-weight: 900;
        margin-bottom: 2rem;
      }

      p,
      ul>li {
        font-size: 16px;
        /* color: #666; */
        color: #181349;
        line-height: 1.7;
      }

      ul {
        list-style: disc;
        padding: 2rem;
      }
    }
  }
}

.cta {
  /* background-color: #ffe0e0; */
  background-color: #f5f5ff;
  padding: 4rem 2rem;

  h2 {
    font-size: 2.5rem;
    color: #18134b;
    margin-bottom: 1rem;
    font-weight: 800;

    @include media-breakpoint-down(md) {
      font-size: 2rem;
    }
  }

  p {
    color: #18134b;
    margin-bottom: 2rem;
    font-style: italic;
    width: 65%;
    line-height: 1.6;

    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }
}

.cta-content {
  max-width: 1200px;
  margin: 0 auto;
  /* text-align: center; */
  background-color: #ffe3e8;
  border-radius: 16px;
  padding: 3rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @include media-breakpoint-down(md) {
    flex-direction: column;
    align-items: center;

    .cta-buttons {
      width: 100%;
      flex: 1;
      flex-direction: column;
      align-items: center;
      gap: 1rem;

      .btn {
        width: 100%;
        margin: 0;

      }
    }
  }
}

.cta-buttons a {
  height: 50px;
  width: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 16px;
}

.btn-primary {
  background-color: white;
  color: #007bff;
}

.btn-primary:hover {
  background-color: #f8f9fa;
}
</style>
