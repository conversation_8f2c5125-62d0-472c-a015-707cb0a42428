<script setup lang="ts">
// No additional logic needed
</script>

<template>
  <div class="features">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-content">
        <h1>Powerful Features</h1>
        <p>Everything you need to manage and grow your business</p>
      </div>
    </section>

    <!-- Main Features -->
    <section class="main-features">
      <div class="container">
        <div class="features-grid">
          <!-- Analytics -->
          <div class="feature-item">
            <div class="feature-icon">
              <i class="pi pi-chart-line" />
            </div>
            <h2>Analytics & Insights</h2>
            <p>Get detailed insights into your business performance with advanced analytics tools.</p>
            <ul>
              <li>Real-time data visualization</li>
              <li>Custom reporting</li>
              <li>Performance metrics</li>
              <li>Trend analysis</li>
            </ul>
          </div>

          <!-- Customer Management -->
          <div class="feature-item">
            <div class="feature-icon">
              <i class="pi pi-users" />
            </div>
            <h2>Customer Management</h2>
            <p>Efficiently manage your customer relationships and track interactions.</p>
            <ul>
              <li>Customer profiles</li>
              <li>Interaction history</li>
              <li>Customer segmentation</li>
              <li>Automated workflows</li>
            </ul>
          </div>

          <!-- Automation -->
          <div class="feature-item">
            <div class="feature-icon">
              <i class="pi pi-sync" />
            </div>
            <h2>Automation</h2>
            <p>Automate repetitive tasks and streamline your workflow.</p>
            <ul>
              <li>Task automation</li>
              <li>Email campaigns</li>
              <li>Notification system</li>
              <li>Integration tools</li>
            </ul>
          </div>

          <!-- Security -->
          <div class="feature-item">
            <div class="feature-icon">
              <i class="pi pi-shield" />
            </div>
            <h2>Security</h2>
            <p>Enterprise-grade security to protect your business data.</p>
            <ul>
              <li>Two-factor authentication</li>
              <li>Data encryption</li>
              <li>Access control</li>
              <li>Audit logs</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Additional Features -->
    <section class="additional-features">
      <div class="container">
        <h2>Additional Features</h2>
        <div class="features-list">
          <div class="feature-card">
            <i class="pi pi-mobile" />
            <h3>Mobile App</h3>
            <p>Access your business data on the go with our mobile application.</p>
          </div>
          <div class="feature-card">
            <i class="pi pi-file-edit" />
            <h3>Document Management</h3>
            <p>Organize and manage your documents efficiently.</p>
          </div>
          <div class="feature-card">
            <i class="pi pi-comments" />
            <h3>Team Collaboration</h3>
            <p>Work together seamlessly with built-in collaboration tools.</p>
          </div>
          <div class="feature-card">
            <i class="pi pi-globe" />
            <h3>Multi-language Support</h3>
            <p>Access the platform in your preferred language.</p>
          </div>
          <div class="feature-card">
            <i class="pi pi-cloud" />
            <h3>Cloud Storage</h3>
            <p>Secure cloud storage for all your business data.</p>
          </div>
          <div class="feature-card">
            <i class="pi pi-cog" />
            <h3>Customization</h3>
            <p>Customize the platform to match your business needs.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Integration Section -->
    <section class="integrations">
      <div class="container">
        <h2>Integrations</h2>
        <p>Connect with your favorite tools and services</p>
        <div class="integration-grid">
          <div class="integration-item">
            <!-- <img src="@/assets/integrations/slack.png" alt="Slack"> -->
          </div>
          <div class="integration-item">
            <!-- <img src="@/assets/integrations/gmail.png" alt="Gmail"> -->
          </div>
          <div class="integration-item">
            <!-- <img src="@/assets/integrations/trello.png" alt="Trello"> -->
          </div>
          <div class="integration-item">
            <!-- <img src="@/assets/integrations/dropbox.png" alt="Dropbox"> -->
          </div>
          <div class="integration-item">
            <!-- <img src="@/assets/integrations/zoom.png" alt="Zoom"> -->
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
      <div class="container">
        <h2>Ready to Experience These Features?</h2>
        <p>Start your free trial today and see the difference.</p>
        <router-link to="/merchant/register" class="btn btn-primary">
          Start Free Trial
        </router-link>
      </div>
    </section>
  </div>
</template>

<style scoped>
.features {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  padding: 6rem 2rem;
  text-align: center;
  border-radius: 8px;
  margin-bottom: 4rem;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.hero p {
  font-size: 1.25rem;
  opacity: 0.9;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
}

.main-features {
  padding: 4rem 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.feature-icon i {
  font-size: 1.5rem;
  color: #007bff;
}

.feature-item h2 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.feature-item p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-item li {
  color: #666;
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
}

.feature-item li::before {
  content: "•";
  color: #007bff;
  position: absolute;
  left: 0;
}

.additional-features {
  padding: 4rem 0;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 4rem 0;
}

.additional-features h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 3rem;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-card {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-card i {
  font-size: 2.5rem;
  color: #007bff;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.25rem;
  color: #333;
  margin-bottom: 1rem;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

.integrations {
  padding: 4rem 0;
}

.integrations h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.integrations p {
  text-align: center;
  color: #666;
  margin-bottom: 3rem;
}

.integration-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  align-items: center;
}

.integration-item {
  text-align: center;
}

.integration-item img {
  max-width: 100px;
  height: auto;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.integration-item img:hover {
  filter: grayscale(0%);
}

.cta {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
  border-radius: 8px;
  margin: 4rem 0;
}

.cta h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.cta p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: white;
  color: #007bff;
}

.btn-primary:hover {
  background-color: #f8f9fa;
}

@media (max-width: 768px) {
  .hero {
    padding: 4rem 1rem;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .features-list {
    grid-template-columns: 1fr;
  }

  .integration-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
