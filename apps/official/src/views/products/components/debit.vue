<script setup lang="ts">

</script>

<template>
  <div class="direct-debit">
    <section class="title">
      <div class="breadcrumb">
        <div>Products</div>
        <i class="pi pi-chevron-right" style="color: #fff;" />
        <div>Direct Debit</div>
      </div>
      <div class="title-content container">
        <div class="content-left">
          <div class="content-left-title">
            DIRECT DEBIT<span class="accent-dot">.</span>
          </div>
          <div class="content-left-description">
            <p>
              Direct debit is a system whereby funds are withdrawn
              electronically by <PERSON> from a customers bank
              account or credit card on the date(s) nominated by the
              Biller. All the customer needs to do is provide their
              authority prior to the first debit taking place.
            </p>
          </div>
          <div>
            <router-link to="/biller-registration" class="product-start-now-button">
              START NOW
            </router-link>
          </div>
        </div>
        <div class="content-right">
          <div>
            <img src="@/assets/official/products/DIRECT DEBIT/credit.png" alt="direct debit" style="width: 260px;">
          </div>
          <i class="pi pi-chevron-right" style="font-size: 5rem; color: #fe4c1c;margin:auto 15px" />
          <div>
            <img src="@/assets/official/products/DIRECT DEBIT/direct-debit-icon.png" alt="dollar" style="width: 200px;">
          </div>
        </div>
      </div>
    </section>
    <section class="work">
      <div class="container">
        <div class="work-title">
          <h2>HOW IT WORKS<span class="question-color">?</span></h2>
        </div>
        <div class="work-list">
          <div class="work-item text-xl">
            Your customer completes a
            Direct Debit Request Form.
            This is done by physically
            signing an authority or
            completing an electronic
            authority online (available
            to authorised billers only).
          </div>
          <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
          <div class="work-item text-xl">
            Bill Buddy commences
            debiting payments on
            the start date at the
            selected frequency.
          </div>
          <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
          <div class="work-item text-xl">
            Two to four business
            days (depending on
            payment origin) after
            the debit the cleared
            payment is
            transferred to you.
          </div>
          <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
          <div class="work-item text-xl">
            Comprehensive
            reporting provided
            each day a
            transaction occurs.
          </div>
          <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
          <div class="work-item text-xl">
            You have 24 hour
            access to our Biller
            Portal to manage
            customer payments
            and schedules.
          </div>
        </div>
      </div>
    </section>
    <section class="benefits">
      <div class="benefits-wrap">
        <div>
          <div class="benefits-title">
            Benefits<span class="accent-dot">.</span>
          </div>
          <div class="benefits-list">
            <div class="benefit-left-item">
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />
                Get paid on time.
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />
                Improve cash flow.
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />
                Make your product more affordable
                by offering payment plans.
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />
                Free up your time to focus on your
                core business.
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />
                Improve customer retention
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />
                Comprehensive reporting & management
                via email and the Biller Portal.
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />
                Funds transferred to you daily.
              </div>
            </div>
            <div class="benefit-right-item">
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />

                No charge to self-manage your debit
                schedules online.
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />

                Bill Buddy does not interfere in the
                relationship between you and your customer.
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />

                Bank account direct debits appear on
                customer's statement as your company name.
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />

                Free automated emails to Customers prior to
                their debit occurring.
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />

                Support for multiple payment schedules.
              </div>

              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />

                Support for fully automated schedules or
                totally ad-hoc transactions.
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="organisation">
      <div class="organisation-title">
        <h2>
          Is it suitable for my organisation<span class="question-color">?</span>
        </h2>
      </div>
      <div class="organisation-content">
        <div class="organisation-item">
          One-off payments
        </div>
        <div class="organisation-item">
          Regular amount recurring payments
        </div>
        <div class="organisation-item">
          Variable amount recurring payments
        </div>
        <div class="organisation-item">
          Variable amount ad-hoc payments
        </div>
        <div class="organisation-item">
          Payment plans
        </div>
        <div class="organisation-item">
          Time-critival payments
        </div>
      </div>
    </section>
    <section class="pricing relative">
      <img src="@/assets/official/products/illustration.jpg" alt="" class="pricing-image">
      <div class="pricing-wrap">
        <div class="pricing-right">
          <div class="pricing-title">
            <h2>Pricing<span class="accent-dot">.</span></h2>
          </div>
          <div class="pricing-content">
            <p>
              No setup fees. No hassle. Just simple, per-transaction pricing.
              To view our full pricing details, complete the Biller Registration process. Once submitted, you'll receive
              a Biller Application Kit to review.
              If you're happy with everything, simply sign and return the form — and you're all set!
            </p>
          </div>
        </div>
      </div>
    </section>
    <section class="cta">
      <div class="cta-content">
        <div>
          <h2>TALK TO A BIZ DEV MANAGER<span class="accent-dot">.</span></h2>
          <p>
            Have questions? Get expert guidance, talk to a payment expert, and see how Bill Buddy can help
            your
            business.
          </p>
        </div>
        <div class="cta-buttons flex">
          <router-link to="/biller-registration" class="talk-to-us-button register">
            BILLER REGISTRATION
          </router-link>
          <router-link to="/Contact-Us" class="talk-to-us-button request-call">
            REQUEST A CALL
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.direct-debit {
  background-color: #f5f5ff;
}

.accent-dot {
  color: #ff5722;
}

.question-color {
  color: #181349;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.title {
  background-color: #181349;
  width: 100%;

  &-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @include media-breakpoint-down(xl) {
      flex-direction: column;
      padding-bottom: 2rem;
    }
  }
}

.breadcrumb {
  max-width: 1300px;
  margin: 0 auto;
  padding: 5px;
  border-bottom: 1px solid rgba(245, 245, 255, 0.5);
  display: flex;
  justify-content: start;
  align-items: center;
  padding-bottom: 3px;

  div {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    margin: 10px 20px;
  }
}

.content {
  &-left {
    display: flex;
    flex-direction: column;
    justify-content: start;
    padding: 3rem 20px;
    flex: 1;

    @include media-breakpoint-down(md) {
      max-width: 100%;
      padding-inline: 0;
    }

    &-title {
      color: #e1ffa9;
      font-size: 5rem;
      font-weight: 900;
    }

    &-description p {
      color: #fff;
      text-align: left;
      width: 530px;
      margin-top: 2rem;
      margin-bottom: 2rem;
      line-height: 1.6;
      font-size: 16px;
      max-width: 100%;
    }
  }

  &-right {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: flex-end;

    @include media-breakpoint-down(xl) {
      justify-content: center;
    }
  }
}

.work {
  padding-block: 3rem;

  &-title h2 {
    font-size: 4rem;
    font-weight: 700;
    color: #fe4c1c;

    @include media-breakpoint-down(lg) {
      text-align: center;
    }
  }

  .pi-chevron-right {
    @include media-breakpoint-down(lg) {
      margin: 1.5rem auto !important;
      transform: rotate(90deg);
    }
  }

  &-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 2rem;

    @include media-breakpoint-down(lg) {
      flex-direction: column;
    }
  }

  &-item {
    background-color: #e1ffa9;
    border-radius: 28px;
    border: 1px solid rgba(179, 179, 178, 0.8);
    padding: 1rem;
    width: 220px;
    height: 240px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    justify-content: start;

    @include media-breakpoint-down(lg) {
      width: 100%;
      height: auto;
    }
  }
}

.benefits {
  width: 100%;
  background-color: #ffe3e8;
  background-image: url('@/assets/official/portal/Direct Debit.png');
  background-repeat: no-repeat;
  background-position: left bottom;
  background-size: 59% 97%;
  padding: 2rem;

  @include media-breakpoint-down(lg) {
    background-image: none;
  }

  &-title {
    font-size: 4rem;
    font-weight: 900;
    color: #181349;
    margin-bottom: 1rem;
  }

  &-wrap {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5rem;
    padding-right: 0;

    @include media-breakpoint-down(lg) {
      padding: 2rem;
    }

    &::before {
      content: '';
      width: 34%;
    }
  }

  &-list {
    display: flex;
    justify-content: center;
    align-items: start;
    gap: 2rem;

    @include media-breakpoint-down(lg) {
      flex-direction: column;
      gap: 0;
    }
  }

  &-item {
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: start;
    font-size: 16px;
    width: 370px;

    i {
      margin-right: 10px;
    }
  }
}

.benefit {

  &-left-item,
  &-right-item {
    display: flex;
    flex-direction: column;
  }
}

.organisation {
  background-color: #f5f5ff;
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;

  &-title {
    text-align: center;

    h2 {
      font-size: 48px;
      font-weight: 700;
      color: #fe4c1c;
    }
  }

  &-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 60px);
    grid-gap: 30px;
    margin: 2rem 0;

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(1, 1fr);
      grid-gap: 2rem;
    }
  }

  &-item {
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: 60px;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    font-size: 16px;

    &:nth-child(3n) {
      margin-right: 0;
    }

    @include media-breakpoint-down(lg) {
      margin-bottom: 0;
    }
  }
}

.cta {
  background-color: #e1ffa9;
  padding: 2rem;

  &-content {
    max-width: 1200px;
    margin: 0 auto;
    background-color: #ffe3e8;
    border-radius: 20px;
    padding: 1.5rem 3.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include media-breakpoint-down(lg) {
      flex-direction: column;
    }
  }

  h2 {
    font-size: 34px;
    color: #18134b;
    font-weight: 700;

    @include media-breakpoint-down(lg) {
      text-align: center;
    }
  }

  p {
    color: #18134b;
    width: 530px;
    font-size: 18px;
    font-style: italic;
    line-height: 1.6;

    @include media-breakpoint-down(lg) {
      width: 100%;
    }
  }

  &-buttons {

    @include media-breakpoint-down(lg) {
      flex-direction: column;
      gap: 1rem;
      width: 100%;
    }

    a {
      height: 50px;
      width: 180px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 600;
      border-radius: 4px;

      @include media-breakpoint-down(lg) {
        width: 100%;
      }
    }

  }
}

.pricing {
  background-color: #e1ffa9;

  .pricing-image {
    position: absolute;
    left: 0;
    top: 0;
    width: 735px;
    height: 375px;
    z-index: 2;
    transition: all 0.3s ease;

    @include media-breakpoint-down(xxxl) {
      width: 660px;
      height: 375px;
    }

    @include media-breakpoint-down(xxl) {
      width: 600px;
      height: 350px;
    }

    @include media-breakpoint-down(xl) {
      width: 500px;
      height: 300px;
    }

    @include media-breakpoint-down(lg) {
      display: none;
    }
  }

  &-wrap {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center;

    @include media-breakpoint-down(lg) {
      flex-direction: column;
      padding: 0 2rem;
    }
  }

  &-title h2 {
    font-size: 48px;
    font-weight: 600;
    color: #181349;
  }

  &-right {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 450px;
    border-bottom: 2px solid #fe4c1c;
    margin-bottom: 2rem;
    height: 375px;

    @include media-breakpoint-down(xxl) {
      height: 350px;
    }

    @include media-breakpoint-down(xl) {
      height: 300px;
    }

    @include media-breakpoint-down(lg) {
      padding-left: 0;
    }

    &::before {
      content: '';
      position: absolute;
      bottom: -5px;
      right: -18px;
      width: 10px;
      height: 10px;
      background-color: #fe4c1c;
      border-radius: 50%;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      right: -34px;
      width: 10px;
      height: 10px;
      background-color: #09deff;
      border-radius: 50%;
    }
  }

  &-content p {
    width: 500px;
    text-align: left;
    font-size: 16px;
    margin: 3rem 0;
    line-height: 30px;

    @include media-breakpoint-down(lg) {
      margin: 1rem 0;
      width: 100%;
    }
  }
}
</style>
