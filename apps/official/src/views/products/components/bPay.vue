<script setup lang="ts">
import divide from '@/components/divide/index.vue'
</script>

<template>
  <div class="b-pay">
    <section class="title">
      <div class="title-wrap">
        <div class="breadcrumb">
          <div>Products</div>
          <i class="pi pi-chevron-right" style="color: #fff;" />
          <div>BPAY</div>
        </div>
        <div class="title-content">
          <div class="content-left">
            <div class="content-left-title">
              BPAY<span class="accent-dot">.</span>
            </div>
            <div class="content-left-description">
              <p>
                BPAY is an service offered by Australia's leading financial
                institutions as a core feature of Internet and phone
                banking. BPAY gives your organisation the option of
                allowing customers to pay your bill at any time, day or
                night, on any day of the year, saving them time and
                reducing the hassle of paying bills.
              </p>
            </div>
            <div>
              <router-link to="/biller-registration" class="product-start-now-button">
                START NOW
              </router-link>
            </div>
          </div>
          <div class="content-right">
            <div>
              <img src="@/assets/official/products/BPAY/bpay-logo.png" alt="bPay" style="width: 300px;">
            </div>
            <i class="pi pi-chevron-right" style="font-size: 5rem; color: #fe4c1c;margin:auto 15px" />
            <div>
              <img
                src="@/assets/official/products/DIRECT DEBIT/direct-debit-icon.png" alt="dollar"
                style="width: 200px;"
              >
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="work">
      <div class="work-title">
        <h2>HOW IT WORKS<span class="question-color">?</span></h2>
      </div>
      <div class="work-list">
        <div class="work-item">
          Bill Buddy issues
          you a range of
          Customer
          Reference Numbers
          (CRN's) to issue to
          your customers.
        </div>
        <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
        <div class="work-item">
          Your customer uses
          their CRN and the Bill
          Buddy BPAY Biller Code
          to make their payment
          from their phone or
          internet banking.
        </div>
        <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
        <div class="work-item">
          Funds are remitted to
          you daily, typically
          two business days
          after the transaction.
        </div>
        <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
        <div class="work-item">
          Comprehensive
          reporting provided
          each day a
          transaction occurs.
        </div>
        <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
        <div class="work-item">
          You have 24 hour
          access to our Biller
          Portal to manage
          customer payments
          and schedules.
        </div>
      </div>
    </section>
    <section class="benefits">
      <div class="benefits-wrap">
        <div>
          <div class="benefits-title">
            Benefits<span class="accent-dot">.</span>
          </div>
          <div class="benefits-list">
            <div class="benefit-left-item">
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />
                A well recognised and trusted
                payment method
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />
                Aids in creating a professional image for
                your organisation
              </div>
            </div>
            <div class="benefit-right-item">
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />
                Improve cash flow by providing an easy
                and convenient payment option.
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;" />
                No customer registration required.
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="organisation">
      <div class="organisation-title">
        <h2>
          Is it suitable for my organisation<span class="question-color">?</span>
        </h2>
      </div>
      <div class="organisation-content">
        <div class="organisation-item text-2xl">
          Organisations that do not want to provide their account information to their customers to make EFT payments.
        </div>
        <div class="organisation-item text-2xl">
          Customers who want to retain control over the timing of their payment.
        </div>
        <div class="organisation-item text-xl">
          Organisations who want the decreased risk associated with the security of their customer having to
          authenticate with their internet or phone banking prior to making a payment.
        </div>
        <div class="organisation-item text-2xl">
          Organisations who want to ensure a meaningful reference is associated with each payment.
        </div>
      </div>
    </section>
    <section class="integration">
      <div class="integration-title">
        <h2>
          Integration<span class="question-color">?</span>
        </h2>
      </div>
      <div class="integration-content">
        <div class="integration-item">
          There is an option of integration with
          Bill Buddy' system for BPAY functions.
        </div>
        <div class="integration-item">
          A full set of easy-implement API
          covers all functions above.
        </div>
        <div class="integration-item">
          Sandbox and test environment
          avalable.
        </div>
      </div>
    </section>
    <section class="pricing relative">
      <img src="@/assets/official/products/illustration.jpg" alt="" class="absolute left-0 top-0 w-[38%] h-[370px] z-2">

      <div class="pricing-wrap">
        <div class="pricing-right">
          <div class="pricing-title">
            <h2>Pricing<span class="accent-dot">.</span></h2>
          </div>
          <div class="pricing-content">
            <p>
              Set up BPAY for your business — without the bank headaches.
            </p>
            <p>
              Become a BPAY Sub-Biller under Bill Buddy’s Master Biller Code with just a small establishment fee. This
              lets you bypass the high setup
              costs and technical hurdles of going directly through a bank.
            </p>
            <p>
              Independent BPAY setup often costs hundreds of dollars and requires you to manage Customer Reference
              Number (CRN) generation.
              With Bill Buddy, setup is simple, and ongoing fees are based on per-transaction pricing — all clearly
              outlined in our Facility Agreement.
            </p>
            <p>
              To access full pricing, just complete the Biller Registration process. You’ll receive a Biller Application
              Kit with everything you need to review
              and approve before getting started.
            </p>
          </div>
        </div>
      </div>
    </section>
    <div style="background-color: #e1ffa9; width: 100%;">
      <divide style="background-color: #e1ffa9; width: 1200px; margin:0 auto" />
    </div>
    <section class="cta">
      <div class="cta-content">
        <div>
          <h2>TALK TO A BIZ DEV MANAGER<span class="accent-dot">.</span></h2>
          <p>
            Have questions? Get expert guidance, talk to a payment expert, and see how Bill Buddy can help
            your
            business.
          </p>
        </div>
        <div class="cta-buttons flex ">
          <router-link
            to="/biller-registration" class="talk-to-us-button register"
          >
            BILLER REGISTRATION
          </router-link>
          <router-link to="/Contact-Us" class="talk-to-us-button request-call">
            REQUEST A CALL
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.b-pay {
  background-color: #f5f5ff;

}

.accent-dot {
  color: #ff5722;
}

.question-color {
  color: #181349;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.title {
  background-color: #181349;
  width: 100%;
}

.title-wrap {
  padding-bottom: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  .content-left-description {
    p {
      color: #fff;
      text-align: left;
      width: 75%;
      margin-top: 2rem;
      margin-bottom: 2rem;
      line-height: 1.6;
      font-size: 16px;

      @include media-breakpoint-down(xl) {
        width: 100%;
      }
    }
  }
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.breadcrumb {
  padding: 5px;
  border-bottom: 1px solid rgba(245, 245, 255, 0.5);
  display: flex;
  justify-content: start;
  align-items: center;
  padding-bottom: 3px;
}

.breadcrumb div {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  margin: 10px 20px;
}

.title-content {
  display: flex;
  justify-content: center;
  max-width: 1200px;

  @include media-breakpoint-down(xl) {
    flex-direction: column;
  }
}

.content-left {
  display: flex;
  flex-direction: column;
  justify-content: start;
  padding: 3rem 20px;
}

.content-left-title {
  color: #e1ffa9;
  font-size: 5rem;
  font-weight: 900;
}

.content-right {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: flex-end;
  margin-left: -6rem;

  @include media-breakpoint-down(xl) {
    margin-left: 0;
    padding: 1rem 2rem;
  }
}

.work {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 20px;

  h2 {
    font-size: 4rem;
    font-weight: 700;
    color: #fe4c1c;

    @include media-breakpoint-down(xl) {
      text-align: center;
    }
  }
}

.work-list {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2rem;

  @include media-breakpoint-down(xl) {
    flex-direction: column;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .work-item {
    background-color: #e1ffa9;
    border-radius: 22px;
    border: 1px solid rgba(179, 179, 178, .8);
    padding: 1rem;
    width: 205px;
    height: 230px;
    line-height: 1.8;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: start;

    @include media-breakpoint-down(xl) {
      width: 100%;
      height: auto;
    }
  }

  .pi-chevron-right {
    @include media-breakpoint-down(lg) {
      margin: 1.5rem auto !important;
      transform: rotate(90deg);
    }
  }
}

.benefits {
  width: 100%;
  background-color: #ffe3e8;
  background-image: url('@/assets/official/products/BPAY/bpay-img.png');
  background-repeat: no-repeat;
  background-position: left bottom;
  background-size: 45% 97%;
  padding: 4rem 0;

  @include media-breakpoint-down(lg) {
    background-image: none;
  }

  &-title {
    font-size: 4rem;
    font-weight: 900;
    color: #181349;
    margin-bottom: 1rem;
  }

  &-wrap {
    max-width: 1350px;
    margin: 0 auto;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 5rem;
    padding-right: 0;

    @include media-breakpoint-down(lg) {
      padding: 0;
    }

  }

  &-list {
    display: flex;
    justify-content: center;
    align-items: start;
    gap: 4rem;

    @include media-breakpoint-down(lg) {
      flex-direction: column;
      gap: 0;
    }
  }

  &-item {
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: start;
    font-size: 16px;
    width: 370px;

    i {
      margin-right: 10px;
    }
  }
}

.benefit {

  &-left-item,
  &-right-item {
    display: flex;
    flex-direction: column;
  }
}

.organisation {
  background-color: #f5f5ff;
  padding: 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.organisation-title h2 {
  font-size: 48px;
  font-weight: 900;
  color: #fe4c1c;
  margin-top: 1.5rem;
  margin-bottom: 3rem;
}

.organisation-title {
  text-align: center;
}

.organisation-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  grid-column-gap: 50px;
  grid-row-gap: 50px;
  margin: 2rem 0 0 0;
  border-bottom: 2px solid #fe4c1c;
  position: relative;
  padding-bottom: 3rem;

  @include media-breakpoint-down(xl) {
    grid-template-columns: repeat(1, 1fr);
  }
}

.organisation-content::before {
  content: '';
  position: absolute;
  bottom: -5px;
  right: -18px;
  width: 10px;
  height: 10px;
  background-color: #fe4c1c;
  border-radius: 50%;
}

.organisation-content::after {
  content: '';
  position: absolute;
  bottom: -5px;
  right: -34px;
  width: 10px;
  height: 10px;
  background-color: #09deff;
  border-radius: 50%;
}

.organisation-item {
  border: 1px solid rgba(179, 179, 178, 0.8);
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  text-align: center;
  padding: 1rem;
  line-height: 1.6;
  /* font-size: 16px; */
  min-height: 120px;
  height: 100%;
}

.integration {
  max-width: 1200px;
  margin: 0 auto;

  .integration-title {
    h2 {
      font-size: 48px;
      font-weight: 900;
      color: #fe4c1c;
      text-align: center;
      margin-top: 1.5rem;
      margin-bottom: 4.5rem;

      @include media-breakpoint-down(xl) {
        margin-bottom: 2rem;
      }
    }
  }

  .integration-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 30px;
    margin-top: 2rem;
    padding-bottom: 5rem;

    @include media-breakpoint-down(xl) {
      padding: 2rem;
      grid-template-columns: repeat(1, 1fr);
    }
  }

  .integration-item {
    border: 1px solid rgba(179, 179, 178, 0.8);
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    font-size: 16px;
    text-align: center;
    padding: 1.5rem;
    min-height: 50px;
    height: 100%;
  }

}

.cta {
  background-color: #e1ffa9;
  padding: 2rem;

  &-content {
    max-width: 1200px;
    margin: 0 auto;
    background-color: #ffe3e8;
    border-radius: 20px;
    padding: 1.5rem 3.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include media-breakpoint-down(lg) {
      flex-direction: column;
    }
  }

  h2 {
    font-size: 34px;
    color: #18134b;
    font-weight: 700;

    @include media-breakpoint-down(lg) {
      text-align: center;
    }
  }

  p {
    color: #18134b;
    width: 530px;
    font-size: 18px;
    font-style: italic;
    line-height: 1.6;

    @include media-breakpoint-down(lg) {
      width: 100%;
    }
  }

  &-buttons {

    @include media-breakpoint-down(lg) {
      flex-direction: column;
      gap: 1rem;
      width: 100%;
    }

    a {
      height: 50px;
      width: 180px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 600;
      border-radius: 4px;

      @include media-breakpoint-down(lg) {
        width: 100%;
      }
    }

  }
}

.pricing {
  background-color: #e1ffa9;

  @include media-breakpoint-down(xl) {
    img {
      display: none;
    }
  }
}

.pricing-wrap {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  padding: 2rem 0;

  .pricing-title h2 {
    font-size: 48px;
    font-weight: 900;
    color: #181349;
  }

  &::before {
    content: '';
    width: 65%;

    @include media-breakpoint-down(xl) {
      display: none;
    }
  }

  .pricing-right {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 100px;
    height: 350px;

    @include media-breakpoint-down(xxl) {
      height: 350px;
    }

    @include media-breakpoint-down(xl) {
      height: 300px;
    }

    @include media-breakpoint-down(lg) {
      padding-left: 0;
    }
  }

  .pricing-content p {
    text-align: left;
    font-size: 16px;
    margin: .8rem 0;
    padding-bottom: 1.25rem;

    @include media-breakpoint-down(xl) {
      width: 100%;
    }
  }

  .pricing-content p:first-child {
    margin-top: 2rem;
  }

  @include media-breakpoint-down(xl) {
    padding: 2rem;
  }
}
</style>
