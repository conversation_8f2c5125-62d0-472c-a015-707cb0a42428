<script setup lang="ts">
import divide from '@/components/divide/index.vue'
</script>

<template>
  <div class="pay-my-invoice">
    <section class="title">
      <div class="title-wrap">
        <div class="breadcrumb">
          <div>Products</div>
          <i class="pi pi-chevron-right" style="color: #fff;" />
          <div>PAYMYINVOICE</div>
        </div>
        <div class="title-content">
          <div class="content-left">
            <div class="content-left-title">
              PAYMYINVOICE<span class="accent-dot">.</span>
            </div>
            <div class="content-left-description">
              <p>
                Make invoice payments easy with a secure online platform.
                Customers can pay anytime via a link in an email or SMS
                using credit/debit cards, bank transfers, or digital wallets.
              </p>
            </div>
            <div>
              <a href="https://www.billbuddyhq.com/biller-registration" class="product-start-now-button">
                REGISTER
              </a>
              <a href="https://www.paymyinvoice.com.au" class="ml-6 btn make-payment">
                MAKE PAYMENT
              </a>
              <a href="https://www.paymyinvoice.com.au" class="btn biller-login">
                MERCHANT LOGIN
              </a>
            </div>
          </div>
          <div class="content-right">
            <div>
              <img
                src="@/assets/official/products/PAYMYINVOICE/pay-my-invoice-logo.png" alt="PAYMYINVOICE"
                style="width: 240px; height: 260px;"
              >
            </div>
            <i class="pi pi-chevron-right" style="font-size: 5rem; color: #fe4c1c;margin:auto 15px" />
            <div>
              <img
                src="@/assets/official/products/DIRECT DEBIT/direct-debit-icon.png" alt="dollar"
                style="width: 200px;"
              >
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="work">
      <div class="work-title">
        <h2>HOW IT WORKS<span class="question-color">?</span></h2>
      </div>
      <div class="work-list">
        <div class="work-item">
          <span class="work-item-title">Invoice Generation:</span>
          <p class="work-item-text">
            Businesses issue
            invoices to their
            customers, including
            a unique identifier or
            reference number.
          </p>
        </div>
        <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
        <div class="work-item">
          <span class="work-item-title">Accessing PayMyInvoice:</span>
          <p class="work-item-text">
            Customers visit the
            PayMyInvoice
            website.
          </p>
        </div>
        <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
        <div class="work-item">
          <span class="work-item-title">
            Entering Payment
            Details:
          </span>
          <p class="work-item-text">
            Customers
            input the required
            information, such

            as the invoice
            number and payment
            amount.
          </p>
        </div>
        <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
        <div class="work-item">
          <span class="work-item-title">Payment Submission:</span>
          <p class="work-item-text">
            Customers provide
            their payment details
            and submit
            the payment through
            the platform.
          </p>
        </div>
        <i class="pi pi-chevron-right" style="font-size: 2rem; color: #fe4c1c;margin:auto 5px" />
        <div class="work-item">
          <span class="work-item-title">Confirmation:</span>
          <p class="work-item-text">
            Upon
            successful payment,
            customers receive a
            confirmation,
            and businesses are
            notified of the
            received payment.
          </p>
        </div>
      </div>
    </section>
    <section class="benefits">
      <div class="benefits-wrap">
        <div>
          <div class="benefits-title">
            Benefits<span class="accent-dot">.</span>
          </div>
          <div class="benefits-list">
            <div class="benefit-left-item">
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;padding-top:5px" />
                <p>
                  <span class="font-semibold">Convenience:</span> Customers can pay invoices
                  online at any time, eliminating the need for
                  in-person payments or mailing checks.
                </p>
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;padding-top:5px" />
                <p>
                  <span class="font-semibold">Security:</span>
                  The platform ensures that all
                  transactions are processed securely, safeguarding sensitive financial information.
                </p>
              </div>
            </div>
            <div class="benefit-right-item">
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;padding-top:5px" />
                <p>
                  <span class="font-semibold">Efficiency:</span>
                  Businesses receive payments
                  promptly, improving cash flow and reducing the time spent on payment collection.
                </p>
              </div>
              <div class="benefits-item">
                <i class="pi pi-circle-fill" style="color: #fe4c1c; font-size: 1.3rem;padding-top:5px" />
                <p>
                  <span class="font-semibold">User-Friendly Interface: </span>
                  The platform is
                  designed for ease of use, making
                  the payment process straightforward for
                  customers.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="pricing">
      <div class="pricing-wrap">
        <div>
          <div class="pricing-title">
            <h2>Pricing<span class="accent-dot">.</span></h2>
          </div>
          <div class="pricing-content">
            <div class="pricing-item">
              <div class="pricing-item-title">
                1.76% + $0.30
              </div>
              <div class="pricing-item-text">
                for domestic VISA / Mastercard cards
              </div>
            </div>
            <div class="pricing-item">
              <div class="pricing-item-title">
                2.97% + $0.30
              </div>
              <div class="pricing-item-text">
                for international VISA / Mastercard cards
              </div>
            </div>
            <div class="pricing-item">
              <div class="pricing-item-title">
                3.96% + $0.30
              </div>
              <div class="pricing-item-text">
                for other cards (e.g. Amex / Diners)
              </div>
            </div>
          </div>
          <div class="text-center mt-8">
            <router-link to="/biller-registration" class="btn  pricing-btn">
              GET STARTED IN MINUTES
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <section class="cta">
      <div class="w-[1200px] m-auto">
        <divide />
      </div>
      <div class="cta-content">
        <div>
          <h2>TALK TO A BIZ DEV MANAGER<span class="accent-dot">.</span></h2>
          <p>
            Have questions? Get expert guidance, talk to a payment expert, and see how Bill Buddy can help
            your
            business.
          </p>
        </div>
        <div class="cta-buttons flex ">
          <router-link to="/biller-registration" class="talk-to-us-button register">
            BILLER REGISTRATION
          </router-link>
          <router-link to="/Contact-Us" class="talk-to-us-button request-call">
            REQUEST A CALL
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.pay-my-invoice {
  background-color: #f5f5ff;
  /* padding: 5px 0; */
}

.accent-dot {
  color: #ff5722;
}

.question-color {
  color: #181349;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 16px;
}

.title {
  background-color: #181349;
  width: 100%;
}

.title-wrap {
  padding-bottom: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.breadcrumb {
  padding: 5px;
  border-bottom: 1px solid rgba(245, 245, 255, 0.5);
  display: flex;
  justify-content: start;
  align-items: center;
  padding-bottom: 3px;
}

.breadcrumb div {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  margin: 10px 20px;
}

.title-content {
  display: flex;
  justify-content: center;
  max-width: 1200px;
}

.content-left {
  display: flex;
  flex-direction: column;
  justify-content: start;
  padding: 3rem 20px;
}

.content-left-title {
  color: #e1ffa9;
  font-size: 5rem;
  font-weight: 700;
}

.content-left-description p {
  color: #fff;
  text-align: left;
  width: 75%;
  margin-top: 2rem;
  margin-bottom: 2rem;
  line-height: 1.6;
  font-size: 18px;
}

.make-payment,
.biller-login {
  color: #fff;
  background-color: var(--colors-warn);
  font-weight: 600;
  width: 180px;
  text-align: center;
  padding: 1.5rem;
  font-size: 16px;
  margin-right: 20px;

  &:hover {
    background-color: hsl(16, 100%, 70%);
  }

  &:active {
    background-color: hsl(16, 100%, 60%);
  }
}

.make-payment {
  background-color: #09deff;
  color: #181349;

  &:hover {
    background-color: rgba(9, 222, 255, 0.7);
  }

  &:active {
    background-color: rgba(9, 222, 255, 0.8);
  }
}

.biller-login {
  background-color: #ffe3e8;
  color: #181349;
}

.content-right {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  /* margin-left: -6rem; */
}

.work {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 20px;
}

.work-title h2 {
  font-size: 4rem;
  font-weight: 900;
  color: #fe4c1c;
}

.work-list {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2rem;
}

.work-item {
  background-color: #e1ffa9;
  border-radius: 22px;
  border: 1px solid rgba(179, 179, 178, 0.8);
  padding: 1.5rem;
  padding-top: 1.25rem;
  width: 220px;
  height: 240px;
  line-height: 1.5;
  display: flex;
  align-items: start;
  flex-direction: column;

  .work-item-title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .work-item-text {
    font-size: 16px;
    font-weight: 400;
  }
}

.benefits {
  width: 100%;
  background-color: #ffe3e8;
  background-image: url('@/assets/official/products/PAYMYINVOICE/pay-my-invoice-img-1.webp');
  background-repeat: no-repeat;
  background-position: left bottom;
  background-size: 46% 97%;
  padding: 2rem;
}

.benefits-title {
  font-size: 4rem;
  font-weight: 900;
  color: #181349;
}

.benefits-wrap {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5rem;
  padding-right: 0;
}

.benefits-wrap::before {
  content: '';
  width: 34%;
}

.benefits-list {
  display: flex;
  justify-content: center;
  align-items: start;
  margin-top: 2rem;
  font-size: 16px;
}

.benefit-left-item,
.benefit-right-item {
  display: flex;
  flex-direction: column;

}

.benefit-left-item {
  margin-right: 30px;
}

.benefits-item {
  height: 55px;
  display: flex;
  align-items: start;
  justify-content: start;
  font-size: 16px;
  width: 370px;
  margin-bottom: 4rem;
}

.benefits-item p {
  line-height: 1.8;
}

.benefits-item i {
  margin-right: 10px;
}

.cta {
  background-color: #e1ffa9;
  padding: 4rem 2rem;

  &-content {
    max-width: 1200px;
    margin: 0 auto;
    background-color: #ffe3e8;
    border-radius: 16px;
    padding: 2.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include media-breakpoint-down(lg) {
      flex-direction: column;
    }
  }

  h2 {
    font-size: 2.5rem;
    color: #18134b;
    margin-bottom: 1rem;
    font-weight: 700;

    @include media-breakpoint-down(lg) {
      text-align: center;
    }
  }

  p {
    color: #18134b;
    margin-bottom: 2rem;
    width: 70%;
    font-size: 16px;
    font-style: italic;
    line-height: 1.6;

    @include media-breakpoint-down(lg) {
      width: 100%;
    }
  }

  &-buttons {

    @include media-breakpoint-down(lg) {
      flex-direction: column;
      gap: 1rem;
      width: 100%;
    }

    a {
      height: 50px;
      width: 240px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;

      @include media-breakpoint-down(lg) {
        width: 100%;
      }
    }

  }
}

.pricing {
  background-color: #e1ffa9;

}

.pricing-wrap {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  padding: 2rem 0;
}

.pricing-title h2 {
  font-size: 48px;
  font-weight: 900;
  color: #181349;
  text-align: center;
}

.pricing-content {
  display: grid;
  /* width: 1200px; */
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 2rem;
}

.pricing-item {
  border: 1px solid rgba(179, 179, 178, 0.8);
  border-radius: 22px;
  padding: 2rem 1rem;
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
}

.pricing-item-title {
  color: #fe4c1c;
  font-size: 42px;
  text-align: center;
  font-weight: 700;
}

.pricing-item-text {
  color: var(--colors-primary);
  text-align: center;
  font-size: 1rem;
  margin-top: 1rem;
}

.pricing-btn {
  background-color: var(--colors-primary);
  color: #fff;
  font-weight: 700;
  font-size: 16px;
  padding: 1.2rem;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(24, 19, 73, 0.7);
  }

  &:active {
    background-color: rgba(24, 19, 73, 0.8);
  }
}
</style>
