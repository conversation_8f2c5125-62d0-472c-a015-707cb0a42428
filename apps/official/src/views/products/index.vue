<script setup lang="ts">

</script>

<template>
  <div class="products">
    <div class="title-img">
      <img src="@/assets/official/bg/ultra_product.webp" alt="banner">
    </div>
    <div class="guide-title">
      <h1>
        Unified Payments<span class="accent-dot">,</span> Secured Transactions<span class="accent-dot">,</span>
        Simplified Experience<span class="accent-dot">.</span>
      </h1>
    </div>
    <section class="guide">
      <div class="text-[18px] w-2/3 m-auto text-center guide-description">
        <p>
          We offer complete payment solutions that ensures secure transactions, flexible payment options,
          and a seamless experience for both businesses and their customers.
        </p>
      </div>
      <div class="guide-content">
        <div class="guide-debit">
          <div class="detail-title flex items-center gap-2">
            <img
              src="@/assets/official/products/DIRECT DEBIT/dd-icon.png" alt="direct debit"
              class="guide-icon"
            ><span>Direct Debit</span>
          </div>
          <div class="detail-content">
            <ul>
              <li>
                Automates recurring payments for subscriptions,
                utilities, and loans.
              </li>
              <li>
                Eliminates late fees and manual transfers for
                businesses and customers.
              </li>
              <li>
                Ensures reliable, scheduled payments with minimal
                effort.
              </li>
            </ul>
          </div>
          <div class="detail-btn">
            <router-link to="/DirectDebit" class="tell-me">
              TELL ME MORE
            </router-link>
          </div>
        </div>
        <div class="guide-webpay">
          <div class="detail-title flex items-center gap-2">
            <div class="w-[22px] h-[22px] bg-[#181349] rounded-[50%] flex justify-center items-center">
              <img src="@/assets/official/products/WEBPAY/webpay-icon.png" alt="webPay" class="h-[13px] w-[16px]">
            </div>
            <span>WebPay</span>
          </div>
          <div class="detail-content">
            <ul>
              <li>
                Enables fast and secure online payments via
                credit/debit card or bank transfer.
              </li>
              <li>
                Provides customers with 24/7 access to pay invoices
                or bills.
              </li>
              <li>
                Ensures a seamless and convenient payment
                experience.
              </li>
            </ul>
          </div>
          <div class="detail-btn">
            <router-link to="/WebPay" class="tell-me">
              TELL ME MORE
            </router-link>
          </div>
        </div>
        <div class="guide-bpay">
          <div class="detail-title flex items-center gap-2">
            <img src="@/assets/official/products/BPAY/BPay.png" alt="webPay" class="guide-icon">
            <span> BPAY</span>
          </div>
          <div class="detail-content">
            <ul>
              <li>
                Allows customers to pay bills directly from their
                bank's online portal.
              </li>
              <li>
                Uses unique BPAY codes for secure and efficient
                transactions.
              </li>
              <li>
                A trusted and widely used payment method in
                Australia.
              </li>
            </ul>
          </div>
          <div class="detail-btn">
            <router-link to="/BPay" class="tell-me">
              TELL ME MORE
            </router-link>
          </div>
        </div>
        <div class="guide-pay">
          <div class="detail-title flex items-center gap-2">
            <div class="w-[22px] h-[22px] bg-[#181349] rounded-[50%] flex justify-center items-baseline">
              <img
                src="@/assets/official/products/PAYMYINVOICE/p-title-icon.png" alt="webPay"
                class="h-[20px] w-[18px]"
              >
            </div>
            PayMyInvoice
          </div>
          <div class="detail-content">
            <ul>
              <li>
                Sends secure payment links via email or SMS for easy
                invoice payments.
              </li>
              <li>
                Supports multiple payment methods, including cards,
                bank transfers, and digital wallets.
              </li>
              <li>Simplifies the billing process for both businesses and customers.</li>
            </ul>
          </div>
          <div class="detail-btn">
            <router-link to="/PayMyInvoice" class="tell-me">
              TELL ME MORE
            </router-link>
          </div>
        </div>
      </div>
    </section>
    <section class="mode">
      <div class="mode-title">
        <h2>Transform the Way You Get Paid<span class="accent-dot">.</span></h2>
      </div>
      <div class="mode-wrap">
        <img src="@/assets/official/portal/Products Image 1.png" alt="">
        <div class="mode-content">
          <div>
            <div class="mode-item">
              <div class="mode-list-decoration">
                <i class="pi pi-star-fill" style="color: #fe4c1c;font-size: 2rem;" />
              </div>
              <p>
                No merchant or banking facilities required other than
                a bank account.
              </p>
            </div>
            <div class="mode-item">
              <div class="mode-list-decoration">
                <i class="pi pi-star-fill" style="color: #fe4c1c;font-size: 2rem;" />
              </div>
              <p>Reduced debtor days by offering multiple payment options.</p>
            </div>
            <div class="mode-item">
              <div class="mode-list-decoration">
                <i class="pi pi-star-fill" style="color: #fe4c1c;font-size: 2rem;" />
              </div>
              <p>
                Lower debtor related administrative costs by offering multiple
                payment options.
              </p>
            </div>
            <div class="mode-item">
              <div class="mode-list-decoration">
                <i class="pi pi-star-fill" style="color: #fe4c1c;font-size: 2rem;" />
              </div>
              <p>
                Secure website in which a business can manage all their
                customer payments.
              </p>
            </div>
            <div class="mode-item">
              <div class="mode-list-decoration">
                <i class="pi pi-star-fill" style="color: #fe4c1c;font-size: 2rem;" />
              </div>
              <p>
                All received customer payments consolidated into one daily
                remittance and report regardless of how the customer paid.
              </p>
            </div>
            <div class="mode-item">
              <div class="mode-list-decoration">
                <i class="pi pi-star-fill" style="color: #fe4c1c;font-size: 2rem;" />
              </div>
              <p>
                Provides an organisation with a more professional image by
                offering multiple payment options.
              </p>
            </div>
            <div class="mode-item">
              <div class="mode-list-decoration">
                <i class="pi pi-star-fill" style="color: #fe4c1c;font-size: 2rem;" />
              </div>
              <p>
                Reduced cost when compared to obtaining and operating
                these products individually from a financial institution.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="cta">
      <div class="cta-content">
        <div>
          <h2>TALK TO A BIZ DEV MANAGER<span class="accent-dot">.</span></h2>
          <p>
            Have questions? Get expert guidance, talk to a payment expert, and see how Bill Buddy can help
            your
            business.
          </p>
        </div>
        <div class="cta-buttons flex">
          <router-link to="/biller-registration" class="talk-to-us-button register">
            BILLER REGISTRATION
          </router-link>
          <router-link to="/Contact-Us" class="talk-to-us-button request-call">
            REQUEST A CALL
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/mixins/breakpoints' as *;

.products {
  width: 100%;
  background-color: #f5f5ff;
}

.title-img {
  img {
    width: 100%;
    height: 400px;
    object-fit: cover;

    @include media-breakpoint-down(md) {
      height: 20vh;
    }

    @include media-breakpoint-down(sm) {
      height: auto;
    }
  }
}

.accent-dot {
  color: #ff5722;
}
.guide-title{
  display: flex;
  justify-content: center;
  padding: 3rem 3rem 0 3rem;
  h1 {
    font-weight: 700;
    color: #181349;
    font-size:3rem;

    @include media-breakpoint-down(lg) {
      font-size: 2rem;
    }

    @include media-breakpoint-down(md) {
      font-size: 1.75rem;
      text-align: center;
    }

    @include media-breakpoint-down(sm) {
      font-size: 1.5rem;
      text-align: center;
    }
  }
}

.guide {
  max-width: 1250px;
  margin: 0 auto;
  padding: 1rem 3rem 3rem 3rem;

  @include media-breakpoint-down(lg) {
    padding: 2rem;
  }

  @include media-breakpoint-down(md) {
    padding: 1.5rem 1rem;
  }

}

.guide-description {
  @include media-breakpoint-down(md) {
    width: 90% !important;
    font-size: 16px !important;
  }

  @include media-breakpoint-down(sm) {
    width: 100% !important;
    font-size: 14px !important;
  }
}

.guide-debit,
.guide-webpay,
.guide-bpay,
.guide-pay {
  border: 1px solid rgba(179, 179, 178, 0.8);
  border-radius: 22px;
  padding: 2rem;
  width: 520px;
  height: 320px;
  margin-bottom: 3rem;
  position: relative;
  background-color: #ffe3e8;

  @include media-breakpoint-down(xl) {
    width: 100%;
    height: auto;
    min-height: 300px;
    padding: 1.5rem;
  }

  @include media-breakpoint-down(md) {
    padding: 1.25rem;
    min-height: 280px;
  }

  @include media-breakpoint-down(sm) {
    margin-bottom: 1.5rem;
    min-height: 270px;
  }
}

.guide-content>div:first-child,
.guide-content>div:last-child {
  background-color: #f5f5ff;
}

.guide-content>div:nth-child(odd) {
  margin-right: 4rem;

  @include media-breakpoint-down(xl) {
    margin-right: 0;
  }
}

.guide-content {
  margin-top: 4rem;
  display: flex;
  flex-wrap: wrap;
  padding: 0 2rem;

  @include media-breakpoint-down(xl) {
    justify-content: space-between;
    gap: 2rem;
  }

  @include media-breakpoint-down(lg) {
    margin-top: 2rem;
  }

  @include media-breakpoint-down(md) {
    flex-direction: column;
    gap: 1.5rem;
  }
}

.detail-title {
  font-size: 16px;
  font-weight: 700;
}

.guide-icon {
  width: 22px;
}

.detail-btn {
  display: inline-block;
  padding: 0.5rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  color: white;
  font-size: 16px;
  background-color: #fe4c1c;
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  transition: all 0.3s ease;
  &:hover {
    background-color: hsl(16, 100%, 70%);
  }

  &:active {
    background-color: hsl(16, 100%, 60%);
  }

  @include media-breakpoint-down(sm) {
    bottom: 1.5rem;
    right: 1.5rem;
    padding: 0.4rem 1.25rem;
    font-size: 14px;
  }
}

.tell-me {
  font-weight: 700;
  font-size: 16px;

  @include media-breakpoint-down(sm) {
    font-size: 14px;
  }
}

.detail-content ul {
  list-style: disc;
  line-height: 2;
  margin-top: 20px;
  padding: 0 20px;
  font-size: 18px;

  @include media-breakpoint-down(lg) {
    font-size: 16px;
    line-height: 1.8;
  }

  @include media-breakpoint-down(sm) {
    font-size: 14px;
    line-height: 1.6;
    margin-top: 12px;
    padding: 0 16px;
  }
}

.cta {
  background-color: #f5f5ff;
  padding: 4rem 2rem;

  @include media-breakpoint-down(lg) {
    padding: 3rem 1.5rem;
  }

  @include media-breakpoint-down(md) {
    padding: 2rem 1rem;
  }
}

.cta-content {
  max-width: 1200px;
  margin: 0 auto;
  background-color: #ffe3e8;
  border-radius: 16px;
  padding: 2.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @include media-breakpoint-down(lg) {
    padding: 2rem;
    flex-direction: column;
    text-align: center;
  }

  @include media-breakpoint-down(md) {
    padding: 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    padding: 1.25rem;
  }
}

.cta h2 {
  font-size: 2.5rem;
  color: #18134b;
  margin-bottom: 1rem;
  font-weight: 700;

  @include media-breakpoint-down(lg) {
    font-size: 2rem;
  }

  @include media-breakpoint-down(md) {
    font-size: 1.75rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 1.5rem;
  }
}

.cta p {
  color: #18134b;
  margin-bottom: 2rem;
  width: 80%;
  font-style: italic;
  font-size: 16px;
  line-height: 1.6;

  @include media-breakpoint-down(lg) {
    width: 100%;
    margin-bottom: 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 14px;
  }
}

.cta-buttons {
  @include media-breakpoint-down(sm) {
    flex-direction: column;
    gap: 1rem;
    align-items: center;

    a {
      margin-right: 0 !important;
    }
  }

  a {
    height: 50px;
    width: 240px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;

    @include media-breakpoint-down(md) {
      height: 45px;
      width: 200px;
      font-size: 14px;
    }
  }
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;

  @include media-breakpoint-down(sm) {
    padding: 0.6rem 1.25rem;
  }
}

.mode {
  height: 900px;
  background-color: #ffe3e8;
  overflow-y: hidden;

  @include media-breakpoint-down(lg) {
    height: auto;
    padding-bottom: 3rem;
  }
}

.mode-wrap {
  position: relative;

  @include media-breakpoint-down(lg) {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

.mode-title h2 {
  font-size: 4rem;
  font-weight: 900;
  text-align: center;
  padding: 3rem;

  @include media-breakpoint-down(lg) {
    font-size: 3rem;
    padding: 2rem;
  }

  @include media-breakpoint-down(md) {
    font-size: 2.5rem;
    padding: 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 2rem;
    padding: 1.25rem;
  }
}

.mode-wrap img {
  position: absolute;
  left: 0;
  bottom: -120px;
  z-index: 1;
  height: 760px;

  @include media-breakpoint-down(xl) {
    height: 660px;
    bottom: -80px;
    display: none;
  }

  @include media-breakpoint-down(lg) {
    position: static;
    height: auto;
    width: 100%;
    max-width: 500px;
    margin-bottom: 2rem;
  }
}

.mode-content {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
  max-width: 1300px;
  margin: 0 auto;

  @include media-breakpoint-down(xxl) {
    margin-right: 24px;
  }

  @include media-breakpoint-down(xl) {
    flex: 1;
    justify-content: center;
    margin-right: 24px;
  }

  @include media-breakpoint-down(lg) {
    flex: 1;
    margin-top: 0;
    justify-content: center;
    margin-right: 24px;
  }

  @include media-breakpoint-down(md) {
    flex: 1;
    padding: 0 1rem;
    justify-content: center;
  }
}

.mode-item {
  background-color: #f5f5ff;
  border-radius: 16px;
  border: 1px solid #ddd;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 620px;
  margin-bottom: 2rem;
  position: relative;
  height: 70px;

  @include media-breakpoint-down(xl) {
    width: 720px;
  }

  @include media-breakpoint-down(lg) {
    width: 520px;
    max-width: 600px
  }

  @include media-breakpoint-down(md) {
    height: auto;
    min-height: 70px;
    padding: 1rem 1rem 1rem 4rem;
    width: 100%;
  }

  @include media-breakpoint-down(sm) {
    max-width: 600px;
    margin-bottom: 1.25rem;
    width: 100%;
  }
}

.mode-list-decoration {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);

  @include media-breakpoint-down(md) {
    left: 10px;
  }

  i {
    @include media-breakpoint-down(sm) {
      font-size: 1.5rem !important;
    }
  }
}

.mode-item p {
  position: absolute;
  left: 65px;
  top: 50%;
  transform: translateY(-50%);
  text-align: left;
  font-size: 16px;

  @include media-breakpoint-down(xl) {
    font-size: 15px;
  }

  @include media-breakpoint-down(md) {
    position: static;
    transform: none;
    font-size: 14px;
    width: 100%;
  }
}
</style>
