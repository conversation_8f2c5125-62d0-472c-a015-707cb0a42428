<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { z } from 'zod'
import divide from '@/components/divide/index.vue'
import GoogleRecaptcha from '@/components/googleRecaptchaV2/index.vue'
import { billerSupportSubmit, submit } from '@/services/api/support'
import { message } from '@/utils/message'
import collapsePanel from '@/views/support/components/collapsePanel.vue'

const searchQuery = ref('')

const route = useRoute()

const faqItems = reactive([
  {
    title: 'How do I get support from <PERSON>?',
    content: `Getting support is easy! Our team is here to help with any questions or issues you may have. You can reach us via:<br />
    <span class="font-semibold">Email: </span> <a style="color: #fe4c1c;" target="_blank" href="mailto:<EMAIL>"><EMAIL></a><br />
    <span class="font-semibold">Phone: </span>1300 434 398 (Mon–Fri, 9am–5pm AEDT)<br />
    <span class="font-semibold">Online: </span>Use the contact form on our website or log in to your portal to raise a support ticket.
    <p>We aim to respond promptly and provide personal, efficient service every step of the way.</p> `,
    isOpen: false,
  },
  {
    title: 'Is Bill <PERSON> Australian Owned?',
    content: 'Yes, Bill Buddy is 100% Australian owned and operated. Our banking facilities are also provided by Australian Banks.',
    isOpen: false,
  },
  {
    title: 'How long has Bill Buddy been operating?',
    content: 'Bill Buddy was established in 2003 and was largely responsible for bringing competitive pricing to the market. Since our establishment our product range has grown to include Direct Debit, WebPay, Pay-by-Phone, and BPAY.',
    isOpen: false,
  },
  {
    title: 'What industries does Bill Buddy provide facilities to?',
    content: 'Our client base covers a diverse range of industries and business sizes. No business is too big or too small for us to service.',
    isOpen: false,
  },
  {
    title: 'Why should I trust Bill Buddy?',
    content: 'Bill Buddy has been operating for over seven years and in that time has built a reputation based on trust, honesty, and reliable service. For the last few years we have been sponsored by Westpac who conducted exhaustive checks on the Company and its Directors prior to providing transactional facilities. Under our arrangements with Westpac we are also required to fully comply with the Bulk Electronic Clearing System (BECS) as governed by the Australian Payments Clearing Association (APCA). All online transactions are 1,024 bit encrypted and our systems and PCI DSS compliant. Bill Buddy also employs continually learning in-house anti-fraud systems to assist in identifying suspicious transactions or activity. The large number of Billers who have been using our services for several years without being locked into a contract is testament to our ability to provide quality services.',
    isOpen: false,
  },
  {
    title: 'Does Bill Buddy report suspicious transactions to the authorities?',
    content: 'Bill Buddy has both a policy and legal obligation of reporting suspicious transactions to the authorities. Bill Buddy is also a reporting entity under the Anti-Money Laundering and Counter-Terrorism Financing Act (Cth) 2006.',
    isOpen: false,
  },
  {
    title: 'How can I be assured that my information is kept safe?',
    content: 'Bill Buddy fully complies with the requirements of the Privacy Act and the National Privacy Principles. We make no differentiation between our personal and business customers, either. We treat all the information you give us as confidential. We will only make use of it to carry out our processes and procedures on your behalf, as required under the Privacy Act or in compliance with the requirements of the various government agencies that we work with. To avoid confusion, Bill Buddy must (from time to time) provide information to various banking bodies to ensure correct transactions are made and resolve any errors. These are standard procedures for payment transaction businesses and we may do this without specific reference to any other party.',
    isOpen: false,
  },
])

const billerList = reactive([
  {
    title: 'How soon after submitting my application can I be using Bill Buddys facilities?',
    content: 'If your application is completed fully it will typically be processed within one business day with successful applicants being able to use our facilities immediately after approval. If your application is urgent please bring this to our attention when you submit it. Applicants who have been approved for the use of BPAY will need to wait up to 10 business days to be established as a BPAY Sub-Biller in the banking systems.',
    isOpen: false,
  },
  {
    title: 'How long after a transaction occurs will I receive the funds?',
    content: 'Bill Buddy has daily remittances that typically occur on the second business day after the transaction. Be aware that some of our competitors hold the funds for longer periods before remitting them to you and only remit once a week.',
    isOpen: false,
  },
  {
    title: 'Will I be locked into a contract?',
    content: 'No, Bill Buddy Billers are not locked into any fixed term contracts.',
    isOpen: false,
  },
  {
    title: 'Can I change the amounts and dates for my customer payments?',
    content: 'Yes. Billers have access to our secure Biller Portal where they can manage their customer payments including changing the amounts and dates of payments. Ad-hoc or one-off payments can also be created using this portal. Billers can of course also call our office to make changes to customer payments.',
    isOpen: false,
  },
  {
    title: 'What reporting is provided to me?',
    content: `Bill Buddy offers comprehensive, real-time reporting via the Biller Portal, as well as email-based updates. You'll have access to customer payment statuses, payment and remittance histories, detailed breakdowns of each remittance, and downloadable CSV files.
              At no extra cost, you can also opt in to receive SMS notifications whenever a remittance is made to your account.`,
    isOpen: false,
  },
  {
    title: 'Can I use authority forms branded with my logo?',
    content: 'Yes, if you provide us with your logo Bill Buddy can provide a branded version of our Direct Debit Authority Form.',
    isOpen: false,
  },
  {
    title: 'Can the transaction related fees be passed on to my customer?',
    content: `Yes, Bill Buddy's system is capable of adding the transaction related fees to the transaction amount. This of course should only be done with your customers consent.`,
    isOpen: false,
  },
])

const payerList = reactive([
  {
    title: 'How can I change the date or amount of my next payment?',
    content: 'To change the date or amount of your next payment you need to contact your Biller. Your Biller is the organisation that is providing you with the product or service that the transaction relates to. If the Biller approves your request to alter the payment amount or date they will advise Bill Buddy or make the update online. Please note that changes to the amount or date cannot be made on the day the payment is due.',
    isOpen: false,
  },
  {
    title: 'How can I cancel the direct debit authority I have provided?',
    content: 'To cancel the direct debit authority you must notify us in writing at least 1 week prior to the next payment due date. Please note that cancelling your direct debit authority does not necessarily cancel any contractual arrangements you have with your Biller and you may still have monies owing to them.',
    isOpen: false,
  },
  {
    title: 'Who do I contact if I have a query about the product/service that was paid for using Bill Buddy?',
    content: 'Queries relating to the product or service supplied should be directed to your Biller. Your Biller is the organisation that is providing you with the product or service that the transaction relates to. If you believe your Biller has acted fraudulently or been misleading please notify us.',
    isOpen: false,
  },
  {
    title: 'Who do I contact if I have a query about the amount I was charged via Bill Buddy?',
    content: 'Transactions conducted on behalf of Billers by Bill Buddy are done under the instruction of the Billers; therefore you should contact your Biller if you have a question regarding the amount of a transaction. Please be aware that in some instances your Biller may have instructed Bill Buddy to add its transaction related fees to the amount of the transaction. Your Biller is the organisation that is providing you with the product or service that the transaction relates to.',
    isOpen: false,
  },
  {
    title: 'How do I update my bank account/credit details previously provided to Bill Buddy?',
    content: 'To change the account details for a direct debit you will need to complete a new Direct Debit Authority. To arrange for this simply contact Bill Buddy or your Biller.',
    isOpen: false,
  },
  {
    title: 'When will my account be debited for a schedule payment?',
    content: 'Your accoun will be debited on the scheduled date if that date is a business day. If the scheduled date falls on a weekend or national public holiday your account will be debited the next business day after the scheduled date. The transaction on your account may occur at any time of the day or night on the effective scheduled date and it is your responsibility to ensure you have sufficient funds in your account to honour the debit.',
    isOpen: false,
  },
  {
    title: 'How do I dispute a transaction that Bill Buddy is associated with?',
    content: `If you want to dispute a transaction you should firstly contact your Biller to discuss your concerns with them. If you are unable to contact your Biller or do not know what the transaction is for please contact Bill Buddy. If you are not satisfied with Bill Buddy's decision regarding your dispute you should contact your financial institution and follow their transaction disputes process. Please note that a dispute should only be lodged with your financial institution if you have been unable to resolve the issue directly with your Biller or Bill Buddy.`,
    isOpen: false,
  },
  {
    title: 'How do I find out what the charge from Bill Buddy on my credit card statement is for?',
    content: `Bill Buddy conducts transactions for many Australian businesses across many industries. If you see "Bill Buddy" on your statement and aren't sure what the transaction is for please visit the Transaction Enquiry page in the Support section at www.billbuddy.com.au.`,
    isOpen: false,
  },
  {
    title: 'Where I can find the direct debit service agreement?',
    content: `<p>By signing our Direct Debit Request you acknowledge and agree to the following terms and conditions:
</p><br>
                            <ol style="list-style-type: decimal; list-style-position: inside;">
								<li>You authorise Bill Buddy to debit your nominated account in the manner specified by your Biller. Your Biller is the
organisation providing you with the product or service for which we are debiting your account.</li>
<li>We will provide you with at least 14 days prior notice in writing if we propose to vary any of the terms of the debit
arrangements in place between us.</li>
<li>You should contact your Biller if you wish to defer or alter any of the debit arrangements.</li>
<li>You will need to advise us in writing if you wish to cancel a Direct Debit Request. Such notice should be delivered
to us at least one working day before the due date for payment or as otherwise stipulated in our Terms and
Conditions.</li>
<li>If you wish to dispute any Debit Item you should refer to us in the first instance and we will seek to resolve the
matter with you. If we cannot resolve the dispute you can contact your financial institution at which your nominated
account is held. Your financial institution will then commence a formal claims procedure on your behalf.</li>
<li>Some financial institution accounts do not facilitate direct debits. If you are uncertain, you should check with your
financial institution before signing a Direct Debit Request, to ensure that your nominated account is able to receive
direct debits through the Bulk Electronic Clearing System.</li>
<li>Before completing the Direct Debit Request, you should check the details of your nominated account against a
recent statement from your financial institution, to ensure that your account details are correct.</li>
<li>You agree that it is your responsibility to have sufficient cleared funds in your nominated account by the due date
to enable payment of Debit Items in accordance with the directions of your Biller.</li>
<li>We will initiate the Debit Item on the due date as advised by your Biller. If the due date for payment falls on a day
which is not a business day in Queensland, then the Debit Item will be processed on the next business day. You
should enquire directly with your financial institution if you are uncertain as to when the Debit Item will be processed
to your account.</li>
<li>If a Debit Item is returned unpaid by your financial institution, you authorise us to present a further debit for
payment. Furthermore you authorise Bill Buddy to debit your account for our Dishonour Charge.</li>
<li>We will ensure the details of your personal records and account details held by us remain confidential. However,
if you lodge a claim in relation to an alleged incorrect or wrongful debit, it may be necessary for us to release such
information to your financial institution or its representative, or to our financial institution or its representative to
enable your claim to be assessed. Further, we will share certain personal information with our contracted agents for
statistical purposes only. This does NOT include your bank account or credit card details.</li>
							</ol>
                        </div>`,
    isOpen: false,
  },
])

const transactionList = reactive([
  {
    title: 'Why is Bill Buddy showing on my credit card statement?',
    content: `<p class="mb-4">
              Bill Buddy is a payment facilitator for thousands of Australian businesses.
            </p>
            <p class="font-semibold mb-4">
              If you see something similar to "Bill Buddy" on your credit card statement, chances are this
              transaction is for a product
              or service you have purchased from one of our Billers.
            </p>
            <p class="mb-4">
              <span class="font-semibold">
                To find out who the Biller associated with the transaction is please go to our
                <span class="underline">Check
                  Payment page</span>.
              </span>
              Note that to enable
              us to locate your transaction you will be asked to enter the first four and last three
              digits of your credit card along with
              the exact amount of the transaction in Australian dollars.
            </p>
            <p class="mb-4">
              If you wish to discuss the transaction further you should contact the Biller for
              whom we facilitated the transaction.
            </p>`,
    isOpen: false,
  },
  {
    title: 'Why is Bill Buddy debiting my bank account?',
    content: `<p class="mb-4">
              Bill Buddy is a payment facilitator for thousands of Australian businesses.
            </p>
            <p class="font-semibold mb-4">
              If you see something similar to "Bill Buddy" on your credit card statement, chances are this
              transaction is for a product
              or service you have purchased from one of our Billers.
            </p>
            <p class="mb-4">
              <span class="font-semibold">
                To find out who the Biller associated with the transaction is please go to our
                <span class="underline">Check
                  Payment page</span>.
              </span>
              Note that to enable
              us to locate your transaction you will be asked to enter the first four and last three
              digits of your credit card along with
              the exact amount of the transaction in Australian dollars.
            </p>
            <p class="mb-4">
              If you wish to discuss the transaction further you should contact the Biller for
              whom we facilitated the transaction.
            </p>
          </collapsePanel>
          <collapsePanel title="Why is Bill Buddy debiting my bank account?">
            <p class="font-semibold mb-4">
              Bill Buddy will only debit your bank account if a Direct Debit
              Authority has been provided.
            </p>
            <p class="mb-4">
              Usually the transaction will appear on your statement along with the name of the
              organisation that has provided you
              with the products/services. This is the organisation we are debiting on behalf of. You
              should contact them with any
              questions regarding the amount or timing of your debit.
            </p>
            <p class="mb-4">
              The other reason a direct debit may take place on your account is if a previous debit
              dishonoured. In this case we may
              charge a failed payment fee as per our DDR Service Agreement and this is usually charged in
              the form of debiting that
              fee from your account.
            </p>`,
    isOpen: false,
  },
])

const billerVisible = ref(false)
const noticeVisible = ref(false)
const payerVisible = ref(false)
const isRead = ref(false)

const payerForm = reactive({
  google_token: '',
  ddr_id_or_crn: '',
  customer_name: '',
  contact_name: '',
  phone: '',
  contact_email: '',
  support_request: '',
  recaptcha: false,
})

const billerForm = reactive({
  google_token: '',
  biller_id: '',
  organisation_name: '',
  name: '',
  phone: '',
  email: '',
  support_request: '',
  recaptcha: false,
})

const validationSchema = toTypedSchema(z.object({
  ddr_id_or_crn: z.string().min(1, { message: 'DDR ID/CRN is required.' }),
  customer_name: z.string().min(1, { message: 'Customer Name is required.' }),
  contact_name: z.string().min(1, { message: 'Contact Name is required.' }),
  contact_email: z.string().min(1, { message: 'Contact Email is required.' }).email({ message: 'PLEASE ENTER A VALID EMAIL ADDRESS' }),
  phone: z.string().min(1, { message: 'Phone is required.' }).regex(/^\d+(-\d+)*$/, { message: 'PLEASE ENTER VALID PHONE NUMBER' }),
  support_request: z.string().min(1, { message: 'Support Request is required.' }),
  google_token: z.string({
    required_error: 'Please complete the reCAPTCHA verification',
  }),
}))

const billerValidationSchema = toTypedSchema(z.object({
  biller_id: z.string().min(1, { message: 'Biller ID is required.' }),
  organisation_name: z.string().min(1, { message: 'Organisation Name is required.' }),
  name: z.string().min(1, { message: 'Name is required.' }),
  email: z.string().min(1, { message: 'Email is required.' }).email({ message: 'PLEASE ENTER A VALID EMAIL ADDRESS' }),
  phone: z.string().min(1, { message: 'Phone is required.' }).regex(/^\d+(-\d+)*$/, { message: 'PLEASE ENTER VALID PHONE NUMBER' }),
  support_request: z.string().min(1, { message: 'Support Request is required.' }),
  google_token: z.string({
    required_error: 'Please complete the reCAPTCHA verification',
  }),
}))

// reCAPTCHA refs and state
const recaptchaRef = ref<InstanceType<typeof GoogleRecaptcha> | null>(null)
const recaptchaVerified = ref(false)

// reCAPTCHA handlers
const onRecaptchaVerify = (response: string) => {
  if (response) {
    if (billerVisible.value) {
      billerForm.google_token = response
    }
    else if (payerVisible.value) {
      payerForm.google_token = response
    }
    recaptchaVerified.value = true
  }
}

const onRecaptchaExpired = () => {
  recaptchaVerified.value = false
}

const onRecaptchaError = () => {
  recaptchaVerified.value = false
}

const payerFormRef = ref()
const billerFormRef = ref()
const isLoading = ref(false)

const billerSubmit = async (values: Record<string, any>) => {
  if (!recaptchaVerified.value) {
    billerForm.recaptcha = false
  }

  if (!values.google_token) {
    message({
      message: 'Please complete the CAPTCHA verification.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
    return
  }
  isLoading.value = true

  try {
    const sendData = {
      google_token: values.google_token,
      biller_id: values.biller_id,
      name: values.name,
      organisation_name: values.organisation_name,
      phone: values.phone,
      email: values.email,
      support_request: values.support_request,
    }
    const res = await billerSupportSubmit(sendData)
    if (res.code === 0) {
      message({
        message: 'Thank you for your submission! We"ll get in touch with you SHORTLY.',
        type: 'success',
        duration: 3000,
        closable: false,
      })
      billerVisible.value = false
      billerForm.google_token = ''
      recaptchaVerified.value = false
    }
  }
  catch {
    message({
      message: 'form submission failed. please review information & try again.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
  }
  finally {
    isLoading.value = false
  }
}
const payerSubmit = async (values: Record<string, any>) => {
  if (!isRead.value) {
    message({
      message: 'Please read the clause.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
    return
  }
  if (!recaptchaVerified.value) {
    payerForm.recaptcha = false
  }

  if (!values.google_token) {
    message({
      message: 'Please complete the CAPTCHA verification.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
    return
  }
  isLoading.value = true

  try {
    const sendData = {
      google_token: values.google_token,
      ddr_id_or_crn: values.ddr_id_or_crn,
      contact_name: values.contact_name,
      customer_name: values.customer_name,
      phone: values.phone,
      contact_email: values.contact_email,
      support_request: values.support_request,
    }
    const res = await submit(sendData)
    if (res.code === 0) {
      message({
        message: 'Thank you for your submission! We"ll get in touch with you SHORTLY.',
        type: 'success',
        duration: 3000,
        closable: false,
      })
      payerVisible.value = false
      payerForm.google_token = ''
      recaptchaVerified.value = false
    }
  }
  catch {
    message({
      message: 'form submission failed. please review information & try again.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
  }
  finally {
    isLoading.value = false
  }
}
const scrollToItem = async (scrollId: string) => {
  if (!scrollId) { return }

  setTimeout(() => {
    document.getElementById(scrollId)?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }, 0)
}

watch(() => route.hash, () => {
  const hash = route.hash.slice(1)
  if (hash) {
    scrollToItem(hash)
  }
}, {
  immediate: true,
})

const isCollapseItem = reactive({
  information: true,
  billerSupport: true,
  payerSupport: true,
})
const viewMore = (item: string) => {
  switch (item) {
    case 'information':
      isCollapseItem.information = !isCollapseItem.information
      break
    case 'biller-support':
      isCollapseItem.billerSupport = !isCollapseItem.billerSupport
      break
    case 'payer-support':
      isCollapseItem.payerSupport = !isCollapseItem.payerSupport
      break
  }
}
const collapseInformation = computed(() => {
  if (isCollapseItem.information) {
    return faqItems.slice(0, 4)
  }
  return faqItems
})

const collapseBiller = computed(() => {
  if (isCollapseItem.billerSupport) {
    return billerList.slice(0, 4)
  }
  return billerList
})

const collapsePayer = computed(() => {
  if (isCollapseItem.payerSupport) {
    return payerList.slice(0, 4)
  }
  return payerList
})

const allCollapse = computed(() => {
  return [...faqItems, ...billerList, ...payerList, ...transactionList]
})

// 可以考虑对搜索进行防抖处理
const debouncedSearch = ref('')
let debounceTimer: ReturnType<typeof setTimeout> | null = null

watch(searchQuery, (newVal) => {
  if (debounceTimer) { clearTimeout(debounceTimer) }
  debounceTimer = setTimeout(() => {
    debouncedSearch.value = newVal
  }, 300) // 300毫秒延迟
})

// 然后在filteredQuestions中使用debouncedSearch代替searchQuery
const filteredQuestions = computed(() => {
  if (!debouncedSearch.value.trim()) { return [] }

  const query = debouncedSearch.value.toLowerCase().trim()
  return allCollapse.value.filter(item =>
    item.title.toLowerCase().includes(query)
    || (item.content && item.content.toLowerCase().includes(query)),
  ).slice(0, 8)
})

// 获取内容预览，移除HTML标签并限制长度
const getContentPreview = (content: string) => {
  if (!content) { return '' }

  // 移除HTML标签
  const textContent = content.replace(/<[^>]*>/g, ' ')

  // 限制长度并添加省略号
  const maxLength = 300
  if (textContent.length > maxLength) {
    return `${textContent.slice(0, maxLength)}...`
  }

  return textContent
}

// 添加一个ref用于存储当前高亮的问题标题
const highlightedQuestion = ref('')

// 点击问题项后跳转到对应位置
const navigateToQuestion = (item: any) => {
  searchQuery.value = ''

  // 确定问题所属的类别和需要展开的状态
  let section = ''
  let needToExpand = false

  if (faqItems.includes(item)) {
    section = 'supportDivide'
    // 检查是否需要展开更多项目
    needToExpand = isCollapseItem.information && !collapseInformation.value.includes(item)
    if (needToExpand) {
      isCollapseItem.information = false
    }
    // 设置当前项为打开状态
    faqItems.forEach((faqItem) => {
      faqItem.isOpen = faqItem === item
    })
  }
  else if (billerList.includes(item)) {
    section = 'billerDivide'
    needToExpand = isCollapseItem.billerSupport && !collapseBiller.value.includes(item)
    if (needToExpand) {
      isCollapseItem.billerSupport = false
    }
    // 设置当前项为打开状态
    billerList.forEach((billerItem) => {
      billerItem.isOpen = billerItem === item
    })
  }
  else if (payerList.includes(item)) {
    section = 'payerDivide'
    needToExpand = isCollapseItem.payerSupport && !collapsePayer.value.includes(item)
    if (needToExpand) {
      isCollapseItem.payerSupport = false
    }
    // 设置当前项为打开状态
    payerList.forEach((payerItem) => {
      payerItem.isOpen = payerItem === item
    })
  }
  else {
    section = 'enquiryDivide'
  }

  // 设置高亮问题标题
  highlightedQuestion.value = item.title

  // 跳转到对应区域
  scrollToItem(section)

  // 3秒后移除高亮效果
  setTimeout(() => {
    highlightedQuestion.value = ''
  }, 1000 * 3)
}
</script>

<template>
  <div class="support">
    <div id="startDivide" class="title-img" />
    <div class="search">
      <div class="search-container">
        <div class="search-input-wrapper">
          <input v-model="searchQuery" type="text" placeholder="What are you looking for?" class="search-input">
          <div v-if="filteredQuestions.length > 0" class="search-dropdown">
            <div
              v-for="(item, index) in filteredQuestions" :key="index" class="search-dropdown-item"
              @click="navigateToQuestion(item)"
            >
              <div class="search-item-title">
                {{ item.title }}
              </div>
              <div class="search-item-content" v-html="getContentPreview(item.content)" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <section id="supportDivide" class="information">
      <div class="support-title">
        <h1>General Inquiries and Company Information<span class="accent-dot">.</span></h1>
      </div>
      <div class="panel-container">
        <div class="border border-[#fe4c1c]">
          <collapsePanel
            v-for="(item, index) in collapseInformation" :key="index"
            v-model:is-open="collapseInformation[index].isOpen" :title="item.title"
            :is-last-item="index === collapseInformation.length - 1"
            :class="{ 'highlight-panel': highlightedQuestion === item.title }"
          >
            <p class="leading-8" v-html="item.content" />
          </collapsePanel>
        </div>
      </div>
      <div class="view-more">
        <button class="support-btn" @click="viewMore('information')">
          {{ isCollapseItem.information ? 'VIEW MORE' : 'VIEW LESS' }}
        </button>
      </div>
    </section>
    <divide class="support-divide" />

    <section id="billerDivide" class="biller">
      <div class="support-title">
        <div>
          <h1>Biller Support<span class="accent-dot">.</span></h1>
        </div>
        <div class="support-help-container">
          <span class="italic text-xl">Need more help?</span>
          <button class="support-btn request" @click="billerVisible = true">
            REQUEST SUPPORT
          </button>
        </div>
      </div>
      <div class="panel-container">
        <div class="border border-[#fe4c1c]">
          <collapsePanel
            v-for="(item, index) in collapseBiller" :key="index"
            v-model:is-open="collapseBiller[index].isOpen" :title="item.title"
            :is-last-item="index === collapseBiller.length - 1"
            :class="{ 'highlight-panel': highlightedQuestion === item.title }"
          >
            <p class="leading-8 whitespace-pre-line">
              {{ item.content }}
            </p>
          </collapsePanel>
        </div>
      </div>
      <div class="view-more">
        <button class="support-btn " @click="viewMore('biller-support')">
          {{ isCollapseItem.billerSupport ? 'VIEW MORE' : 'VIEW LESS' }}
        </button>
      </div>
    </section>

    <divide class="support-divide" />

    <section id="payerDivide" class="payer">
      <div class="support-title">
        <div>
          <h1>Payer Support<span class="accent-dot">.</span></h1>
        </div>
        <div class="support-help-container">
          <span class="italic text-xl">Need more help?</span>
          <button class="support-btn request" @click="noticeVisible = true">
            REQUEST SUPPORT
          </button>
        </div>
      </div>
      <!-- <div class="italic my-8">
        If you want to query a Bill Buddy transaction on your bank account or credit card statement,
        <span class="underline">click here.</span>
      </div> -->
      <div class="panel-container">
        <div class="border border-[#fe4c1c]">
          <collapsePanel
            v-for="(item, index) in collapsePayer" :key="index"
            v-model:is-open="collapsePayer[index].isOpen" :title="item.title"
            :is-last-item="index === collapsePayer.length - 1"
            :class="{ 'highlight-panel': highlightedQuestion === item.title }"
          >
            <p class="leading-8" v-html="item.content" />
          </collapsePanel>
        </div>
      </div>
      <div class="view-more">
        <button class="support-btn " @click="viewMore('payer-support')">
          {{ isCollapseItem.payerSupport ? 'VIEW MORE' : 'VIEW LESS' }}
        </button>
      </div>
    </section>
    <divide class="support-divide" />
    <section id="enquiryDivide" class="enquiry">
      <div class="support-title">
        <div>
          <h1>Transaction Enquiry<span class="accent-dot">.</span></h1>
        </div>
      </div>
      <div class="panel-container">
        <div class="border border-[#fe4c1c]">
          <collapsePanel
            v-for="(item, index) in transactionList" :key="index" v-model:is-open="item.isOpen"
            :title="item.title" :class="{ 'highlight-panel': highlightedQuestion === item.title }"
          >
            <div v-html="item.content" />
          </collapsePanel>
        </div>
      </div>
    </section>
    <section class="cta">
      <div class="cta-content">
        <div class="cta-text">
          <h2>TALK TO A BIZ DEV MANAGER<span class="accent-dot">.</span></h2>
          <p>
            Have questions? Get expert guidance, talk to a payment expert, and see how Bill Buddy can help your
            business.
          </p>
        </div>
        <div class="cta-buttons">
          <router-link to="/biller-registration" class="talk-to-us-button register">
            BILLER REGISTRATION
          </router-link>
          <router-link to="/Contact-Us" class="talk-to-us-button request-call">
            REQUEST A CALL
          </router-link>
        </div>
      </div>
    </section>
    <div class="dialog">
      <Dialog v-model:visible="billerVisible" :modal="true" style="border:none;" pt:mask:class="backdrop-blur-sm">
        <template #container="{ closeCallback }">
          <div class="w-[750px]  p-10 bg-[#ffe3e8] text-[#181349] rounded-2xl font-medium font-medium">
            <div class="header flex justify-between items-center border-b-2 border-[#acb8c0] pb-4 ">
              <div class="font-semibold text-[2rem]">
                Request Biller Support
              </div>
              <i
                class="pi pi-times" style="color: #fe4c1c;font-weight: 700;font-size: 1.5rem;"
                @click="closeCallback"
              />
            </div>
            <ScrollPanel style="width: 100%; height: 600px">
              <div class="description mt-8 mb-16">
                <p class="mb-6">
                  If you are a registered Bill Buddy Biller requiring support please
                  complete
                  the
                  following form:
                </p>
                <p class="italic">
                  If you are a customer (payer) of a business who uses Bill Buddy please go to the
                  Payer Support page.
                </p>
              </div>
              <VeeForm ref="billerFormRef" :validation-schema="billerValidationSchema" @submit="billerSubmit">
                <div class="form grid ">
                  <Field v-slot="{ errorMessage }" v-model="billerForm.biller_id" name="biller_id">
                    <div class="flex justify-between w-full">
                      <div class="flex flex-col w-2/3">
                        <InputText
                          v-model="billerForm.biller_id" class="custom-input col-span-3" name="biller_id"
                          type="text" placeholder="*Biller ID"
                        />

                        <Message
                          v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small"
                          variant="simple"
                        >
                          <div class="flex items-center">
                            <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                            <span class="text-[#ff3131]">
                              {{
                                errorMessage }}
                            </span>
                          </div>
                        </Message>
                      </div>

                      <div class="pt-2 w-46">
                        Please input either ID
                        or organisation name.
                      </div>
                    </div>
                  </Field>
                  <Field v-slot="{ errorMessage }" v-model="billerForm.organisation_name" name="organisation_name">
                    <InputText
                      v-model="billerForm.organisation_name" class="custom-input" name="organisation_name"
                      type="text" placeholder="*Organisation Name"
                    />

                    <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                      <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                        <span class="text-[#ff3131]">
                          {{
                            errorMessage }}
                        </span>
                      </div>
                    </Message>
                  </Field>
                  <Field v-slot="{ errorMessage }" v-model="billerForm.name" name="name">
                    <InputText
                      v-model="billerForm.name" class="custom-input" name="name" type="text"
                      placeholder="*Name"
                    />

                    <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                      <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                        <span class="text-[#ff3131]">
                          {{
                            errorMessage }}
                        </span>
                      </div>
                    </Message>
                  </Field>
                  <Field v-slot="{ errorMessage }" v-model="billerForm.phone" name="phone">
                    <InputText
                      v-model="billerForm.phone" class="custom-input" name="phone" type="text"
                      placeholder="*Phone"
                    />

                    <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                      <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                        <span class="text-[#ff3131]">
                          {{
                            errorMessage }}
                        </span>
                      </div>
                    </Message>
                  </Field>
                  <Field v-slot="{ errorMessage }" v-model="billerForm.email" name="email">
                    <InputText
                      v-model="billerForm.email" class="custom-input" name="email" type="text"
                      placeholder="*Email"
                    />

                    <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                      <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                        <span class="text-[#ff3131]">
                          {{
                            errorMessage }}
                        </span>
                      </div>
                    </Message>
                  </Field>
                </div>
                <div class="custom-textarea">
                  <div class="font-semibold mb-2">
                    *Support Request
                  </div>
                  <Field v-slot="{ errorMessage }" v-model="billerForm.support_request" name="support_request">
                    <Textarea v-model="billerForm.support_request" rows="12" cols="76" />
                    <Message v-if="errorMessage" class="ml-4 mt-2 mb-4" severity="error" size="small" variant="simple">
                      <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                        <span class="text-[#ff3131]">
                          {{
                            errorMessage }}
                        </span>
                      </div>
                    </Message>
                  </Field>
                </div>
                <div class="my-4">
                  <div class="font-semibold mb-2">
                    *CAPTCHA
                  </div>
                  <Field v-slot="{ errorMessage }" v-model="billerForm.google_token" name="google_token">
                    <div class="field mb-4">
                      <GoogleRecaptcha
                        ref="recaptchaRef" class="mb-2" @verify="onRecaptchaVerify"
                        @expired="onRecaptchaExpired" @error="onRecaptchaError"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>
                </div>
                <div class="flex justify-between items-center">
                  <button
                    class="btn btn-secondary mt-2 cursor-pointer" severity="warn" :disabled="isLoading"
                    type="submit"
                  >
                    SUBMIT
                  </button>
                  <div class="w-2/3 italic leading-6">
                    Thank you for your submission.
                    <br>
                    We’ll get in touch with you within 2 business days.
                  </div>
                </div>
              </VeeForm>
            </ScrollPanel>
          </div>
        </template>
      </Dialog>
      <Dialog v-model:visible="noticeVisible" :modal="true" style="border:none;" pt:mask:class="backdrop-blur-sm">
        <template #container="{ closeCallback }">
          <div class="w-[650px]  p-10 bg-[#ffe3e8] text-[#181349] rounded-2xl font-medium">
            <div class="header flex justify-between items-center border-b-2 border-[#acb8c0] pb-4 ">
              <div class="font-semibold text-[2rem]">
                NOTICE
              </div>
              <i
                class="pi pi-times" style="color: #fe4c1c;font-weight: 700;font-size: 1.5rem;"
                @click="closeCallback"
              />
            </div>
            <div class="notice pr-4">
              <p class="mb-6  mt-10">
                Looking for answers? Check our FAQ first!
              </p>
              <p class="mb-6">
                Before submitting a support request, please review the Customer (Payer) Related
                Information section in our FAQ.
              </p>
              <p class="mb-6">
                If you still need assistance, click PROCEED to fill out the Payer Support form.
              </p>
            </div>
            <div>
              <button class="btn btn-secondary mt-2" @click="() => { payerVisible = true; noticeVisible = false }">
                PROCEED
              </button>
            </div>
          </div>
        </template>
      </Dialog>
      <Dialog v-model:visible="payerVisible" :modal="true" style="border:none;" pt:mask:class="backdrop-blur-sm">
        <template #container="{ closeCallback }">
          <div class="w-[750px]  p-10 bg-[#ffe3e8] text-[#181349] rounded-2xl font-medium">
            <div class="header flex justify-between items-center border-b-2 border-[#acb8c0] pb-4 ">
              <div class="font-semibold text-[2rem]">
                Request Payer Support
              </div>
              <i
                class="pi pi-times" style="color: #fe4c1c;font-weight: 700;font-size: 1.5rem;"
                @click="closeCallback"
              />
            </div>
            <ScrollPanel style="width: 100%; height: 600px">
              <div class="description mt-8 mb-8">
                <p class="mb-6">
                  Please fill out the form below:
                </p>
              </div>
              <VeeForm ref="payerFormRef" :validation-schema="validationSchema" @submit="payerSubmit">
                <div class="form grid ">
                  <Field v-slot="{ errorMessage }" v-model="payerForm.ddr_id_or_crn" name="ddr_id_or_crn">
                    <InputText
                      v-model="payerForm.ddr_id_or_crn" class="custom-input" name="ddr_id_or_crn" type="text"
                      placeholder="*DDR ID/CRN (if known)"
                    />

                    <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                      <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                        <span class="text-[#ff3131]">
                          {{
                            errorMessage }}
                        </span>
                      </div>
                    </Message>
                  </Field>
                  <Field v-slot="{ errorMessage }" v-model="payerForm.customer_name" name="customer_name">
                    <InputText
                      v-model="payerForm.customer_name" class="custom-input" name="customer_name" type="text"
                      placeholder="*Customer Name"
                    />

                    <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                      <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                        <span class="text-[#ff3131]">
                          {{
                            errorMessage }}
                        </span>
                      </div>
                    </Message>
                  </Field>
                  <Field v-slot="{ errorMessage }" v-model="payerForm.contact_name" name="contact_name">
                    <InputText
                      v-model="payerForm.contact_name" class="custom-input" name="contact_name" type="text"
                      placeholder="*Contact Name"
                    />

                    <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                      <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                        <span class="text-[#ff3131]">
                          {{
                            errorMessage }}
                        </span>
                      </div>
                    </Message>
                  </Field>
                  <Field v-slot="{ errorMessage }" v-model="payerForm.phone" name="phone">
                    <InputText
                      v-model="payerForm.phone" class="custom-input" name="phone" type="text"
                      placeholder="*Phone"
                    />

                    <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                      <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                        <span class="text-[#ff3131]">
                          {{
                            errorMessage }}
                        </span>
                      </div>
                    </Message>
                  </Field>
                  <Field v-slot="{ errorMessage }" v-model="payerForm.contact_email" name="contact_email">
                    <InputText
                      v-model="payerForm.contact_email" class="custom-input" name="contact_email" type="text"
                      placeholder="*Contact Email"
                    />

                    <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                      <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                        <span class="text-[#ff3131]">
                          {{
                            errorMessage }}
                        </span>
                      </div>
                    </Message>
                  </Field>
                </div>
                <div class="custom-textarea">
                  <div class="font-semibold mb-2">
                    *Support Request
                  </div>
                  <Field v-slot="{ errorMessage }" v-model="payerForm.support_request" name="support_request">
                    <Textarea v-model="payerForm.support_request" rows="12" cols="76" />
                    <Message v-if="errorMessage" class="ml-4 mt-2 mb-4" severity="error" size="small" variant="simple">
                      <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                        <span class="text-[#ff3131]">
                          {{
                            errorMessage }}
                        </span>
                      </div>
                    </Message>
                  </Field>
                </div>
                <div class="read flex items-start mt-6">
                  <Checkbox v-model="isRead" class="custom-checkbox" binary />
                  <label class="ml-4 leading-6">
                    <p>
                      I have read the Customer FAQ and acknowledge that customer support is
                      limited to existing customers of Bill Buddy. Any queries relating to the product
                      or service paid for via Bill Buddy should be directed to the organization who
                      was responsible for the supply of that product/service. If you believe that
                      organization is acting dishonestly or fraudulently please let us know.*
                    </p>
                  </label>
                </div>
                <div class="my-8">
                  <div class="font-semibold mb-2">
                    *CAPTCHA
                  </div>
                  <Field v-slot="{ errorMessage }" v-model="payerForm.google_token" name="google_token">
                    <div class="field mb-4">
                      <GoogleRecaptcha
                        ref="recaptchaRef" class="mb-2" @verify="onRecaptchaVerify"
                        @expired="onRecaptchaExpired" @error="onRecaptchaError"
                      />
                      <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                        {{ errorMessage }}
                      </Message>
                    </div>
                  </Field>
                </div>
                <div class="flex justify-between items-center">
                  <Button class="btn btn-secondary mt-2" :disabled="isLoading" type="submit">
                    SUBMIT
                  </Button>
                  <div class="w-2/3 italic leading-6">
                    Thank you for your submission.
                    <br>
                    We’ll get in touch with you within 2 business days.
                  </div>
                </div>
              </VeeForm>
            </ScrollPanel>
          </div>
        </template>
      </Dialog>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.support {
  width: 100%;
  background-color: #f5f5ff;

}

.title-img {
  margin: 0 auto;
  height: 400px;
  width: 100vw;
  background: url('@/assets/official/bg/ultra_SUPPORT-1.webp') no-repeat;
  background-size: 100% 100%;
  padding-top: 1rem;

  @include media-breakpoint-down(md) {
    height: 20vh;
  }

  @include media-breakpoint-down(sm) {
    height: 20vh;
  }
}

.accent-dot {
  color: #ff5722;
}

.support-divide {
  max-width: 1200px;
  margin: 2rem auto;

  @include media-breakpoint-down(xxl) {
    max-width: 1100px;
  }

  @include media-breakpoint-down(xl) {
    max-width: 920px;
  }

  @include media-breakpoint-down(lg) {
    max-width: 720px;
  }

  @include media-breakpoint-down(md) {
    max-width: 540px;
    margin: 1.5rem auto;
  }

  @include media-breakpoint-down(sm) {
    max-width: 96%;
    margin: 2rem auto;
  }
}

.search {
  max-width: 1200px;
  margin: 2rem auto;
  margin-top: 4rem;

  @include media-breakpoint-down(xxl) {
    max-width: 1100px;
  }

  @include media-breakpoint-down(xl) {
    max-width: 920px;
  }

  @include media-breakpoint-down(lg) {
    max-width: 720px;
  }

  @include media-breakpoint-down(md) {
    max-width: 540px;
    margin: 1.5rem auto;
  }

  @include media-breakpoint-down(sm) {
    max-width: 100%;
    padding: 0 1rem;
  }
}

.search-container {
  display: flex;
  gap: 30px;
  margin-bottom: 24px;

  @include media-breakpoint-down(md) {
    flex-direction: column;
    gap: 15px;
  }
}

.search-input-wrapper {
  position: relative;
  width: 100%;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #282456;
  border-radius: 16px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.2s;
  background-color: var(--colors-white);
  width: 100%;
}

.search-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  width: 100%;
  background-color: #fff;
  border: 2px solid #fe4c1c;
  border-radius: 16px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 10;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  color: #181349;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #ffe3e8;
  }
}

.search-item-title {
  font-weight: 700;
  margin-bottom: 4px;
}

.search-item-content {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.search-input:focus {
  border-color: #fe4c1c;
}

.information,
.biller,
.payer,
.enquiry {
  max-width: 1200px;
  margin: 0 auto;
  text-align: left;

  @include media-breakpoint-down(xxl) {
    max-width: 1100px;
  }

  @include media-breakpoint-down(xl) {
    max-width: 920px;
  }

  @include media-breakpoint-down(lg) {
    max-width: 720px;
  }

  @include media-breakpoint-down(md) {
    max-width: 540px;
    margin: 1.5rem auto;
  }

  @include media-breakpoint-down(sm) {
    max-width: 100%;
    padding: 0 1rem;
  }
}

.support-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;

  @include media-breakpoint-down(md) {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
  }

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    align-items: flex-start;

    div:first-child {
      margin-bottom: 0.5rem;
    }
  }
}

.support-title h1 {
  font-size: 42px;
  font-weight: 900;
  margin: 0 0 0.5rem 0;

  @include media-breakpoint-down(lg) {
    font-size: 36px;
  }

  @include media-breakpoint-down(md) {
    font-size: 30px;
    margin-bottom: 0;
  }

  @include media-breakpoint-down(sm) {
    font-size: 24px;
  }
}

.view-more {
  display: flex;
  justify-content: center;
  font-size: 16px;
  margin-top: 2rem;
}

.support-btn {
  display: inline-block;
  padding: 1rem;
  background-color: #e1ffa9;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 700;
  color: #18134b;
  width: 220px;
  cursor: pointer;
  transition: all 0.3s ease;
  &:hover {
    background-color: rgba(225, 255, 169, 0.5);
  }

  &:active {
    background-color: rgba(225, 255, 169, 1);
  }

  @include media-breakpoint-down(sm) {
    width: 100%;
    font-size: 14px;
  }
}

.request {
  background-color: #fe4c1c;
  color: #fff;
  margin-left: 25px;
  transition: all 0.3s ease;

  &:hover {
    background-color: hsl(16, 100%, 70%);
  }

  &:active {
    background-color: hsl(16, 100%, 60%);
  }

  @include media-breakpoint-down(md) {
    margin-left: 0.5rem;
    width: auto;
    min-width: 160px;
    padding: 0.75rem 1rem;
    white-space: nowrap;
  }

  @include media-breakpoint-down(sm) {
    font-size: 13px;
    min-width: auto;
    width: auto;
    padding: 0.5rem 0.75rem;
    margin-left: 0;
  }
}

.panel-container {
  max-width: 1200px;
  margin: 0 auto;

  @include media-breakpoint-down(xxl) {
    max-width: 1100px;
  }

  @include media-breakpoint-down(xl) {
    max-width: 920px;
  }

  @include media-breakpoint-down(lg) {
    max-width: 720px;
  }

  @include media-breakpoint-down(md) {
    max-width: 540px;
    margin: 1.5rem auto;
  }

  @include media-breakpoint-down(sm) {
    max-width: 100%;
  }
}

.cta {
  background-color: #f5f5ff;
  padding: 4rem 2rem;

  @include media-breakpoint-down(md) {
    padding: 2rem 1rem;
  }

  @include media-breakpoint-down(sm) {
    padding: 1.5rem 1rem;
  }
}

.cta-content {
  max-width: 1200px;
  margin: 0 auto;
  background-color: #ffe3e8;
  border-radius: 16px;
  padding: 3rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @include media-breakpoint-down(lg) {
    max-width: 540px;
    flex-direction: column;
    gap: 2rem;
    text-align: center;
    padding: 2rem 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    max-width: 100%;
    padding: 1.5rem 1rem;
  }
}

.cta h2 {
  font-size: 2.5rem;
  color: #18134b;
  margin-bottom: 1rem;
  font-weight: 700;

  @include media-breakpoint-down(lg) {
    font-size: 2rem;
  }

  @include media-breakpoint-down(md) {
    font-size: 1.75rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
    line-height: 1.3;
  }
}

.cta p {
  color: #18134b;
  margin-bottom: 2rem;
  font-style: italic;
  width: 70%;
  line-height: 1.6;
  font-size: 16px;

  @include media-breakpoint-down(lg) {
    width: 100%;
    margin-bottom: 1rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 14px;
    margin-bottom: 0.5rem;
  }
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 700;
  transition: all 0.3s ease;
  font-size: 16px;
}

.cta-text {
  @include media-breakpoint-down(md) {
    width: 100%;
  }
}

.cta-buttons {
  display: flex;

  @include media-breakpoint-down(md) {
    flex-direction: column;
    width: 100%;
    gap: 1rem;
  }
}

.btn-secondary {
  background-color: #fe4c1c;
  color: #fff;
  font-weight: 700;
  width: 170px;
  height: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  --p-button-primary-hover-background: #fe4c1c;
  --p-button-primary-border-color: #fe4c1c;
  --p-button-primary-hover-border-color: #fe4c1c;

  @include media-breakpoint-down(sm) {
    width: 100%;
    font-size: 14px;
  }
}

.custom-input {
  height: 45px;
  border: 2px solid #181349;
  border-radius: 20px;
  padding-left: 30px;
  font-size: 1.2rem;
  margin-bottom: 1.5rem;

  @include media-breakpoint-down(sm) {
    font-size: 1rem;
    padding-left: 20px;
  }
}

:deep(.p-inputtext::placeholder) {
  color: #b3b8ca;
  font-weight: 600;
}

:deep(.p-textarea) {
  border: 2px solid #181349;

  @include media-breakpoint-down(md) {
    width: 100% !important;
  }
}

// 添加一个新的样式类用于帮助文本和按钮的容器
.support-help-container {
  display: flex;
  align-items: center;
  gap: 1rem;

  @include media-breakpoint-down(md) {
    flex-wrap: nowrap;
  }

  @include media-breakpoint-down(sm) {
    width: 100%;
    justify-content: space-between;
    gap: 0.5rem;
  }

  span {
    @include media-breakpoint-down(sm) {
      font-size: 14px;
    }
  }
}

/* 闪烁动画效果 */
@keyframes highlight-pulse {
  0% {
    background-color: transparent;
  }

  25% {
    background-color: rgba(254, 76, 28, 0.3);
  }

  50% {
    background-color: transparent;
  }

  75% {
    background-color: rgba(254, 76, 28, 0.3);
  }

  100% {
    background-color: transparent;
  }
}

:deep(.highlight-panel) {
  animation: highlight-pulse 3s ease-in-out;
}
</style>
