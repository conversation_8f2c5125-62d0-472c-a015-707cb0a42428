<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  isLastItem: {
    type: Boolean,
    default: false,
  },
})

const isOpen = defineModel<boolean>('isOpen', { required: false, default: false, type: <PERSON>olean })

const changeOpen = () => {
  isOpen.value = !isOpen.value
}
</script>

<template>
  <div class="panel" :class="{ 'last-item': isLastItem }">
    <div class="panel-header" @click="changeOpen">
      <div class="panel-header-left">
        <Transition name="icon-fade" mode="out-in">
          <i v-if="isOpen" key="minus" class="pi pi-minus panel-icon" />
          <i v-else key="plus" class="pi pi-plus panel-icon" />
        </Transition>
        <span class="panel-title">{{ props.title }}</span>
      </div>
      <!-- 右侧插槽 -->
      <div class="panel-header-right" @click.stop>
        <slot name="header-right" :is-open="isOpen" />
      </div>
    </div>
    <div class="panel-content" :class="{ 'panel-content-expanded': isOpen }">
      <div class="panel-content-inner">
        <slot />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.panel {
  border-bottom: 1px solid #e5e7eb;
}

.panel.last-item {
  border-bottom: none;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  cursor: pointer;
  user-select: none;

  @include media-breakpoint-down(md) {
    padding: 12px;
  }

  @include media-breakpoint-down(sm) {
    padding: 10px 8px;
  }
}

.panel-header-left {
  display: flex;
  align-items: center;
  gap: 12px;

  @include media-breakpoint-down(sm) {
    gap: 8px;
  }
}

.panel-header-right {
  margin-left: auto;
}

.panel-title {
  font-weight: 700;
  color: #181349;
  font-size: 16px;

  @include media-breakpoint-down(lg) {
    font-size: 15px;
  }

  @include media-breakpoint-down(md) {
    font-size: 14px;
  }
}

.panel-icon {
  color: #fe4c1c;
  font-weight: 700;
  font-size: 24px;

  @include media-breakpoint-down(md) {
    font-size: 20px;
  }
}

.panel-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease;
  will-change: max-height;
}

.panel-content-expanded {
  max-height: 2000px; /* 设置一个足够大的值 */
}

.panel-content-inner {
  padding: 0 6rem 16px 48px;
  color: #181349;
  font-size: 16px;

  @include media-breakpoint-down(lg) {
    padding: 0 3rem 16px 48px;
  }

  @include media-breakpoint-down(md) {
    padding: 0 1.5rem 16px 40px;
    font-size: 14px;
  }

  @include media-breakpoint-down(sm) {
    padding: 0 0.5rem 16px 36px;
    font-size: 14px;
    line-height: 1.4;
  }
}

/* 图标过渡动画 */
.icon-fade-enter-active,
.icon-fade-leave-active {
  transition: opacity 0.2s;
}

.icon-fade-enter-from,
.icon-fade-leave-to {
  opacity: 0;
}
</style>
