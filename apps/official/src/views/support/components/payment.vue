<script setup lang="ts">

</script>

<template>
  <div class="payment">
    <div class="payment-header">
      <div class="left" />
      <div class="right" />
      <div class="content">
        <h1>Check Payment Made<span class="accent-dot">.</span></h1>
      </div>
    </div>
    <div class="payment-content">
      <div class="tips">
        <div class="first">
          <p>
            You may find on your credit card statement the entries of
            payment a payment to:
          </p>
          <p class="font-semibold">
            "BBY*BusinessName"
          </p>
          <p>
            To find the details of who this payment is for, you need to
            enter the following details.
          </p>
        </div>
        <div class="second">
          <img src="@/assets/official/cut-payment.png" alt="">
        </div>
      </div>
      <div class="payment-form">
        <div>
          <h2>Fill out the form<span class="accent-dot">.</span></h2>
        </div>
        <div class="form-detail">
          <InputText
            class="custom-input" name="first" type="text"
            placeholder="*The first 6 digits of your credit card number"
          />
          <InputText
            class="custom-input" name="last" type="text"
            placeholder="*The last 3 digits of your credit card number"
          />
          <InputText
            class="custom-input" name="amount" type="text"
            placeholder="*The exact amount of the transaction in AUD"
          />
          <div class="captcha">
            <div class="captcha-title">
              *CAPTCHA
            </div>
          </div>
          <div class="payment-btn">
            <button to="/biller-registration" class="btn btn-secondary">
              SEARCH
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/mixins/breakpoints' as *;

.payment {
    width: 100%;
    background-color: #f5f5ff;
    padding-bottom: 4rem;

    @include media-breakpoint-down(md) {
        padding-bottom: 2rem;
    }
}

.accent-dot {
    color: #ff5722;
}

.payment-header {
    position: relative;
    background-color: #19164b;
    color: #e1ffa9;
    padding: 130px 20px;

    @include media-breakpoint-down(lg) {
        padding: 100px 20px;
    }

    @include media-breakpoint-down(md) {
        padding: 80px 20px;
    }

    @include media-breakpoint-down(sm) {
        padding: 60px 20px;
    }
}

.content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 100%;
}

.content h1 {
    font-size: 4rem;
    font-weight: 700;

    @include media-breakpoint-down(lg) {
        font-size: 3.5rem;
    }

    @include media-breakpoint-down(md) {
        font-size: 2.5rem;
    }

    @include media-breakpoint-down(sm) {
        font-size: 2rem;
    }
}

.right::before {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.21;
    background-image: url('@/assets/official/bg/ultra_Contact BG.png');
    background-repeat: no-repeat;
    background-size: 100% 160%;
    background-position: top;
}

.payment-content {
    max-width: 1300px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 4rem;

    @include media-breakpoint-down(lg) {
        max-width: 940px;
        padding: 3rem 2rem;
    }

    @include media-breakpoint-down(md) {
        flex-direction: column;
        padding: 2rem 1.5rem;
        max-width: 100%;
    }

    @include media-breakpoint-down(sm) {
        padding: 1.5rem 1rem;
    }
}

.tips {
    margin-right: 2rem;
    width: 580px;

    @include media-breakpoint-down(lg) {
        width: 45%;
    }

    @include media-breakpoint-down(md) {
        width: 100%;
        margin-right: 0;
        margin-bottom: 2rem;
    }
}

.first {
    color: #19164b;
    background-color: #ffe3e8;
    border: 1px solid #ddd;
    border-radius: 16px;
    padding: 1.5rem;
    font-size: 16px;
    margin-bottom: 1.5rem;

    @include media-breakpoint-down(sm) {
        padding: 1rem;
        font-size: 14px;
    }
}

.first p:nth-child(2) {
    margin: 2rem 0;

    @include media-breakpoint-down(sm) {
        margin: 1rem 0;
    }
}

.second {
    background-color: #ffe3e8;
    border: 1px solid #ddd;
    border-radius: 16px;
    padding: 0.5rem;
    font-size: 16px;

    img {
        width: 100%;
        height: auto;
    }
}

.payment-form {
    width: 700px;
    padding: 0 1.5rem;

    @include media-breakpoint-down(lg) {
        width: 50%;
    }

    @include media-breakpoint-down(md) {
        width: 100%;
        padding: 0;
    }
}

.payment-form h2 {
    font-size: 3rem;
    font-weight: 700;
    padding: 1rem;

    @include media-breakpoint-down(lg) {
        font-size: 2.5rem;
    }

    @include media-breakpoint-down(md) {
        font-size: 2rem;
        padding: 0.5rem;
    }

    @include media-breakpoint-down(sm) {
        font-size: 1.75rem;
    }
}

.form-detail {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    line-height: 1.6;
    border: 1px solid #ddd;
    border-radius: 16px;
    padding: 3rem 2rem;

    @include media-breakpoint-down(md) {
        padding: 2rem 1.5rem;
    }

    @include media-breakpoint-down(sm) {
        padding: 1.5rem 1rem;
    }
}

.payment-btn {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;

    @include media-breakpoint-down(sm) {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary {
    background-color: #fe4c1c;
    color: #fff;
    font-weight: 600;
    width: 170px;
    height: 3.5rem;
    display: flex;
    justify-content: center;
    align-items: center;

    @include media-breakpoint-down(sm) {
        width: 100%;
    }
}

.custom-input {
    height: 45px;
    border: 2px solid #181349;
    border-radius: 20px;
    padding-left: 30px;
    font-size: 1.2rem;
    margin-bottom: 2rem;

    @include media-breakpoint-down(md) {
        font-size: 1rem;
    }

    @include media-breakpoint-down(sm) {
        padding-left: 20px;
        margin-bottom: 1.5rem;
    }
}

:deep(.p-inputtext::placeholder) {
    color: #b3b8ca;
    font-weight: 600;
}

.captcha-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 3rem;

    @include media-breakpoint-down(md) {
        font-size: 1.25rem;
        margin-bottom: 2rem;
    }

    @include media-breakpoint-down(sm) {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }
}
</style>
