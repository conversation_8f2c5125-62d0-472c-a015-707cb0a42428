<script setup lang="ts">
import type { DictItem } from '@/services/api/dict'
import divide from '@/components/divide/index.vue'
import GoogleRecaptcha from '@/components/googleRecaptchaV2/index.vue'
import { submitFeedback, submitMessage } from '@/services/official/contact'
import { useDictStore } from '@/store/modules/dict'
import { message } from '@/utils/message'
import { toTypedSchema } from '@vee-validate/zod'
import { Field, Form as VeeForm } from 'vee-validate'
import { onMounted, reactive, ref } from 'vue'
import { z } from 'zod'

// import { Loader } from '@googlemaps/js-api-loader'
// import { onMounted, ref } from 'vue'

// const mapRef = ref<any>(null)

// const initMap = async () => {
//   const loader = new Loader({
//     apiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
//     version: 'weekly',
//     libraries: ['places', 'marker'],
//   })

//   try {
//     await loader.importLibrary('maps').then(({ Map }) => {
//       mapRef.value = new Map(document.getElementById('map') as HTMLElement, {
//         center: { lat: -37.8005294, lng: 144.9932422 },
//         zoom: 15,
//       })
//     })

//     // await loader.importLibrary('marker').then((markerLib) => {
//     //   const { AdvancedMarkerElement } = markerLib
//     //   void new AdvancedMarkerElement({
//     //     map: mapRef.value,
//     //     position: { lat: -37.8005294, lng: 144.9932422 },
//     //     title: 'Bill Buddy',
//     //   })
//     // })
//   }
//   catch (error) {
//     console.error('加载Google Maps失败:', error)
//   }
// }

// onMounted(() => {
//   initMap()
// })

const { getDictByType } = useDictStore()

interface Dict {
  categoryOptions: DictItem[]
  productOptions: DictItem[]
  feedbackOptions: DictItem[]
  customerOptions: DictItem[]
}

const messageFormRef = ref()
const feedbackFormRef = ref()

const recaptchaRef = ref<InstanceType<typeof GoogleRecaptcha> | null>(null)
const recaptchaFeedbackRef = ref<InstanceType<typeof GoogleRecaptcha> | null>(null)
const recaptchaVerified = ref(false)

// reCAPTCHA handlers
const onRecaptchaVerify = (response: string) => {
  if (response) {
    messageForm.google_token = response
    recaptchaVerified.value = true
  }
}
const onRecaptchaVerifyFeedback = (response: string) => {
  if (response) {
    feedbackForm.google_token = response
    recaptchaVerified.value = true
  }
}

const onRecaptchaExpired = () => {
  recaptchaVerified.value = false
}

const onRecaptchaError = () => {
  recaptchaVerified.value = false
}

const messageForm = reactive({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  companyName: '',
  request_category: null,
  enquiry: '',
  product: '',
  message: '',
  google_token: '',
  recaptcha: false,
})

const feedbackForm = reactive({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  companyName: '',
  customer: '',
  landline: '',
  feedbackType: '',
  message: '',
  google_token: '',
  recaptcha: false,
})
const validationSchema = toTypedSchema(z.object({
  firstName: z.string().min(1, { message: 'First Name is required.' }),
  lastName: z.string().min(1, { message: 'Last Name is required.' }),
  companyName: z.string().min(1, { message: 'Company Name is required.' }),
  email: z.string().min(1, { message: 'Email is required.' }).email({ message: 'PLEASE ENTER A VALID EMAIL ADDRESS' }),
  phone: z.string().min(1, { message: 'Phone is required.' }).regex(/^\d+(-\d+)*$/, { message: 'PLEASE ENTER VALID PHONE NUMBER' }),
  request_category: z.union([z.string(), z.number()]).refine(value => value !== undefined && value !== null && value !== '', {
    message: 'Request Category is required.',
  }),
  product: z.string().optional(),
  enquiry: z.string().min(1, { message: 'Enquiry is required.' }),
  message: z.string().min(1, { message: 'Message is required.' }),
  google_token: z.string({
    required_error: 'Please complete the reCAPTCHA verification',
  }),
}))
const validationFeedbackSchema = toTypedSchema(z.object({
  firstName: z.string().min(1, { message: 'First Name is required.' }),
  lastName: z.string().min(1, { message: 'Last Name is required.' }),
  companyName: z.string().min(1, { message: 'Company Name is required.' }),
  email: z.string().min(1, { message: 'Email is required.' }).email({ message: 'PLEASE ENTER A VALID EMAIL ADDRESS' }),
  phone: z.string().min(1, { message: 'Phone is required.' }).regex(/^\d+(-\d+)*$/, { message: 'PLEASE ENTER VALID PHONE NUMBER' }),
  feedbackType: z.string().min(1, { message: 'Feedback Type is required.' }),
  customer: z.string().min(1, { message: 'Bill Buddy Customer is required.' }),
  landline: z.string().optional(),
  message: z.string().min(1, { message: 'Message is required.' }),
  google_token: z.string({
    required_error: 'Please complete the reCAPTCHA verification',
  }),

}))
const messageSubmit = async (value: any) => {
  if (!recaptchaVerified.value) {
    messageForm.recaptcha = false
  }
  if (!value.google_token) {
    message({
      message: 'Please complete the CAPTCHA verification.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
    return
  }
  try {
    const sendData = {
      first_name: value.firstName,
      last_name: value.lastName,
      email: value.email,
      phone: value.phone,
      company_name: value.companyName,
      request_category: String(value.request_category),
      enquiry: value.enquiry,
      product: value.product,
      message: value.message,
      google_token: value.google_token,
    }

    const res = await submitMessage(sendData)
    if (res.code === 0) {
      message({
        message: 'Thank you for your submission! We"ll get in touch with you SHORTLY.',
        type: 'success',
        duration: 3000,
        closable: false,
      })
      messageForm.google_token = ''
      recaptchaVerified.value = false
      recaptchaRef.value?.reset()
    }
  }
  catch {
    message({
      message: 'form submission failed. please review information & try again.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
  }
}
const feedbackSubmit = async (value: any) => {
  if (!recaptchaVerified.value) {
    feedbackForm.recaptcha = false
  }
  if (!value.google_token) {
    message({
      message: 'Please complete the CAPTCHA verification.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
    return
  }
  try {
    const sendData = {
      customer_name: value.customer,
      first_name: value.firstName,
      last_name: value.lastName,
      email: value.email,
      phone: value.phone,
      company_name: value.companyName,
      feedback_type: value.feedbackType,
      land_line: value.landline,
      message: value.message,
      google_token: value.google_token,
    }
    const res = await submitFeedback(sendData)
    if (res.code === 0) {
      message({
        message: 'Thank you for your submission! We"ll get in touch with you SHORTLY.',
        type: 'success',
        duration: 3000,
        closable: false,
      })
      feedbackForm.google_token = ''
      recaptchaVerified.value = false
      recaptchaRef.value?.reset()
    }
  }
  catch {
    message({
      message: 'form submission failed. please review information & try again.',
      type: 'error',
      duration: 3000,
      closable: false,
    })
  }
}

const dicts = ref<Dict>({
  categoryOptions: [],
  productOptions: [
    { label: 'Direct Debit', value: 'DIRECT DEBIT' },
    { label: 'WebPay', value: 'WEBPAY' },
    { label: 'BPAY', value: 'BPAY' },
    { label: 'PayMyInvoice', value: 'PAYMYINVOICE' },
  ],
  feedbackOptions: [],
  customerOptions: [],
},
)
const contactMessage = ref()
const scrollToMessage = () => {
  if (!contactMessage.value) { return }
  setTimeout(() => {
    contactMessage.value?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }, 0)
}

onMounted(() => {
  Promise.all([
    getDictByType('official_request_category').then((res) => {
      dicts.value.categoryOptions = res.map(item => ({
        label: item.label,
        value: item.label,
      }))
    }),
    getDictByType('official_feedback_type').then((res) => {
      dicts.value.feedbackOptions = res.map(item => ({
        label: item.label,
        value: item.label,
      }))
    }),
    getDictByType('official_feedback_customer').then((res) => {
      dicts.value.customerOptions = res.map(item => ({
        label: item.label,
        value: item.label,
      }))
    }),
  ])
})
</script>

<template>
  <div class="contact">
    <div class="contact-header">
      <div class="left" />
      <div class="right" />
      <div class="content">
        <h1>Contact Us<span class="accent-dot">.</span></h1>
        <div class="flex justify-center gap-8 btn-group">
          <router-link class="btn btn-primary btn-register " to="/biller-registration">
            BILLER REGISTRATION
          </router-link>
          <!-- <router-link to="/Contact-Us" class="btn btn-primary btn-book">
            BOOK A CALL
          </router-link> -->
          <a class="btn btn-primary btn-book cursor-pointer" @click="scrollToMessage">
            BOOK A CALL
          </a>
        </div>
      </div>
    </div>
    <div class="contact-content container">
      <!-- <div id="map" class="map" />
      <div class="contact-information">
        <div class="information-title">
          <h2>Got Questions<span class="accent-dot">?</span></h2>
        </div>
        <div class="information-detail">
          <span class="contact-item">
            <strong>Email:</strong> <a
              href="mailto:<EMAIL>"
              class="underline text-[#181349]"
            ><EMAIL></a>
          </span>
          <span class="contact-item">
            <strong>Website:</strong>
            <a href="https://www.billbuddy.com.au" target="_blank" rel="noopener" class="underline text-[#181349]">
              www.billbuddy.com.au
            </a>
          </span>
        </div>
        <div class="contact-btn">
          <router-link to="/biller-registration" class="btn btn-primary">
            BILLER SUPPORT
          </router-link>
          <router-link to="/book-call" class="btn btn-secondary">
            PAYER SUPPORT
          </router-link>
        </div>
        <divide />
        <div class="address">
          <span>
            222 Hoddle Street,
          </span>
          <span>
            Abbotsford VIC 3067,
          </span>
          <span>
            Australia.
          </span>
        </div>
      </div> -->
      <div class="information">
        <div class="information-item">
          <div class="item-icon">
            <img src="@/assets/official/contact-us/Email.png" alt="">
          </div>
          <div class="flex flex-col justify-center text-center gap-2 mt-4">
            <span class="item-title whitespace-nowrap">Send an Email</span>
            <span class="item-content">
              <a href="mailto:<EMAIL>" class=" text-[#181349]"><EMAIL></a>
            </span>
          </div>
        </div>
        <div class="information-item">
          <div class="item-icon">
            <img src="@/assets/official/contact-us/Phone.png" alt="">
          </div>
          <div class="flex flex-col justify-center text-center gap-2 mt-4">
            <span class="item-title whitespace-nowrap">Give us a Call</span>
            <span class="item-content">1300 434 398</span>
          </div>
        </div>
        <div class="information-item">
          <div class="item-icon ">
            <img src="@/assets/official/contact-us/Location.png" alt="">
          </div>
          <div class="flex flex-col justify-center items-center text-center gap-2 mt-4">
            <span class="item-title whitespace-nowrap">Visit Our Office</span>
            <span class="flex flex-col gap-2 item-content">
              <span>
                222 Hoddle Street
              </span>
              <span>
                Abbotsford, VIC 3067
              </span>
              <span>
                Australia
              </span>
            </span>
          </div>
        </div>
        <div class="information-item">
          <div class="item-icon">
            <img src="@/assets/official/contact-us/Clock.png" alt="">
          </div>
          <div class="flex flex-col justify-center text-center gap-2 mt-4">
            <span class="item-title whitespace-nowrap">Office Hours</span>
            <span class="item-content flex flex-col gap-2">
              <span>Monday to Friday
              </span>
              <span>
                9:00 AM – 5:30 PM
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="contact-divide">
      <divide />
    </div>
    <div ref="contactMessage" class="message">
      <div class="message-title">
        Send us a message<span class="accent-dot">.</span>
      </div>
      <div class="message-subtitle">
        Our team is ready to assist with your payment needs. Contact us today and let’s simplify your transactions.
      </div>
      <div class="message-form">
        <VeeForm ref="messageFormRef" :validation-schema="validationSchema" @submit="messageSubmit">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-x-10">
            <div class="form flex flex-col">
              <label for="firstName" class="custom-label">First Name<span class="form-required">*</span></label>
              <Field v-slot="{ errorMessage }" v-model="messageForm.firstName" name="firstName">
                <InputText v-model="messageForm.firstName" class="custom-input" name="firstName" type="text" />

                <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                  <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                    <span class="text-[#ff3131]">
                      {{
                        errorMessage }}
                    </span>
                  </div>
                </Message>
              </Field>
            </div>
            <div class="form flex flex-col">
              <label for="lastName" class="custom-label">Last Name<span class="form-required">*</span></label>
              <Field v-slot="{ errorMessage }" v-model="messageForm.lastName" name="lastName">
                <InputText v-model="messageForm.lastName" class="custom-input" name="lastName" type="text" />

                <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                  <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                    <span class="text-[#ff3131]">
                      {{
                        errorMessage }}
                    </span>
                  </div>
                </Message>
              </Field>
            </div>
            <div class="form flex flex-col">
              <label for="email" class="custom-label">Email<span class="form-required">*</span></label>
              <Field v-slot="{ errorMessage }" v-model="messageForm.email" name="email">
                <InputText v-model="messageForm.email" class="custom-input" name="email" type="text" />

                <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                  <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                    <span class="text-[#ff3131]">
                      {{
                        errorMessage }}
                    </span>
                  </div>
                </Message>
              </Field>
            </div>
            <div class="form flex flex-col">
              <label for="phone" class="custom-label">Phone Number<span class="form-required">*</span></label>
              <Field v-slot="{ errorMessage }" v-model="messageForm.phone" name="phone">
                <InputGroup class="custom-input-group">
                  <InputGroupAddon>+61</InputGroupAddon>
                  <InputText v-model="messageForm.phone" name="phone" type="text" />
                </InputGroup>

                <Message v-if="errorMessage" class="ml-4 mt-4 mb-4" severity="error" size="small" variant="simple">
                  <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                    <span class="text-[#ff3131]">
                      {{
                        errorMessage }}
                    </span>
                  </div>
                </Message>
              </Field>
            </div>
            <div class="form flex flex-col mt-6 md:mt-0">
              <label for="companyName" class="custom-label">Company Name<span class="form-required">*</span></label>
              <Field v-slot="{ errorMessage }" v-model="messageForm.companyName" name="companyName">
                <InputText v-model="messageForm.companyName" class="custom-input" name="companyName" type="text" />

                <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                  <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                    <span class="text-[#ff3131]">
                      {{
                        errorMessage }}
                    </span>
                  </div>
                </Message>
              </Field>
            </div>
            <div class="form flex flex-col ">
              <label for="request_category" class="custom-label">Request Category<span
                class="form-required"
              >*</span></label>
              <Field v-slot="{ errorMessage }" v-model="messageForm.request_category" name="request_category">
                <Select
                  v-model="messageForm.request_category" class="custom-select" :options="dicts.categoryOptions"
                  option-label="label" option-value="value"
                >
                  <template #dropdownicon>
                    <i class="pi pi-sort-down-fill" style="color: #ff5f00;font-size: 22px;" />
                  </template>
                </select>
                <Message v-if="errorMessage" class="ml-4 mt-4 mb-4" severity="error" size="small" variant="simple">
                  <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                    <span class="text-[#ff3131]">
                      {{
                        errorMessage }}
                    </span>
                  </div>
                </Message>
              </Field>
            </div>
            <div class="form flex flex-col  mt-6 md:mt-0">
              <label for="enquiry" class="custom-label">Enquiry<span class="form-required">*</span></label>
              <Field v-slot="{ errorMessage }" v-model="messageForm.enquiry" name="enquiry">
                <InputText v-model="messageForm.enquiry" class="custom-input" name="enquiry" type="text" />

                <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                  <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                    <span class="text-[#ff3131]">
                      {{
                        errorMessage }}
                    </span>
                  </div>
                </Message>
              </Field>
            </div>
            <div class="form flex flex-col ">
              <label for="product" class="custom-label">Bill Buddy Product</label>
              <Field v-slot="{ errorMessage }" v-model="messageForm.product" name="product">
                <Select
                  v-model="messageForm.product" class="custom-select" :options="dicts.productOptions"
                  option-label="label" option-value="value"
                >
                  <template #dropdownicon>
                    <i class="pi pi-sort-down-fill" style="color: #ff5f00;font-size: 22px;" />
                  </template>
                </select>
                <Message v-if="errorMessage" class="ml-4 mt-4 mb-4" severity="error" size="small" variant="simple">
                  <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                    <span class="text-[#ff3131]">
                      {{
                        errorMessage }}
                    </span>
                  </div>
                </Message>
              </Field>
            </div>
            <div class="custom-textarea md:col-span-2 flex flex-col  mt-6 md:mt-0">
              <label for="message" class="custom-label">Message<span class="form-required">*</span></label>
              <Field v-slot="{ errorMessage }" v-model="messageForm.message" name="message">
                <Textarea v-model="messageForm.message" auto-resize rows="6" cols="76" />
                <Message v-if="errorMessage" class="ml-4 mt-2 mb-4" severity="error" size="small" variant="simple">
                  <div class="flex items-center">
                    <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                    <span class="text-[#ff3131]">
                      {{
                        errorMessage }}
                    </span>
                  </div>
                </Message>
              </Field>
            </div>
            <div class="my-6">
              <div class="font-bold mb-2 text-[18px]">
                *CAPTCHA
              </div>
              <Field v-slot="{ errorMessage }" v-model="messageForm.google_token" name="google_token">
                <div class="field mb-4">
                  <GoogleRecaptcha
                    ref="recaptchaRef" class="mb-2" @verify="onRecaptchaVerify"
                    @expired="onRecaptchaExpired" @error="onRecaptchaError"
                  />
                  <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
            </div>
            <div class="flex justify-end items-center mt-8 md:col-span-2">
              <button class="btn btn-secondary mt-2 cursor-pointer" type="submit">
                SUBMIT
              </button>
            </div>
          </div>
        </VeeForm>
      </div>
    </div>
    <div class="feedback">
      <div class="feedback-title">
        <h1>
          <span class="text-title">Unified</span> Payments,<br>
          <span class="text-title">Secured</span> Transactions,<br>
          <span class="text-title">Simplified</span> Experience.
        </h1>
      </div>
      <div class="feedback-content">
        <div class="feedback-left">
          <div class="feedback-left-title">
            Want to provide us a feedback<span class="questions-color">?</span>
          </div>
          <div class="mt-6 rounded-xl h-[87.7%]">
            <img src="@/assets/official/contact-feedback.webp" alt="" class="h-full rounded-[10px]">
          </div>
        </div>
        <div class="feedback-form">
          <VeeForm ref="feedbackFormRef" :validation-schema="validationFeedbackSchema" @submit="feedbackSubmit">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-10">
              <div class="form flex flex-col md:col-span-2 mb-[1.5rem]">
                <label for="firstName" class="custom-label">Are you currently with Bill Buddy?<span
                  class="form-required"
                >*</span></label>
                <Field v-slot="{ errorMessage }" v-model="feedbackForm.customer" name="customer">
                  <Select
                    v-model="feedbackForm.customer" class="custom-select" :options="dicts.customerOptions"
                    option-label="label" option-value="value"
                  >
                    <template #dropdownicon>
                      <i class="pi pi-sort-down-fill" style="color: #ff5f00;font-size: 22px;" />
                    </template>
                  </select>

                  <Message v-if="errorMessage" class="ml-4 mt-2" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </Field>
              </div>
              <div class="form flex flex-col">
                <label for="firstName" class="custom-label">First Name<span class="form-required">*</span></label>
                <Field v-slot="{ errorMessage }" v-model="feedbackForm.firstName" name="firstName">
                  <InputText v-model="feedbackForm.firstName" class="custom-input" name="firstName" type="text" />

                  <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </Field>
              </div>
              <div class="form flex flex-col">
                <label for="lastName" class="custom-label">Last Name<span class="form-required">*</span></label>
                <Field v-slot="{ errorMessage }" v-model="feedbackForm.lastName" name="lastName">
                  <InputText v-model="feedbackForm.lastName" class="custom-input" name="lastName" type="text" />

                  <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </Field>
              </div>
              <div class="form flex flex-col md:col-span-2">
                <label for="companyName" class="custom-label">Company Name<span class="form-required">*</span></label>
                <Field v-slot="{ errorMessage }" v-model="feedbackForm.companyName" name="companyName">
                  <InputText v-model="feedbackForm.companyName" class="custom-input" name="companyName" type="text" />

                  <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </Field>
              </div>
              <div class="form flex flex-col">
                <label for="landline" class="custom-label">Landline</label>
                <Field v-slot="{ errorMessage }" v-model="feedbackForm.landline" name="landline">
                  <InputText v-model="feedbackForm.landline" class="custom-input" name="landline" type="text" />

                  <Message v-if="errorMessage" class="ml-4 mt-4 mb-4" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </Field>
              </div>
              <div class="form flex flex-col   md:mt-0">
                <label for="phone" class="custom-label">Phone Number<span class="form-required">*</span></label>
                <Field v-slot="{ errorMessage }" v-model="feedbackForm.phone" name="phone">
                  <InputGroup class="custom-input-group">
                    <InputGroupAddon>+61</InputGroupAddon>
                    <InputText v-model="feedbackForm.phone" name="phone" type="text" />
                  </InputGroup>

                  <Message v-if="errorMessage" class="ml-4 mt-4 mb-4" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </Field>
              </div>
              <div class="form flex flex-col md:col-span-2 mt-[1.5rem] md:mt-0">
                <label for="email" class="custom-label">Email<span class="form-required">*</span></label>
                <Field v-slot="{ errorMessage }" v-model="feedbackForm.email" name="email">
                  <InputText v-model="feedbackForm.email" class="custom-input" name="email" type="text" />

                  <Message v-if="errorMessage" class="ml-4 -mt-2 mb-4" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </Field>
              </div>

              <div class="form flex flex-col md:col-span-2  mb-[1.5rem]">
                <Field v-slot="{ errorMessage }" v-model="feedbackForm.feedbackType" name="feedbackType">
                  <label for="feedbackType" class="custom-label" :class="{ 'mt-[1.5rem]': errorMessage }">Feedback
                    Type<span class="form-required">*</span></label>
                  <Select
                    v-model="feedbackForm.feedbackType" class="custom-select " :options="dicts.feedbackOptions"
                    option-label="label" option-value="value"
                  >
                    <template #dropdownicon>
                      <i class="pi pi-sort-down-fill" style="color: #ff5f00;font-size: 22px;" />
                    </template>
                  </select>
                  <Message v-if="errorMessage" class="ml-4 mt-2 mb-4" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </Field>
              </div>

              <div class="custom-textarea md:col-span-2 flex flex-col">
                <label for="message" class="custom-label">Message<span class="form-required">*</span></label>
                <Field v-slot="{ errorMessage }" v-model="feedbackForm.message" name="message">
                  <Textarea v-model="feedbackForm.message" auto-resize rows="6" cols="76" />
                  <Message v-if="errorMessage" class="ml-4 mt-2 mb-4" severity="error" size="small" variant="simple">
                    <div class="flex items-center">
                      <i class="pi pi-exclamation-triangle" style="color: #ff3131; margin-right: 10px;" />
                      <span class="text-[#ff3131]">
                        {{
                          errorMessage }}
                      </span>
                    </div>
                  </Message>
                </Field>
              </div>
              <div class="my-6">
                <div class="font-bold mb-2 text-[18px]">
                  *CAPTCHA
                </div>
                <Field v-slot="{ errorMessage }" v-model="feedbackForm.google_token" name="google_token">
                  <div class="field mb-4">
                    <GoogleRecaptcha
                      ref="recaptchaFeedbackRef" class="mb-2" @verify="onRecaptchaVerifyFeedback"
                      @expired="onRecaptchaExpired" @error="onRecaptchaError"
                    />
                    <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </div>
                </Field>
              </div>
              <div class="flex justify-end items-center mt-8 md:col-span-2">
                <button class="btn btn-secondary mt-2 cursor-pointer" type="submit">
                  SUBMIT
                </button>
              </div>
            </div>
          </VeeForm>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.contact {
  width: 100%;
  background-color: #f5f5ff;
  padding-bottom: 4rem;
}

.accent-dot {
  color: #ff5722;
}

.form-required {
  color: #ff3131;
}

.text-title {
  color: #fe4c1c;
}

.questions-color {
  color: #ff5f00;
}

.contact-header {
  position: relative;
  background-color: #19164b;
  color: #e1ffa9;
  padding: 160px 20px;

  @include media-breakpoint-down(lg) {
    padding: 100px 20px;
  }

  @include media-breakpoint-down(xl) {
    padding: 150px 20px;
  }

  @include media-breakpoint-down(md) {
    padding: 110px 20px;
  }

  @include media-breakpoint-down(sm) {
    padding: 100px 20px;
  }
}

.content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  text-align: center;
}

.content h1 {
  font-size: 4.9rem;
  font-weight: 900;

  @include media-breakpoint-down(lg) {
    font-size: 3.5rem;
  }

  @include media-breakpoint-down(md) {
    font-size: 3rem;
  }

  @include media-breakpoint-down(sm) {
    font-size: 2.5rem;
  }
}

.right::before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-image: url('@/assets/official/bg/ultra_Contact BG.png');
  background-repeat: no-repeat;
  background-size: 100% 160%;
  background-position: top;
}

.contact-content {
  max-width: 1200px;
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 3rem 4rem 1rem 4rem;

  @include media-breakpoint-down(lg) {
    padding: 3rem 2rem;
    flex-direction: column;
  }

  @include media-breakpoint-down(md) {
    padding: 2rem 1.5rem;
  }

  @include media-breakpoint-down(sm) {
    padding: 1.5rem 1rem;
  }
}

.map {
  width: 600px;
  height: 450px;
  background-color: #19164b;
  margin-right: 3rem;

  @include media-breakpoint-down(lg) {
    width: 100%;
    height: 400px;
    margin-right: 0;
    margin-bottom: 2rem;
  }

  @include media-breakpoint-down(md) {
    width: 100%;
    height: 350px;
  }

  @include media-breakpoint-down(sm) {
    height: 250px;
  }
}

.contact-information {
  // border: 1px solid #ddd;
  // border-radius: 16px;
  // background-color: #ffe3e8;
  padding: 1.5rem;
  width: 100%;

  @include media-breakpoint-down(md) {
    width: 100%;
  }

  @include media-breakpoint-down(sm) {
    padding: 1.25rem 1rem;
  }
}

.information {
  // display: flex;
  // justify-content: center;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin: 2rem;

  @include media-breakpoint-down(xl) {
    gap: 0;
    margin: 0;
  }

  @include media-breakpoint-down(lg) {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  @include media-breakpoint-down(md) {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  @include media-breakpoint-down(sm) {
    grid-template-columns: repeat(1, 1fr);
    gap: 2rem;
  }
}

.information-item {
  display: flex;
  flex-direction: column;
  padding: 0 2rem;
  margin: 0 2rem;

  @include media-breakpoint-down(xl) {
    padding: 0;
  }

  .item-title {
    font-weight: 600;
    font-size: 2.1rem;
  }
  .item-content{
    font-size: 16px;
  }
}

.item-icon {
  display: flex;
  justify-content: center;

  img {
    width: 100px;
    height: 100px;
  }
}

.item-icon i {
  color: #fff;
  background-color: #fe4c1c;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  font-size: 58px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.contact-divide {
  max-width: 1200px;
  background-color: #f5f5ff;
  margin: 0 auto;

  @include media-breakpoint-down(xl) {
    padding: 0 2rem;
  }
}

.contact-information h2 {
  font-size: 42px;
  font-weight: 900;
  padding: 1rem 0;

  @include media-breakpoint-down(lg) {
    font-size: 36px;
  }

  @include media-breakpoint-down(md) {
    font-size: 32px;
  }

  @include media-breakpoint-down(sm) {
    font-size: 28px;
    padding: 0.75rem 0;
  }
}

.information-detail {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-bottom: 1.5rem;
  line-height: 1.6;
  font-size: 16px;

  @include media-breakpoint-down(sm) {
    font-size: 15px;
    padding-bottom: 1.25rem;
  }
}

.contact-btn {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;

  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }
}

.message {
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 2rem;

  &-title {
    font-size: 3.5rem;
    font-weight: bold;
    display: flex;
    justify-content: center;

    @include media-breakpoint-down(lg) {
      padding: 2rem;
      display: block;
      text-align: center;
    }
  }

  &-subtitle {
    font-size: 1.5rem;
    display: flex;
    justify-content: center;
    margin: 1rem 0 1.5rem 0;

    @include media-breakpoint-down(md) {
      padding: 2rem;

    }
  }

  &-form {
    background-color: #ffe3e8;
    border: 1px solid #b2b2b2;
    border-radius: 30px;
    padding: 2rem 6rem;

    @include media-breakpoint-down(xl) {
      padding: 2rem 2rem;
      margin: 0 2rem;
    }
  }
}

.custom-label {
  margin-left: 20px;
  margin-bottom: 4px;
  font-size: 18px;
  font-weight: 700;
}

.custom-input {
  height: 45px;
  border: 2px solid #181349;
  border-radius: 20px;
  padding-left: 30px;
  font-size: 17px;
  margin-bottom: 1.5rem;

  @include media-breakpoint-down(sm) {
    font-size: 16px;
    padding-left: 20px;
  }
}

.custom-input-group {
  height: 45px;
  --p-inputgroup-addon-border-radius: 20px;
  --p-inputgroup-addon-border-color: #181349;
  --p-inputgroup-addon-min-width: 3.8rem;

  .p-inputgroupaddon {
    border: 2px solid #181349;
    color: #3b3b3b;
    border-right: 1px solid #181349;
    font-size: 17px;
  }

  .p-inputtext {
    border: 2px solid #181349;
    border-left: 1px solid #181349;
  }
}

.custom-select {
  border: 2px solid #181349;
  border-radius: 20px;
  height: 45px;
  align-items: center;
  padding: 0 10px 0 20px;
  font-size: 17px;
}

.custom-textarea {
  .p-textarea {
    border: 2px solid #181349;
    border-radius: 20px;
    margin-top: 4px;
    padding: 10px 20px;
    line-height: 1.3;
    font-size: 17px;
  }
}

.feedback {

  margin-top: 2rem;

  &-title {
    padding: 5rem 0;
    margin: 7rem 0;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: url('@/assets/official/contact-us/contact-bg.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;

    @include media-breakpoint-down(xl) {
      background: none;
      margin: 0;
      padding: 4rem 2rem;
    }

    h1 {
      font-size: 62px;
      font-weight: 700;

      @include media-breakpoint-down(lg) {
        font-size: 40px;
      }
    }

  }

  &-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;

    @include media-breakpoint-down(xl) {
      flex-direction: column;
    }
  }

  &-left {
    width: 25%;

    @include media-breakpoint-down(xl) {
      width: 100%;
      text-align: center;
    }

    &-title {
      font-size: 35px;
      font-weight: 700;
      height: 10%;

      @include media-breakpoint-down(xl) {
        height: 100%;
      }

    }
  }

  img {
    @include media-breakpoint-down(xl) {
      display: none;
    }
  }

  &-form {
    width: 73%;
    background-color: #ffe3e8;
    border: 1px solid #b2b2b2;
    border-radius: 30px;
    padding: 2rem 2rem;

    @include media-breakpoint-down(xl) {
      width: auto;
      margin: 0 2rem;

    }
  }

}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 16px;
  color: #181349;

  @include media-breakpoint-down(sm) {
    font-size: 14px;
    padding: 0.6rem 1.25rem;
  }
}

.btn-primary {
  background-color: #e1ffa9;
  color: #181349;
  font-weight: 600;
  width: 180px;
  height: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;

}

.btn-group {
  @include media-breakpoint-down(md) {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding-bottom: 2rem;
    align-items: center;
  }
}

.btn-register {
  background-color: #ffe3e8;
  width: 200px;

  &:hover {
    background-color: hsl(349, 100%, 90%);
  }

  &:active {
    background-color: hsl(349, 100%, 80%);
  }

  @include media-breakpoint-down(sm) {
    width: 200px;
    height: 2.75rem;
  }
}

.btn-book {
  background-color: #09deff;
  width: 200px;

  &:hover {
    background-color: hsl(188, 100%, 45%);

  }

  &:active {
    background-color: hsl(188, 100%, 35%);
  }

  @include media-breakpoint-down(sm) {
    width: 200px;
    height: 2.75rem;
  }
}

.btn-secondary {
  background-color: #fe4c1c;
  color: #fff;
  font-weight: 600;
  width: 180px;
  height: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;

  @include media-breakpoint-down(sm) {
    width: 100%;
    height: 3rem;
  }

  &:hover {
    background-color: hsl(13, 99%, 45%);
  }

  &:active {
    background-color: hsl(13, 99%, 35%);
  }
}

.address {
  display: flex;
  flex-direction: column;
  margin-top: 1rem;
  line-height: 1.6;
  font-size: 16px;

  @include media-breakpoint-down(sm) {
    font-size: 15px;
  }
}

.contact-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 0.5rem;

  @include media-breakpoint-down(md) {
    flex-direction: row;
    gap: 0.5rem;
    align-items: center;
  }

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  strong {
    font-weight: 600;
    margin-right: 0.5rem;
  }
}

.container {
  margin-left: auto;
  margin-right: auto;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  width: calc(100% - 3rem);
  max-width: 1340px;

  @include media-breakpoint-down(xxl) {
    max-width: 1140px;
  }

  @include media-breakpoint-down(lg) {
    max-width: 920px;
  }

  @include media-breakpoint-down(md) {
    max-width: 540px;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  @include media-breakpoint-down(sm) {
    max-width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style>
