declare namespace FlexiratesProperty {

  interface PropertyInfo {
    active_register_flag: string
    active_registration_id: string
    allow_quaterly: string
    allow_register: string
    assessment_number: string
    end_date: string
    full_amount: string
    instalment_1_amount: string
    instalment_1_due: string
    instalment_2_due: string
    instalment_2_mount: string
    instalment_3_amount: string
    instalment_3_due: string
    instalment_4_amount: string
    instalment_4_due: string
    message: string
    postcode: string
    property_address: string
    property_suburb: string
    scheduled_payments_flag: string
    start_date: string
  }
  interface Banking {
    account_name?: string
    account_no?: string
    account_no_encrypt?: null
    bsb?: string
    city?: string
    company_name?: string
    country_iso2?: null
    created_at?: null
    created_uid?: number
    credit_brand?: number
    credit_type?: null
    deleted_at?: null
    deleted_uid?: number
    email?: string
    enable?: number
    expiration_month?: null
    expiration_year?: null
    first_name?: string
    flexi_property_id?: number
    flexi_user_id?: number
    id?: number
    last_name?: string
    line_1?: string
    line_2?: string
    payment_instrument_id?: null
    phone?: null
    postcode?: string
    security_code?: null
    shipping_id?: null
    state?: string
    type?: number
    updated_at?: null
    updated_uid?: number
  }
  interface Property {
    assessment_number: string
    created_at: null
    created_uid: number
    deleted_at: null
    deleted_uid: null
    end_date: string
    flexi_user_id: number
    full_amount: string
    id: number
    post_code: string
    property_address: string
    property_info: PropertyInfo
    start_date: null
    updated_at: string
    updated_uid: number
  }

}
