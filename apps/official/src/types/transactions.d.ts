declare namespace Transaction {

  interface Info {
    'created_at'?: string
    'created_uid'?: number
    /**
     * 客户信息
     */
    'customer'?: Customer.Info

    'customer_banking'?: CustomerBanking
    /**
     * 客户卡ID
     */
    'customer_banking_id'?: number
    /**
     * 客户编号
     */
    'customer_id'?: string
    /**
     * 客户计划
     */
    'customer_plan'?: Plan.Info
    /**
     * 客户计划ID
     */
    'customer_plan_id'?: number
    'deleted_at'?: null
    'deleted_uid'?: number
    /**
     * 税费金额
     */
    'gst_amount'?: string
    /**
     * 手续费金额
     */
    'surcharge_amount'?: string
    'amount'?: string

    'id'?: number
    /**
     * 商户编号
     */
    'merchant_id'?: string
    /**
     * 净值金额，同上
     */
    'net_amount'?: string
    /**
     * 净值币种，同上
     */
    'net_currency'?: string
    /**
     * 原始交易id，transaction.id
     */
    'ori_trans_id'?: number
    /**
     * 支付金额
     */
    'payment_amount'?: string
    /**
     * 支付币种
     */
    'payment_currency'?: string
    /**
     * 客户计划对应的原始计划
     */
    'plan'?: Plan.Info
    /**
     * 计划编号
     */
    'plan_id'?: string
    /**
     * 备注信息，Declined Reason等
     */
    'remark'?: string
    /**
     * 状态，1：Succeeded 2：Failed 3：Pending 4：Refunded 5：Disputed 6：UnCaptured
     */
    'status'?: number
    'timelines'?: Timeline[]

    'settlement_details'?: SettlementDetail[]
    /**
     * 交易类型，1：Payment 2：Refund 3：Dispute
     */
    'trans_type'?: number
    'updated_at'?: null
    'trans_no'?: string
    'updated_uid'?: number

    'trans_invoice_number'?: string

    'fee_amount'?: string

    'refunded_date[]'?: string[]
  }
  interface CustomerBanking {
    /**
     * 账户名称
     */
    account_name: string
    /**
     * 账户号码
     */
    account_no: string
    /**
     * 账单地址
     */
    bill_address: string
    /**
     * 账单城市
     */
    bill_city: string
    /**
     * 账单国家
     */
    bill_country: string
    /**
     * 账单国家2字代码
     */
    bill_country_isoa: string
    /**
     * 账单邮箱
     */
    bill_email: string
    /**
     * 账单名
     */
    bill_first_name: string
    /**
     * 账单姓
     */
    bill_last_name: string
    /**
     * 账单人
     */
    bill_name: string
    /**
     * 账单电话
     */
    bill_phone: string
    /**
     * 账单邮编
     */
    bill_postcode: string
    /**
     * 账单州
     */
    bill_state: string
    bsb: string
    /**
     * 城市
     */
    city: string
    /**
     * 公司名称
     */
    company_name: null
    created_at: string
    created_uid: number
    /**
     * 如果是信用卡，信用卡类型，枚举：1：PayPal / 2：AfterPay / 3：Zip / 4：Latitude Interest Free
     */
    credit_type: number
    customer_id: string
    deleted_at: null
    deleted_uid: number
    /**
     * 邮件
     */
    email: string
    enable: number
    /**
     * 有效期，通常是信用卡
     */
    expiration_date: string
    /**
     * 名
     */
    first_name: string
    id: number
    /**
     * 姓
     */
    last_name: string
    /**
     * 地址1
     */
    line_1: string
    /**
     * 地址2
     */
    line_2: string
    /**
     * 卡token
     */
    payment_instrument_id: null
    /**
     * 邮编
     */
    postcode: string
    /**
     * CVV，通常是信用卡
     */
    security_code: string
    /**
     * 收货地址ID，如果选择的是收货地址
     */
    shipping_id: null
    /**
     * 州
     */
    state: string
    /**
     * 账户类型，枚举：1：Bank Account / 2：Credit
     */
    type: number

    expiration_month?: string
    expiration_year?: string

    credit_brand?: number
    updated_at: null
    updated_uid?: number
  }

  interface Timeline {
    id: number
    remark: string
    title: string
    trans_id: number
    trans_no: string
  }

  interface SettlementDetail {
    amount_type: string
    amount_type_text: string
    settlement_amount: string
    settlement_currency: string
  }
}
