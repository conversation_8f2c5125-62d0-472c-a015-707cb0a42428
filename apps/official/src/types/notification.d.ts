export interface Notification {
  id: string
  title: string
  content: string
  type: 'info' | 'payment' | 'error' | 'system'
  time: Date
  read: boolean
}

export interface NotificationGroup {
  today: Notification[]
  yesterday: Notification[]
  lastWeek: Notification[]
}

export type NotificationType = 'info' | 'payment' | 'error' | 'system'

export interface NotificationColor {
  [key: string]: string
}
