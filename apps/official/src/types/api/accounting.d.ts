declare namespace Api {
  interface CreateAccountingReq {
    client_id: string
    client_secret: string
    account_code: string
  }

  interface CreateAccountingConfigReq {
    client_id: string
    client_secret: string
    redirect_uri: string
  }

  interface CreateAccountingConfigRes {
    url: string
  }

  interface AccountingListReq {
    page?: number
    page_size?: number
    client_id?: string
    status?: string
    created_at?: string[]
    sort_by?: string
    sort_order?: string
  }

  interface UpdateAccountingReq extends CreateAccountingReq {
    id: number
  }

  interface AccountingListRes {
    data: Accounting.Info[]
    total: number
  }

  interface AccountingDetailRes {
    code: number
    message: string
    data: Accounting.Info
  }

  interface AccountingNumberDetailReq {
    /**
     * 发票编号
     */
    invoice_number: string
    /**
     * 加密发票ID
     */
    invoice_token: string
  }

  interface AccountingNumberDetailRes {
    /**
     * 待支付金额
     */
    amount_due: string
    /**
     * 已支付金额
     */
    amount_paid: string
    /**
     * 支付币种
     */
    currency: string
    /**
     * invoiceID
     */
    invoice_id: string
    /**
     * invoice编号
     */
    invoice_number: string
    /**
     * 总支付金额
     */
    surcharge_rate: {
      fee_rate: string
      fee_value: string
    }
    total: string
  }

  interface AccountingNumberPayReq {
    /**
     * 支付金额
     */
    amount: number
    /**
     * 卡号
     */
    card_number: string
    /**
     * 邮箱
     */
    email: string
    /**
     * 有效期月
     */
    expiration_month: string
    /**
     * 有效期年
     */
    expiration_year: string
    /**
     * 名
     */
    first_name: string
    /**
     * Google验证token
     */
    google_token: string
    /**
     * invoice编号
     */
    invoice_number: string
    /**
     * 加密ID
     */
    invoice_token: string
    /**
     * 姓
     */
    last_name: string
    /**
     * CVV
     */
    security_code: string
  }
}
