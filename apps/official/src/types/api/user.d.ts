declare namespace Api {

  /**
   * Request payload for the login endpoint.
   *
   * @property email - The user's email address.
   * @property password - The user's password.
   * @property rememberMe - Whether to remember the user's login.
   */
  interface UserLoginReq {
    email: string
    password: string
    rememberMe: boolean
    google_token: string
  }

  interface UserLoginRes {
    refresh_token: string
    access_token: string
  }

  interface UserRegisterReq {
    email: string
    password: string
  }

  interface UserRegisterRes {
    message: string
  }

  interface UserForgotPasswordReq {
    email: string
  }

  interface UserForgotPasswordRes {
    message: string
  }

  interface UserSetNewPasswordRequest {
    /**
     * 新密码
     */
    new_password: string
    /**
     * 标识，从地址上获取
     */
    token: string

  }

  interface UserTwoFactorSetupRes {
    secret: string
    google2fa_url: string
    ios_download_path: string
    android_download_path: string
    is_bind: boolean
  }

  interface UserTwoFactorVerifyReq {
    /**
     * MFA秘钥，用户首次绑定验证时必填
     */
    secret?: string
    /**
     * MFA验证码
     */
    verify_code: string
  }
  interface UserTwoFactorVerifyRes {
    data: any
  }

  interface UserListRes extends CommonListRes<User.UserInfo[]> {
    current_page?: number
    per_page?: number
    total?: number
  }

  interface UserListReq {
    /**
     * 页码
     */
    'page'?: number
    'page_size'?: number
    /**
     * 排序字段
     */
    'sort_by'?: string
    /**
     * 排序方向
     */
    'sort_order'?: 'asc' | 'desc'
    /**
     * 用户名，支持模糊搜索
     */
    'name'?: string
    /**
     * 邮箱，支持模糊搜索
     */
    'email'?: string
    /**
     * 用户状态，1: 活跃 0: 禁用
     */
    'status'?: string
    /**
     * 用户类型，1: 管理员 2: 普通用户 3: 访客
     */
    'type'?: string
    /**
     * 2FA状态，1: 开启 0: 关闭
     */
    'mfa_check'?: string
    /**
     * 创建时间范围，[start, end]，格式：yyyy-MM-dd
     */
    'created_at[]'?: string[]
  }

}
