declare namespace Api {
  interface XeroSubmitPaymentReq {
    redirect_uri: string
  }

  interface XeroInfoRes {
    xero: {
      email: string
      file_name: string
      id: number
      merchant_id: string
      status: number
      sync_records: XeroSyncRecord[]
    }
  }

  interface XeroSyncRecord {
    id: number
    merchant_id: string
    status: number
    created_at: string
    updated_at: string
  }

  interface XeroChartOfAccountsRes {
    account_code: string
    name: string
  }

  interface XeroInvoiceTemplatesRes {
    accept_pinch_pay: 0 | 1
    allow_auto_debit: 0 | 1
    id: number
    monitor_debit: 0 | 1
    name: string
    send_invoice_notify: 0 | 1
    theme_id: string
    type: string
  }

  interface XeroCreateInvoiceReq {
    /**
     * 联系人ID
     */
    customer_id: string
    /**
     * 描述
     */
    reference: string
    /**
     * 发票到期时间
     */
    due_date: string
    /**
     * 子项
     */
    line_items: XeroCreateInvoiceLineItem[]
    /**
     * 主题id
     */
    theme_id: string
  }

  interface XeroCreateInvoiceLineItem {
    /**
     * 账户编码
     */
    account_code: string
    /**
     * 描述
     */
    description: string
    /**
     * 单价
     */
    unit_amount: string
  }

  interface InvoiceConfigRes {
    /**
     * 发票上尚未支付的金额
     */
    amount_due: string
    /**
     * 已收到发票款项的总和
     */
    amount_paid: string
    /**
     * 发票编号
     */
    invoice_number: string
    /**
     * 发票不含税合计金额
     */
    sub_total: string
    /**
     * 附加费率
     */
    surcharge_fees: {
      /**
       * 费率类型（1：百分比、2：固定值）
       */
      fee_rate: string
      /**
       * 附加费
       */
      fee_value: string
    }
    customer: {
      name: string
      customer_id: string
    }
    invoice_id: string
    created_at: string
    due_date: string
    line_items: Invoice.LineItem[]
  }

  interface GetXeroInvoiceConfigReq {
    /**
     * 加密ID（url路径获取）
     */
    id?: string
    invoice_number?: string
  }

  interface XeroPayReq {
    amount: number
    email: string
    name: string
    google_token: string
    invoice_number: string
    invoice_token: string
    bank?: {
      bsb: string
      account_name: string
      account_no: string
    }
    card?: {
      card_number: string
      expiration_month: string
      expiration_year: string
      security_code: string
    }
  }

  interface XeroPayRes {

  }

  interface XeroSettingsRes {
    bank_account_id?: number
    bank_accounts?: XeroSettingsBankAccountRes[]
    branding_themes?: XeroSettingsBrandingThemeRes[]
    email?: string
    file_name?: string
    id?: number
    logo?: string
    merchant_id?: string
    status?: number
    tenant_id?: string
  }

  interface XeroSettingsBankAccountRes {
    id: number
    name: string
    tenant_id: string
  }

  interface XeroSettingsBrandingThemeRes {
    accept_pinch_pay?: number
    allow_auto_debit?: number
    id?: number
    monitor_debit?: number
    send_invoice_notify?: number
    tenant_id?: string
  }
}
