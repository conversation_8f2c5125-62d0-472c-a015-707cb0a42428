declare namespace Api {
  interface FlexiratesListItem {
    banking?: FlexiratesProperty.Banking[]
    created_at?: null
    created_uid?: number
    deleted_at?: null
    deleted_uid?: number
    enable?: number
    end_date?: string
    fail_times?: number
    flexi_banking_id?: number
    flexi_property_id?: number
    flexi_user_id?: number
    id?: number
    next_process_date?: string
    plan_id?: string
    process_terms?: number
    process_type?: number
    property?: FlexiratesProperty.Property
    start_date?: string
    status?: number
    updated_at?: null
    updated_uid?: number
  }
  interface FlexiratesGetScheduleReq extends CommonSearchListParams {
    property_id: string
  }
  interface FlexiratesScheduleRes extends CommonListRes {
    data: FlexiratesListItem[]
  }
  interface FlexiratesSchedulesDetailRes extends CommonRes {
    data: FlexiratesListItem
  }
}
