declare namespace Api {

  interface RouterItem {
    i18nKey: string
    path: string
    name: string
    component: string
    isHide?: boolean
    redirect?: string | { name: string }
    isHideBreadcrumb?: boolean
    isSeparator?: boolean
    breadcrumbTitle?: string
    children?: RouterItem[]
    icon?: string
    isKeepAlive?: boolean
  }

  interface RouterRes {
    routes: RouterItem[]
  }
}
