declare namespace Api {
  interface UserRoleListRes extends CommonListRes<User.Info[]> {
    current_page?: number
    per_page?: number
    total?: number
  }

  interface UserRoleListReq {
    /**
     * 页码
     */
    'page'?: number
    'page_size'?: number
    /**
     * 排序字段
     */
    'sort_by'?: string
    /**
     * 排序方向
     */
    'sort_order'?: 'asc' | 'desc'
    /**
     * 用户名，支持模糊搜索
     */
    'name'?: string
    /**
     * 邮箱，支持模糊搜索
     */
    'email'?: string
    /**
     * 用户状态，1: 活跃 0: 禁用
     */
    'status'?: string
    /**
     * 用户类型，1: 管理员 2: 普通用户 3: 访客
     */
    'type'?: string
    /**
     * 2FA状态，1: 开启 0: 关闭
     */
    'mfa_check'?: string
    /**
     * 创建时间范围，[start, end]，格式：yyyy-MM-dd
     */
    'created_at[]'?: string[]
  }
}
