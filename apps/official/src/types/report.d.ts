declare namespace Report {
  interface Revenue {
    monthly: {
      [key: string]: Total
    }
    quarterly: {
      [key: string]: Total
    }
    total: Total
    yoy: Yoy
  }

  interface Yoy {
    monthly: {
      [key: string]: Total
    }
    quarterly: {
      [key: string]: Total
    }
    total: Total
  }

  interface Total {
    payment_amount: number
    net_amount: number
  }

  interface transactionPayout {
    daily: {
      [key: string]: number
    }
    payout_daily: {
      [key: string]: number
    }
    by_brand: {
      [key: string]: number
    }
    cost_by_brand: {
      [key: string]: transactionDetail
    }
    cost_date_range: {
      start: string
      end: string
    }
  }

  interface transactionDetail {
    net_amount: string
    fee_amount: string
    fee_ratio: number
  }

  interface subscription {
    summary: [{
      plan_id: string
      plan_name: string
      total_plans: number
      active_plans: number
      new_plans: number
      churned_plans: number
      trend_plans: number
      mrr: number
      arr: number
    }]
    trends: {
      top_plans: string[]
      daily_changes: {
        [key: string]: {
          [key: string]: {
            total_plans: number
            active_plans: number
            new_plans: number
          }
        }
      }
    }
  }
}
