declare namespace Invoice {
  interface Info {
    /**
     * 创建时间
     */
    created_at: string
    /**
     * 币种
     */
    currency: string
    /**
     * 客户数据
     */
    customer: Customer.Info
    /**
     * 客户ID
     */
    customer_id: string
    /**
     * 截止时间
     */
    due_date: string
    id: number
    /**
     * 渠道发票编号
     */
    invoice_number: string
    /**
     * MID
     */
    merchant_id: string
    /**
     * 状态
     */
    status: string
    /**
     * 发票总金额
     */
    sub_total: string
    timeline: null
    /**
     * 交易数据
     */
    timeline: Timeline[]
    payment_method: PaymentMethod

    line_items: LineItem[]
  }

  interface Timeline {
    payment_amount: string
    payment_currency: string
    status: number
    trans_invoice_id: number
    trans_no: string
  }

  interface PaymentMethod {
    /**
     * 卡号
     */
    account_no: string
    /**
     * 交易时间
     */
    created_at: string
    /**
     * 卡品牌
     */
    credit_brand: number
    /**
     * 订单号
     */
    trans_no: string
  }
  interface LineItem {
    account_code: string
    description: string
    id: number
    line_amount: string
    quantity: string
    unit_amount: string
    xero_invoice_id: number
  }

  interface DashboardData {
    count: number
    /**
     * 状态常量
     */
    status: number | null
    /**
     * 状态名称
     */
    status_text: string
  }

}
