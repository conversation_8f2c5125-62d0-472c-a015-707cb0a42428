// 定义文件上传相关类型
declare namespace FileUpload {
  // 上传文件结果项
  interface UploadFileItem {
    /** 文件访问URL */
    url: string
    /** 文件名称 */
    name?: string
    /** 文件大小(字节) */
    size?: number
    /** 文件类型 */
    type?: string
    /** 其他可能的额外属性 */
    [key: string]: any
  }

  // 文件上传组件的v-model类型
  type UploadModelValue = UploadFileItem | UploadFileItem[] | string | null

  // 文件上传函数选项
  interface UploadOptions {
    /** 请求头 */
    headers?: Record<string, string>
    /** 进度回调 */
    onProgress?: (progress: number) => void
    /** 取消信号 */
    signal?: AbortSignal
    /** 是否启用分片上传 */
    chunked?: boolean
    /** 分片大小 */
    chunkSize?: number
  }

  // 上传函数接口
  interface UploadFunction {
    (file: File, options?: UploadOptions): Promise<any>
    /** 是否支持分片上传 */
    supportsChunks?: boolean
  }

  // 文件上传的状态类型
  type UploadStatus = 'pending' | 'uploading' | 'success' | 'error'

  // 内部文件项状态
  interface FileListItem {
    /** 原始文件对象 */
    file: File
    /** 唯一标识 */
    id: string
    /** 上传进度(0-100) */
    progress: number
    /** 上传状态 */
    status: UploadStatus
    /** 预览URL */
    preview?: string
    /** 服务器响应 */
    response?: any
    /** 错误信息 */
    error?: string | Error
    /** 警告信息 */
    warning?: string
  }
}
