import { getAssetsFiles } from './getAssetsFile'

export const getBankType = (bankType: number) => {
  switch (bankType) {
    case 1:
      return 'Visa'
    case 2:
      return 'Mastercard'
    case 3:
      return 'American Express'
    case 4:
      return 'Discover'
    case 5:
      return 'Diners Club'
    case 0:
    case 6:
      return 'Carte Blanche'
  }
  return ''
}

export const getBankTypeImageUrl = (bankType: number) => {
  switch (bankType) {
    case 1:
      return getAssetsFiles('merchant/card-type/visa.png')
    case 2:
      return getAssetsFiles('merchant/card-type/mastercard.png')
    case 3:
      return getAssetsFiles('merchant/card-type/amex.png')
    case 4:
      return getAssetsFiles('merchant/card-type/discover.png')
    case 5:
      return getAssetsFiles('merchant/card-type/diners.png')
    case 0:
    case 6:
      return getAssetsFiles('merchant/card-type/carte-blanche.png')
  }

  return ''
}
