import type { DictItem } from '@/services/api/dict'
import { useDictStore } from '@/store/modules/dict'

/**
 * 字典工具类
 */
export class DictUtil {
  /**
   * 获取字典标签
   * @param dictType 字典类型
   * @param value 字典值
   * @returns 字典标签
   */
  static getLabel(dictType: string, value: string | number): string {
    const dictStore = useDictStore()
    return dictStore.getDictLabel(dictType, value)
  }

  /**
   * 获取字典项
   * @param dictType 字典类型
   * @param value 字典值
   * @returns 字典项
   */
  static getItem(dictType: string, value: string | number): DictItem | undefined {
    const dictStore = useDictStore()
    const dictCache = Reflect.get(dictStore, 'dictCache')

    if (!Reflect.has(dictCache, dictType)) { return undefined }

    const dictList = Reflect.get(dictCache, dictType) as DictItem[]
    return dictList.find(item => item.value === value)
  }

  /**
   * 获取字典列表
   * @param dictType 字典类型
   * @returns 字典列表
   */
  static async getList(dictType: string): Promise<DictItem[]> {
    const dictStore = useDictStore()
    const getDictByType = Reflect.get(dictStore, 'getDictByType')
    return await Reflect.apply(getDictByType, dictStore, [dictType])
  }

  /**
   * 根据字典值列表获取对应的标签列表
   * @param dictType 字典类型
   * @param values 字典值列表
   * @returns 字典标签列表
   */
  static getLabels(dictType: string, values: (string | number)[]): string[] {
    const dictStore = useDictStore()
    const dictCache = Reflect.get(dictStore, 'dictCache')

    if (!Reflect.has(dictCache, dictType)) { return [] }

    const dictList = Reflect.get(dictCache, dictType) as DictItem[]
    return values.map((value) => {
      const item = dictList.find(item => item.value === value)
      return item ? item.label : ''
    }).filter(Boolean)
  }

  /**
   * 根据字典标签获取字典值
   * @param dictType 字典类型
   * @param label 字典标签
   * @returns 字典值
   */
  static getValue(dictType: string, label: string): string | number | null | undefined {
    const dictStore = useDictStore()
    const dictCache = Reflect.get(dictStore, 'dictCache')

    if (!Reflect.has(dictCache, dictType)) { return undefined }

    const dictList = Reflect.get(dictCache, dictType) as DictItem[]
    const item = dictList.find(item => item.label === label)
    return item ? item.value : undefined
  }

  /**
   * 检查字典值是否存在
   * @param dictType 字典类型
   * @param value 字典值
   * @returns 是否存在
   */
  static exists(dictType: string, value: string | number): boolean {
    const dictStore = useDictStore()
    const dictCache = Reflect.get(dictStore, 'dictCache')

    if (!Reflect.has(dictCache, dictType)) { return false }

    const dictList = Reflect.get(dictCache, dictType) as DictItem[]
    return !!dictList.find(item => item.value === value)
  }

  /**
   * 过滤字典列表
   * @param dictType 字典类型
   * @param filterFn 过滤函数
   * @returns 过滤后的字典列表
   */
  static filter(dictType: string, filterFn: (item: DictItem) => boolean): DictItem[] {
    const dictStore = useDictStore()
    const dictCache = Reflect.get(dictStore, 'dictCache')

    if (!Reflect.has(dictCache, dictType)) { return [] }

    const dictList = Reflect.get(dictCache, dictType) as DictItem[]
    return dictList.filter(filterFn)
  }

  /**
   * 转换为下拉选项格式
   * @param dictType 字典类型
   * @returns 下拉选项列表
   */
  static async toOptions(dictType: string): Promise<{ label: string, value: string | number | null }[]> {
    const list = await this.getList(dictType)
    return list.map(item => ({
      label: item.label,
      value: item.value,
    }))
  }
}

export const addAllToDict = (dictList: DictItem[], option: { label: string, value: string | number | null } = { label: 'All', value: null }) => {
  if ([undefined, null, 0].includes(dictList[0]?.value as number)) {
    return dictList
  }
  dictList.unshift(option)
  return dictList
}
