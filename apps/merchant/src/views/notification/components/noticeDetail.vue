<script setup lang="ts">
const props = withDefaults(defineProps<{
  record: Record<string, any>
  visible: boolean
}>(), {
  record: () => ({}),
  visible: false,
})

const emits = defineEmits<{
  (e: 'onChange', value: { isClose: boolean, [property: string]: any }): void
}>()

const handUpdate = (value: boolean) => {
  emits('onChange', { isClose: value, ...props.record })
}
</script>

<template>
  <Dialog
    v-model:visible="props.visible" modal maximizable header="Notification Details" :style="{ width: '45rem' }"
    @update:visible="(value: boolean) => { handUpdate(value) }"
  >
    <div class="notification-detail">
      <div class="title">
        {{ record?.title }}
      </div>
      <Tag :severity="record?.is_read === 1 ? 'success' : 'danger'" :value="record?.is_read === 1 ? 'READ' : 'UNREAD'" />
      <Divider />
      <div class="content">
        {{ record?.content }}
      </div>
      <Divider />
      <div class="time">
        {{ record?.created_at }}
      </div>
    </div>
    <div class="flex justify-end gap-2">
      <Button severity="warn" type="button" label="Close" size="small" @click="handUpdate(false)" />
    </div>
  </Dialog>
</template>

<style lang="scss" scoped>
.notification-detail {
  width: 100%;
  height: 100%;

  .title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;

  }

  .content {
    width: 100%;
    font-size: 1.15rem;
    color: var(--color-gray-400);
    // max-height: calc(100vh - 500px);
    // overflow-y: auto;
    overflow-wrap: anywhere;
  }

  .time {
    color: var(--color-gray-200);
    font-size: 1rem;
  }
}
</style>
