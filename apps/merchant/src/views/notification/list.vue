<script setup lang="ts">
import type { EditNotify, FlexiratesNotifyListReq } from '@/types/notification'
import Button from 'primevue/button'
import Divider from 'primevue/divider'
import { computed, onMounted, reactive, ref } from 'vue'
import email from '@/assets/merchant/notification/email.svg'
import email_ed from '@/assets/merchant/notification/email_ed.svg'
import email_un from '@/assets/merchant/notification/email_un.svg'
import more from '@/assets/merchant/notification/more.svg'
import trash from '@/assets/merchant/notification/trash.svg'
import BasePaginator from '@/components/common/BasePaginator.vue'
import { useRequestList } from '@/composables/useRequestList'
import { deleteNotify, getNotifyList, markNotify } from '@/services/api/notification'
import { useDictStore } from '@/store/modules/dict'
import { useNotificationStore } from '@/store/modules/notifications'
import NoticeDetail from './components/noticeDetail.vue'

const { getDictByType } = useDictStore()
const notificationStore = useNotificationStore()

const selectedCategory = ref<number | string>('ALL')
const categoriesList = ref<any[]>()

const selectedIds = ref<any[]>([])

const currentRecord = ref<Record<string, any>>({})
const isVisible = ref<boolean>(false)

const searchModel = reactive<FlexiratesNotifyListReq>({
  page: 1,
  page_size: 15,
  keyword: '',
  category: undefined,
})

const {
  list: notificationList,
  loading,
  total,
  search,
  failed,
  failureMessage,
  setSearchParams,
  rewrite,
} = useRequestList<EditNotify, FlexiratesNotifyListReq>({
  requestFn: getNotifyList,
  immediate: false,
  defaultParams: searchModel,
})
// const isSelectedAll = ref(false)
const isSelectedAll = computed(() => {
  return selectedIds.value.length === notificationList?.value?.length
})
const searchFn = (value?: { page?: number, page_size?: number, isKeepSelected?: boolean, [property: string]: any }) => {
  searchModel.page = value?.page ? value?.page as number : searchModel.page
  searchModel.page_size = value?.page_size ? value?.page_size as number : searchModel.page_size
  searchModel.keyword = value?.keyword ? value?.keyword as string : searchModel.keyword
  searchModel.category = value?.category ? value?.category as number : searchModel.category
  if (!value?.isKeepSelected) {
    selectedIds.value = []
  }
  setSearchParams(searchModel)
  search()
}

// When delete
const handleDelete = (is_all: number, id?: number | string) => {
  const query: EditNotify = {
    is_all,
    ids: '',
  }
  if (is_all === 1) {
    selectedIds.value = []
  }
  if (is_all === 0 && (!id) && (!selectedIds.value.length)) {
    return window.$toast.add({ severity: 'warn', summary: 'Warning', detail: 'Just pick the data first.' })
  }
  else if (is_all === 0) {
    selectedIds.value = id ? selectedIds.value?.filter(item => item.id !== id) : []
    query.ids = id ? id?.toString?.() : selectedIds.value.toString()
  }
  window.$confirm.require({
    message: `Are you sure you want to delete ${is_all ? 'all' : (id ? 'the notification' : 'selected notifications')}? This action cannot be undone.`,
    header: `Delete ${is_all ? 'All' : (id ? 'The Notifications' : 'Selected Notifications')} `,
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: 'No',
    },
    acceptProps: {
      label: 'Yes',
      severity: 'danger',
    },
    accept: () => {
      deleteNotify(query).then(() => {
        window.$toast.add({ severity: 'success', summary: 'Success', detail: 'successfully' })
        // update list
        searchFn({ isKeepSelected: true })
        // update user notice num
        notificationStore.getUnreadCount()
      })
    },
  })
}

// When Mark
const handleMark = (is_read: number, is_all: number) => {
  const query: EditNotify = {
    is_all,
    is_read,
    ids: '',
  }
  if (is_all === 0 && (!selectedIds.value.length)) {
    return window.$toast.add({ severity: 'warn', summary: 'Warning', detail: 'Just pick the data first.' })
  }
  else if (is_all === 0) {
    query.ids = selectedIds?.value?.toString?.()
  }
  window.$confirm.require({
    message: `Are you sure you want to mark ${is_all ? 'all' : 'selected'} as ${is_read ? 'read' : 'unread'} notifications? This action cannot be undone.`,
    header: `Mark ${is_all ? 'All' : 'Selected'} Notifications`,
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: 'No',
    },
    acceptProps: {
      label: 'Yes',
      severity: 'danger',
    },
    accept: () => {
      markNotify(query).then(() => {
        window.$toast.add({ severity: 'success', summary: 'Success', detail: 'successfully' })
        // update list
        searchFn()
        // update user notice num
        notificationStore.getUnreadCount()
      })
    },
  })
}

// When search update searchModel data
const handleSearch = (event: any) => {
  searchFn({ keyword: event?.target?.value?.trim(), isKeepSelected: false })
}

// When search update searchModel data
const handleCategory = (value: number | string) => {
  if (selectedCategory.value === value) { return }
  selectedCategory.value = value
  searchFn({ category: value === 'ALL' ? undefined : value, page: 1, page_size: 15, isKeepSelected: false })
}

// When change page, update searchModel'pagination data
const handlePageChange = (event: any) => {
  searchFn({ page: event?.page, page_size: event?.rows, isKeepSelected: false })
}

const handleDetail = (record: Record<string, any>) => {
  if (record?.id) {
    currentRecord.value = record
    isVisible.value = true
  }
}

const handleClose = (value: { isClose: boolean, [property: string]: any }) => {
  if (!value.isClose) {
    // update list
    const newList = notificationList.value.map((item: any) => {
      if (item?.id === value?.id) {
        item.is_read = 1
      }
      return item
    })
    rewrite(newList)
    // update user notice num
    markNotify({
      is_read: 1,
      ids: value.id.toString(),
      is_all: 0,
    }).then(() => {
      notificationStore.getUnreadCount()
    })
  }
  isVisible.value = value.isClose
}

// When Select
const handCheckbox = (type: string, value: []) => {
  switch (type) {
    case 'ALL':
      if (value) {
        selectedIds.value = notificationList?.value?.map((item: any) => item?.id)
      }
      else {
        selectedIds.value = []
      }
      break
  }
}
const getCategory = () => getDictByType('notification_merchant_category').then((resp) => {
  categoriesList.value = [
    {
      label: 'ALL NOTIFS',
      value: 'ALL',
    },
    ...resp,
  ]
})
onMounted(() => {
  getCategory()
  search()
})
</script>

<template>
  <Card>
    <template #content>
      <div class="notification-container bg-white">
        <div class="notification-preference">
          <div>
            <div class="title">
              Notification
            </div>
            <div class="subtitle">
              Stay updated with your latest activities.
            </div>
          </div>
          <div class="preference-btn-wrapper">
            <Button
              class="preference-btn" label="MARK ALL AS READ" severity="warn"
              @click="() => { handleMark(1, 1) }"
            />
            <Button
              class="preference-btn" label="DELETE ALL" severity="secondary" variant="outlined"
              @click="() => { handleDelete(1) }"
            />
            <Button
              class="preference-btn" label="SETTINGS" severity="secondary" variant="outlined" icon="pi pi-cog"
              @click="() => { $router.push('/notification/setting') }"
            />
          </div>
        </div>
        <div class="notification-search">
          <div class="search-form-input-group">
            <i class="pi pi-search search-form-input-icon" />
            <InputText class="search-form-input" placeholder="Search Notifications" @keydown.enter="handleSearch" />
          </div>
          <div class="search-form-btn-group" aria-label="Scrollable content" role="region">
            <Button
              v-for="item in categoriesList" :key="item.value" class="search-btn"
              :label="item.label.toUpperCase()" :severity="selectedCategory === item.value ? 'warn' : 'secondary'"
              :variant="selectedCategory === item.value ? undefined : 'outlined'"
              @click="() => { handleCategory(item.value) }"
            />
          </div>
        </div>
        <Divider />

        <div class="notification-message">
          <div class="notification-select">
            <Checkbox
              v-model="isSelectedAll" binary :indeterminate="!isSelectedAll && selectedIds?.length > 0"
              @update:model-value="(value:[]) => handCheckbox('ALL', value)"
            />
            <Image :src="more" width="14px" alt="Image" class="icon" />
            <span>Select</span>
            <Divider layout="vertical" />
            <Button variant="text" @click="() => { handleDelete(0) }">
              <Image :src="trash" width="14px" alt="Image" class="icon" />
              <span>Delete All</span>
            </Button>
            <Divider layout="vertical" />
            <Button variant="text" @click="() => { handleMark(1, 0) }">
              <Image :src="email_ed" width="18px" alt="Image" class="icon" />
              <span>Mark as Read</span>
            </Button>
            <Divider layout="vertical" />
            <Button variant="text" @click="() => { handleMark(0, 0) }">
              <Image :src="email" width="21px" alt="Image" class="icon" />
              <span>Mark as Unread</span>
            </Button>
          </div>
          <div class="notification-content" :class="loading ? 'loading' : ''">
            <i v-if="loading" class="pi pi-spin pi-spinner loading-icon" />
            <div v-if="notificationList?.length" class="flex flex-col gap-1">
              <div
                v-for="(record) in notificationList" :key="record?.id" :class="!record?.is_read ? 'active' : ''"
                class="flex center gap-2 content-item"
              >
                <Checkbox
                  v-model="selectedIds" name="notice" :value="record.id" class="icon"
                  @update:model-value="(value: any) => { handCheckbox('ONE', value) }"
                />
                <div class="icon-wrapper">
                  <Image v-if="!!record?.is_read" :src="email_ed" width="20px" alt="Image" class="icon" />
                  <OverlayBadge v-else-if="!record?.is_read" severity="danger">
                    <Image class="icon" :src="email_un" width="20px" alt="Image" />
                  </OverlayBadge>
                </div>
                <div class="title" :title="record?.title" @click="() => { handleDetail(record) }">
                  <span :class="record?.is_fail ? 'fail-msg' : ''">{{ record?.title || 'Title' }}</span>
                </div>
                <div class="desc flex1" :class="record?.is_fail ? 'fail-msg' : ''">
                  {{ record?.abstract || '' }}
                </div>
                <Button
                  variant="text" style="padding: 4px 10px;"
                  @click="() => { handleDelete(0, record?.id) }"
                >
                  <Image :src="trash" alt="Image" />
                </Button>
                <div class="desc">
                  {{ record?.created_at || 'unknown' }}
                </div>
              </div>
            </div>
            <BaseEmpty v-else-if="!notificationList?.length" :message="failed ? failureMessage : undefined" />
          </div>
        </div>
        <BasePaginator
          :rows="searchModel?.page_size" :page="searchModel.page || 1" :total="total"
          size="small" @page-change="handlePageChange"
        />
      </div>
    </template>
    <NoticeDetail :record="currentRecord" :visible="isVisible" @on-change="(e: any) => { handleClose(e) }" />
  </Card>
</template>

<style scoped lang='scss'>
@use '@/styles/mixins/breakpoints' as *;

.notification-container {
  &.bg-white {
    background-color: var(--color-white);
  }

  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;

  .notification-preference {
flex: 1;
align-self: center;

    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.06rem;

    .preference-btn-wrapper {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    .preference-btn {
      margin-left: 1.63rem;
      width: 11.18425rem;
      // height: 2.0625rem;
      flex-shrink: 0;
      font-family: Inter;
      font-weight: 600;
      font-style: Semi Bold;
      font-size: 0.9375rem;
      line-height: 100%;
      letter-spacing: 0%;
      text-align: center;
      // color: #545454;
      border-radius: 0.65rem;

      @include media-breakpoint-down(md) {
        margin-left: 0.6rem;
        width: 7rem;
        height: 2.82rem;
        font-size: 0.75rem;
      }

      @include media-breakpoint-down(sm) {
        margin-left: 0.2rem;
        font-size: 0.7rem;
      }

    }

    .title {
      font-weight: 800;
      font-size: 1.5625rem;
      // color: #181349;
      color: var(--color-indigo-950);
    }

    .subtitle {
      font-weight: 400;
      font-size: 0.9375rem;
      // color: #545454;
      color: var(--color-gray-500);
    }
  }

  .notification-search {
    width: 100%;
    height: 2.50681rem;
    display: flex;
    align-items: center;
    margin-bottom: 1.19rem;
    justify-content: flex-start;

    .search-form-btn-group {
      display: flex;
      flex-wrap: nowrap;
      white-space: nowrap;
      overflow-x: auto;

      /* 设置整个滚动条的样式 */
      &::-webkit-scrollbar {
        width: 0.2rem;
        /* 滚动条的宽度 */
        height: 0.2rem;
        /* 滚动条的高度 */
      }

      /* 滚动条轨道 */
      &::-webkit-scrollbar-track {
        background: transparent;
        /* 轨道背景色 */
        border-radius: 0.625rem;
        /* 圆角 */
      }

      /* 滚动条滑块 */
      &::-webkit-scrollbar-thumb {
        // background: #f1f1f1;
        background: var(--color-zinc-50);
        /* 滑块颜色 */
        border-radius: 0.625rem;
        /* 圆角 */
      }

      /* 当鼠标悬停在滑块上 */
      &::-webkit-scrollbar-thumb:hover {
        // background: #555;
        background: var(--color-gray-400);
        /* 悬停效果颜色 */
      }

      .search-btn {
        &:first-of-type {
          margin-left: 0;
        }

        width: 8.6745rem;
        height: 2.5rem;
        margin-left: 1.03rem;
        flex-shrink: 0;
        border-radius: 0.85rem;
        font-family: Inter;
        font-weight: 600;
        font-style: Semi Bold;
        font-size: 0.9375rem;
        letter-spacing: 0%;
        text-align: center;

        @include media-breakpoint-down(md) {
          margin-left: 0.6rem;
          width: 6rem;
          height: 2.2rem;
          font-size: 0.75rem;
        }

        @include media-breakpoint-down(sm) {
          margin-left: 0.4rem;
          width: 4rem;
          height: 1.85rem;
          font-size: 0.7rem;

        }
      }
    }
  }

  .notification-message {
    margin-top: 0.25rem;

    .notification-select {
      display: flex;
      align-items: center;
      padding: 0 1.14rem;
      margin-bottom: 0.81rem;

      >button,
      span {
        // color: #545454;
        color: var(--color-gray-500);
        font-weight: 400;
        font-size: 0.9375rem;
      }

      .icon {
        &:first-of-type {
          margin: 0 0.9375rem;
        }

        margin:0 0.9375rem 0 0;
        text-align: center;
      }
    }

    .notification-content {
      width: 100%;
      position: relative;

      .loading-icon {
        display: block;
        position: absolute;
        top: calc(50% - 16px);
        left: calc(50% - 16px);
        font-size: 32px;
        color: var(--color-white);
        z-index: 99999999;
      }

      &.loading::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1;
      }

      .content-item {
        cursor: default;
        align-items: center;
        padding: 0 1.14rem;

        &.active {
          // background-color: #F5F5FF;
          background-color: var(--color-white-100);
        }

        .title {
          width: 18%;
          margin-right: 8px;

          >span {
            display: inline-block;
            font-weight: 600;
            font-style: SemiBold;
            font-size: 1.125rem;
            line-height: 100%;
            letter-spacing: 0%;
            max-width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            border-bottom: 0.0625rem solid transparent;
            margin-bottom: - 0.0625rem;
            transition: border-bottom 0.2s ease;

            &.fail-msg {
              font-weight: 600;
              font-style: SemiBold;
              font-size: 1.125rem;
              line-height: 100%;
              letter-spacing: 0%;
              // color: #EB001B;
              color: var(--color-red-600);

              &:hover {
                border-bottom: 0.0625rem solid var(--color-red-600);
              }
            }

            &:hover {
              // border-bottom: 0.0625rem solid #545454;
              border-bottom: 0.0625rem solid var(--color-gray-300);
            }
          }

        }

        .desc {
          font-weight: 400;
          font-style: Regular;
          font-size: 0.9375rem;

          &.fail-msg {
            // color: #181349;
            // color: #EB001B;
            color: var(--color-red-600);
            font-size: 15px;
            font-weight: 600;
          }
        }

        .icon-wrapper {
          margin: 0 14px;
          justify-content: center;
          text-align: center;
        }

        .flex1 {
          flex: 1;
        }
      }
    }
  }

  .search-form-input-group {
    position: relative;
    margin-right: 0.94rem;
    width: 25.25rem;
    height: 2.5rem;

    @include media-breakpoint-down(md) {
      margin-right: 0.6rem;
      width: 21rem;
      height: 2.2rem;
    }

    @include media-breakpoint-down(sm) {
      margin-right: 0.4rem;
      width: 18.25rem;
      height: 1.85rem;
    }

    .search-form-input-icon {
      position: absolute;
      top: 50%;
      left: 1.34rem;
      transform: translateY(-50%);
      z-index: 2;
      font-size: 0.9375rem;
      color: var(--p-button-warn-background);
    }

    .search-form-input {
      width: 100%;
      height: 100%;
      position: relative;
      border-radius: 0.85rem;
      font-size: 0.9375rem;
      padding-left: 2.8rem;
    }
  }

  .p-divider-horizontal {
    margin: 0;
    // color: #000000;
    color: var(--color-black);
  }

  .p-divider-horizontal:before {
    border-block-start: 0.0625rem solid var(--color-black);
  }

  .p-button-outlined.p-button-secondary {
    // border-color: #545454;
    // color: #545454;
    border-color: var(--color-gray-500);
    color: var(--color-gray-500);
  }
}

:deep(.notification-message .p-checkbox-checked .p-checkbox-box) {
  background: transparent;
}

:deep(.notification-message .p-checkbox-checked .p-checkbox-icon) {
  color: var(--color-black);
}

:deep(.notification-message .p-checkbox-box) {
  background: transparent;
}

:deep(.notification-message .p-checkbox-checked:not(.p-disabled):has(.p-checkbox-input:hover) .p-checkbox-box) {
  background: var(--color-orange-500);
  border-color: var(--color-orange-500);
}
</style>
