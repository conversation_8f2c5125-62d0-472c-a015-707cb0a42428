<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup'
import { storeToRefs } from 'pinia'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { onMounted, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import * as yup from 'yup'
import { useListRefresh } from '@/composables/useListRefresh'
import { user as userApi, userRole } from '@/services/api'
import { useUserStore } from '@/store/modules/user'

defineOptions({
  name: 'UserCreate',
})

const { t } = useI18n()
const toast = useToast()
const router = useRouter()
const formRef = ref()
const loading = ref(false)
const userStore = useUserStore()

const { groupList } = storeToRefs(userStore)

const { backWithRefresh } = useListRefresh('usersList', () => {})

// 表单初始值
const initialValues = reactive({
  user_name: '',
  email: '',
  password: '',
  to_fa_check: 0,
  // roles: [],
  business_ids: [],
})

// 角色选项
const roles = ref<{ name: string, id: number }[]>([])

// 获取角色列表
const fetchRoles = async () => {
  const response = await userRole.getList({
    page: 1,
    page_size: 100,
  })
  roles.value = (response.data?.data || []).map((role: UserRole.Info) => ({
    name: role.name,
    id: role.id as number,
  }))
}

onMounted(() => {
  fetchRoles()
})

// 表单验证schema
const schema = toTypedSchema(yup.object({
  user_name: yup.string()
    .min(3, t('validation.minLength', { min: 3 }))
    .max(50, t('validation.maxLength', { max: 50 }))
    .required(),
  email: yup.string()
    .email(t('validation.emailInvalid'))
    .required(t('validation.emailRequired')),
  password: yup.string()
    .min(8, t('validation.passwordMinLength'))
    .matches(/[A-Z]/, t('validation.passwordUppercase'))
    .matches(/[a-z]/, t('validation.passwordLowercase'))
    .matches(/\d/, t('validation.passwordNumber'))
    .required(),
  to_fa_check: yup.number().min(0, '2FA is required').required(),
  business_ids: yup.array().optional(),
  // roles: yup.array().of(yup.object({ name: yup.string(), id: yup.number() })).min(1, 'Role is required'),
}))

// 提交表单
const submitForm = async () => {
  loading.value = true

  try {
    const result = await formRef.value?.validate()

    if (!result.valid) {
      loading.value = false
      return
    }

    const formData = {
      ...result.values,
      mfa_check: result.values.to_fa_check,
      // roles: result.values.roles.map((role: { id: number }) => role.id),
    }

    const { code } = await userApi.createUser(formData)

    if (code === 0) {
      toast.add({
        severity: 'success',
        summary: t('common.success'),
        detail: t('merchant.messages.createSuccess', 'Merchant created successfully'),
      })

      backWithRefresh()
    }
  }
  catch (error) {
    console.error('Failed to Create a user:', error)
    toast.add({
      severity: 'error',
      summary: t('common.error'),
      detail: t('merchant.messages.createFailed', 'Failed to Create a user'),
    })
  }
  finally {
    loading.value = false
  }
}

// 取消操作
const cancel = () => {
  router.back()
}
</script>

<template>
  <div class="merchant-create-page">
    <div class="p-4 bg-white rounded-2xl">
      <VeeForm
        ref="formRef"
        :validation-schema="schema"
        class="merchant-form flex flex-col gap-4 "
        @submit="submitForm"
      >
        <!-- 用户名 -->
        <Field v-slot="{ field, errorMessage }" v-model="initialValues.user_name" name="user_name" class="form-col">
          <div class="field">
            <label for="user_name" class="mb-2 block">{{ t('merchant.form.name', 'Username') }} <span class="text-red-500">*</span></label>
            <InputText
              id="user_name"
              v-bind="field"
              :placeholder="t('merchant.form.name', 'Username')"
              class="w-full"
              :class="{ 'p-invalid': errorMessage }"
              :input-props="{
                autocomplete: 'new-password',
              }"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- 邮箱 -->
        <Field v-slot="{ field, errorMessage }" v-model="initialValues.email" name="email" class="form-col">
          <div class="field">
            <label for="email" class="mb-2 block">{{ t('merchant.form.email', 'Email') }}*</label>
            <InputText
              id="email"
              v-bind="field"
              :placeholder="t('merchant.form.email', 'Email')"
              class="w-full"
              type="email"
              :class="{ 'p-invalid': errorMessage }"
              :input-props="{
                autocomplete: 'new-password',
              }"
              autocomplete="new-password"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- 密码 -->
        <Field v-slot="{ field, errorMessage, handleChange }" v-model="initialValues.password" name="password" class="form-col">
          <div class="field">
            <label for="password" class="mb-2 block">{{ t('merchant.form.password', 'Password') }}*</label>
            <Password
              id="password"
              :model-value="field.value"
              :placeholder="t('merchant.form.password', 'Password')"
              class="w-full"
              toggle-mask
              :root="{
                autocomplete: 'new-password',
              }"
              :input-props="{
                autocomplete: 'new-password',
                class: {
                  'p-invalid': errorMessage,
                },
              }"
              @update:model-value="handleChange"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>
        <!-- 2FA认证 -->
        <Field v-slot="{ errorMessage }" v-model="initialValues.to_fa_check" name="to_fa_check" as="div" class="form-col">
          <div class="field">
            <label class="mb-2 block">{{ t('merchant.form.2fa', '2FA Authentication') }}</label>
            <Select
              v-model="initialValues.to_fa_check"
              :options="[
                { label: 'Enabled', value: 1 },
                { label: 'Disabled', value: 0 },
              ]"
              option-label="label"
              option-value="value"
              class="w-full"
              name="to_fa_check"
              :placeholder="t('merchant.form.select2FA', 'Select 2FA Option')"
            />
          </div>
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>

        <!-- 角色 -->
        <!-- <Field v-slot="{ field, errorMessage, handleChange }" v-model="initialValues.roles" name="roles" class="form-col">
          <div class="field mb-4">
            <label for="roles" class="mb-2 block">{{ t('merchant.form.roles', 'Roles') }}</label>
            <MultiSelect
              :model-value="field.value"
              :options="roles"
              option-label="name"
              :placeholder="t('merchant.form.selectRoles', 'Select Roles')"
              display="chip"
              class="w-full"
              name="roles"
              @update:model-value="handleChange"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field> -->

        <!-- Assign to Business -->
        <Field v-slot="{ field, errorMessage, handleChange }" v-model="initialValues.business_ids" name="business_ids" class="form-col">
          <div class="field mb-4">
            <label for="bids" class="mb-2 block">Assign to Business</label>
            <MultiSelect
              :model-value="field.value"
              :options="groupList.map(item => {
                return {
                  ...item,
                  label: `${item.label} - ${item.value}`,
                }
              })"
              option-label="label"
              option-value="value"
              class="w-full"
              placeholder="Select Assign to Business"
              :class="{ 'p-invalid': errorMessage }"
              @update:model-value="handleChange"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- 表单按钮 -->
        <div class="flex justify-end mt-6 gap-2 ">
          <Button
            type="button"
            :label="t('common.cancel')"
            icon="pi pi-times"
            class="p-button-text"
            @click="cancel"
          />
          <Button
            severity="warn"
            type="submit"
            :label="t('common.save')"
            icon="pi pi-check"
            :loading="loading"
          />
        </div>
      </VeeForm>
    </div>
  </div>
</template>

<style scoped>
.merchant-form {
  max-width: 600px;
}

.form-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.form-col {
  flex: 1;
  min-width: 250px; /* 确保列不会太窄 */
}

.w-full {
  width: 100%;
}

:deep(.p-password) {
  width: 100%;
}

:deep(.p-password-input) {
  width: 100%;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-col {
    min-width: 100%;
  }
}

/* 小型移动设备 */
@media screen and (max-width: 480px) {
  :deep(.p-float-label) {
    font-size: 0.9rem;
  }

  :deep(.p-inputtext) {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}
</style>
