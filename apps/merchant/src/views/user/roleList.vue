<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { HttpResponse } from '@/services/http'
import { useToast } from 'primevue/usetoast'

import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import { useListRefresh } from '@/composables/useListRefresh'
import { usePermissions } from '@/composables/usePermissions'
import { useRequestList } from '@/composables/useRequestList'
import { Permissions } from '@/constants/permissions'
import { SearchFieldType } from '@/constants/search'
import { userRole } from '@/services/api'
import { formatDate } from '@/utils/date'

defineOptions({
  name: 'merchantRoleList',
})

const { hasPermission, hasAnyPermission } = usePermissions()

const { t } = useI18n()
const toast = useToast()
const router = useRouter()

// 使用 useRequestList 处理角色列表数据
const requestList = useRequestList<UserRole.Info[], Api.UserRoleListReq>({
  requestFn: userRole.getList as unknown as (params: Api.UserRoleListReq) => Promise<HttpResponse<CommonListRes<UserRole.Info[]>>>,
})

const {
  list,
  loading,
  total,
  refresh,
  reset,
  setSearchParams,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
} = requestList

useListRefresh('userRoleList', reset)

// 列配置
const columns = ref<TableColumnItem[]>([
  {
    field: 'name',
    header: t('merchant.role.columns.name', 'Role Name'),
    style: { minWidth: '150px' },
    sortable: true,
    sortField: 'name',
  },
  {
    field: 'slug',
    header: t('merchant.role.columns.slug', 'Slug'),
    style: { minWidth: '150px' },
  },
  {
    field: 'created_at',
    header: t('common.created', 'Created Date'),
    style: { minWidth: '150px' },
    sortable: true,
    template: 'date',
    sortField: 'created_at',
  },
  {
    field: 'action',
    header: '',
    style: { width: '60px' },
    template: 'action',
    alignFrozen: 'right',
    frozen: true,
  },
])

const searchModel = ref<Partial<Api.UserRoleListReq>>({
  'name': '',
  'created_at[]': [],
})

const searchFields = computed(() => [
  {
    name: 'name',
    label: t('merchant.role.columns.name', 'Role Name'),
    type: SearchFieldType.TEXT,
    placeholder: t('merchant.role.search.namePlaceholder', 'Search by role name'),
    maxlength: 50,
    defaultValue: '',
  },
  {
    name: 'created_at[]',
    label: t('common.created', 'Created Date'),
    type: SearchFieldType.DATE_RANGE,
    placeholder: t('merchant.search.datePlaceholder', 'Select Date Range'),
    defaultValue: [],
  },
])

// 搜索处理
const handleSearch = (params: Record<keyof Api.UserRoleListReq, any>) => {
  setSearchParams(params)
  search()
}

// 排序处理
const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  requestList.setParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  requestList.search()
}

// 导航到创建页面
const navigateToCreate = () => {
  if (!hasPermission(Permissions.ROLE_CREATE)) {
    return
  }
  router.push({ name: 'userRoleCreate' })
}

// 导航到编辑页
const navigateToEdit = (data: UserRole.Info) => {
  if (!hasPermission(Permissions.ROLE_UPDATE)) {
    return
  }
  router.push({
    name: 'userRoleEdit',
    params: {
      id: data.id,
    },
  })
}

// 删除确认对话框
const deleteDialog = ref(false)
const selectedRole = ref<UserRole.Info | null>(null)

// 确认删除
const confirmDelete = (data: UserRole.Info) => {
  if (!hasPermission(Permissions.ROLE_DELETE)) {
    return
  }
  selectedRole.value = data
  deleteDialog.value = true
}

// 执行删除
const deleteRole = () => {
  if (!selectedRole.value?.id) { return }

  deleteDialog.value = false

  userRole.remove(String(selectedRole.value.id)).then((_res) => {
    toast.add({
      severity: 'success',
      summary: t('common.success', 'Success'),
      detail: t('merchant.role.messages.deleteSuccess', 'Role deleted successfully'),
    })
    refresh()
  }).catch((error) => {
    console.error('Failed to delete role:', error)
    toast.add({
      severity: 'error',
      summary: t('common.error', 'Error'),
      detail: t('merchant.role.messages.deleteFailed', 'Failed to delete role'),
    })
  })

  selectedRole.value = null
}
</script>

<template>
  <div class="merchant-role-list-page">
    <BaseSearch
      v-model="searchModel"
      :loading="loading"
      :basic-search-fields="searchFields"
      @search="handleSearch"
    />

    <div v-if="hasPermission(Permissions.ROLE_CREATE)" class="flex gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <Button label="Create a Role" icon="pi pi-plus" severity="warn" class="!px-8" @click="navigateToCreate" />
    </div>

    <BaseDataTable
      :columns="columns"
      :value="list"
      :row-hover="true"
      :loading="loading"
      :total-records="total"
      :paginator="true"
      :rows="50"
      :lazy="true"
      data-key="id"
      :show-search-bar="false"
      :show-multiple-column="false"
      :scrollable="true"
      search-placeholder="Search roles..."
      :failed="failed"
      :striped-rows="false"
      :failure-message="failureMessage"
      @sort="handleSort"
      @page="(e: DataTablePageEvent) => handlePageChange(e)"
    >
      <template #date="{ data }">
        <div>{{ formatDate(data.created_at) }}</div>
      </template>
      <template #action="{ data }">
        <BaseDataTableActions
          v-if="hasAnyPermission([
            Permissions.ROLE_UPDATE,
            Permissions.ROLE_DELETE,
          ]) && data?.slug !== 'administrator'"
          :loading="data.__loading"
          :is-show-delete="data?.slug !== 'administrator' && hasPermission(Permissions.ROLE_DELETE)"
          :is-show-edit="data?.slug !== 'administrator' && hasPermission(Permissions.ROLE_UPDATE)"
          :is-show-detail="false"
          @edit="navigateToEdit(data)"
          @delete="confirmDelete(data)"
        />
      </template>
    </BaseDataTable>

    <!-- 删除确认对话框 -->
    <Dialog
      v-model:visible="deleteDialog"
      :style="{ width: '450px' }"
      :header="t('merchant.role.dialogs.confirmDelete', '确认删除')"
      :modal="true"
    >
      <div class="confirmation-content">
        <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem" />
        <span v-if="selectedRole">
          {{ t('merchant.role.dialogs.deleteConfirmMessage', { name: selectedRole.name }, `确定要删除角色 "${selectedRole?.name}" 吗？该操作不可恢复。`) }}
        </span>
      </div>
      <template #footer>
        <Button :label="t('common.no', '取消')" icon="pi pi-times" text @click="deleteDialog = false" />
        <Button :label="t('common.yes', '确定')" icon="pi pi-check" text @click="deleteRole" />
      </template>
    </Dialog>
  </div>
</template>

<style scoped>
.p-dialog .p-dialog-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem;
}

.p-dialog .p-dialog-footer {
  border-top: 1px solid #dee2e6;
  padding: 1.5rem;
  text-align: right;
}

.p-dialog .p-dialog-content {
  padding: 2rem;
}

.field {
  margin-bottom: 1.5rem;
}

.confirmation-content {
  display: flex;
  align-items: center;
}
</style>
