<script setup lang="ts">
import type { TreeNode } from 'primevue/treenode'
import { toTypedSchema } from '@vee-validate/yup'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { onMounted, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import * as yup from 'yup'
import { useListRefresh } from '@/composables/useListRefresh'
import { userRole as userRoleApi } from '@/services/api'

defineOptions({
  name: 'UserRoleCreate',
})

const { t } = useI18n()
const toast = useToast()
const router = useRouter()
const formRef = ref()
const loading = ref(false)

const { backWithRefresh } = useListRefresh('userRoleList', () => {})

// 表单初始值
const initialValues = reactive({
  name: '',
  slug: '',
  permissions: [],
})

// 权限树数据
const permissions = ref<TreeNode[]>([])

const transformPermissions = (data: UserRole.PermissionListRes[]): TreeNode[] => {
  return data.map((item: UserRole.PermissionListRes) => {
    return {
      key: String(item.id),
      label: item.name,
      data: item,
      children: item?.children?.length ? transformPermissions(item.children) : [],
    }
  })
}

// 获取权限列表
const getPermissionList = async () => {
  const res = await userRoleApi.getPermissionList()
  permissions.value = transformPermissions(res.data)
}

// 表单验证schema
const schema = toTypedSchema(yup.object({
  name: yup.string()
    .min(3, t('validation.minLength', { min: 3 }))
    .max(50, t('validation.maxLength', { max: 50 }))
    .required(),
  slug: yup.string()
    .min(3, t('validation.minLength', { min: 3 }))
    .max(50, t('validation.maxLength', { max: 50 }))
    .matches(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens')
    .required(),
}))

// 提交表单
const submitForm = async () => {
  loading.value = true

  try {
    const result = await formRef.value?.validate()

    // 获取选中的节点
    const checkedKeys = []

    for (const key in initialValues.permissions) {
      if (Object.prototype.hasOwnProperty.call(initialValues.permissions, key)) {
        const element = initialValues.permissions[key] as TreeNode
        if (element.checked) {
          checkedKeys.push(key)
        }
      }
    }

    if (!result.valid) {
      loading.value = false
      return
    }

    // 映射字段名称以匹配API要求
    const formData = {
      role_name: result.values.name,
      role_mark: result.values.slug,
      permissions: checkedKeys,
    }

    const { code } = await userRoleApi.create(formData)

    if (code !== 0) {
      throw new Error('Failed to create role')
    }

    toast.add({
      severity: 'success',
      summary: t('common.success'),
      detail: t('merchant.role.messages.createSuccess', 'Role created successfully'),
      life: 3000,
    })

    backWithRefresh()
  }
  catch (error) {
    console.error('Failed to create role:', error)
  }
  finally {
    loading.value = false
  }
}

// 取消操作
const cancel = () => {
  router.back()
}

onMounted(() => {
  getPermissionList()
})
</script>

<template>
  <div class="merchant-role-create-page">
    <div class="p-4 bg-white rounded-2xl">
      <VeeForm
        ref="formRef"
        :validation-schema="schema"
        class="merchant-role-form flex flex-col gap-4"
        @submit="submitForm"
      >
        <!-- 角色名称 -->
        <Field v-slot="{ field, errorMessage }" v-model="initialValues.name" name="name" class="form-col">
          <div class="field">
            <label for="name" class="mb-2 block">{{ t('merchant.role.columns.name', 'Role Name') }}*</label>
            <InputText
              id="name"
              v-bind="field"
              :placeholder="t('merchant.role.columns.name', 'Role Name')"
              class="w-full"
              autofocus
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- 角色标识 -->
        <Field v-slot="{ field, errorMessage }" v-model="initialValues.slug" name="slug" class="form-col">
          <div class="field">
            <label for="slug" class="mb-2 block">{{ t('merchant.role.columns.slug', 'Slug') }}*</label>
            <InputText
              id="slug"
              v-bind="field"
              :placeholder="t('merchant.role.columns.slug', 'Slug')"
              class="w-full"
            />
            <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </div>
        </Field>

        <!-- 角色权限树 -->

        <Field v-slot="{ errorMessage }" name="permissions" class="form-col">
          <label for="permissions" class="mb-2 block">{{ t('merchant.role.columns.permissions', 'Permissions') }}</label>
          <Tree
            v-model:selection-keys="initialValues.permissions"
            :value="permissions"
            selection-mode="checkbox"
            class="w-full md:w-[30rem] !p-0"
            placeholder="Select permissions"
          >
            <template #content="{ node }">
              {{ node }}
            </template>
          </Tree>
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>

        <!-- 表单按钮 -->
        <div class="flex justify-end mt-6 gap-2">
          <Button
            type="button"
            :label="t('common.cancel')"
            icon="pi pi-times"
            class="p-button-text mr-2"
            @click="cancel"
          />
          <Button
            severity="warn"
            type="submit"
            :label="t('common.save')"
            icon="pi pi-check"
            :loading="loading"
          />
        </div>
      </VeeForm>
    </div>
  </div>
</template>

<style scoped>
.merchant-role-form {
  max-width: 600px;
}

.form-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.form-col {
  flex: 1;
  min-width: 250px;
}

.w-full {
  width: 100%;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-col {
    min-width: 100%;
  }
}

/* 小型移动设备 */
@media screen and (max-width: 480px) {
  :deep(.p-float-label) {
    font-size: 0.9rem;
  }

  :deep(.p-inputtext) {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}
</style>
