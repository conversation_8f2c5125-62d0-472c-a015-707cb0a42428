<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import BaseSearch from '@/components/common/BaseSearch.vue'
import { useDict } from '@/composables/useDict'
import { useListRefresh } from '@/composables/useListRefresh'
import { useRequestList } from '@/composables/useRequestList'
import { SearchFieldType } from '@/constants/search'
import { user as userApi } from '@/services/api'
import { formatDate } from '@/utils/date'

defineOptions({
  name: 'userBidList',
})

const { t } = useI18n()

const router = useRouter()

// 使用 useRequestList 处理商户列表数据
const requestList = useRequestList<User.BidInfo[], Api.BidListReq>({
  requestFn: userApi.getBidList,
})

const {
  list,
  loading,
  total,
  refresh,
  setSearchParams,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
} = requestList

// 使用通用的列表刷新逻辑
useListRefresh('userBidList', refresh)

const options = ref<{
  [key: string]: DictItem[]
}>({
  settlementType: [],
  merchantBusinessStatus: [],
  bpaySwitch: [],
})

// 列配置
const columns = ref<TableColumnItem[]>([
  {
    field: 'merchant_name',
    header: 'Business Name',
  },
  {
    field: 'business_id',
    header: 'Business ID',
  },
  {
    field: 'business_name',
    header: 'Business Name',
    style: { minWidth: '150px' },
  },
  {
    field: 'settlement_type',
    header: 'Settlement Type',
    template: 'settlementType',
  },
  {
    field: 'status',
    header: 'Status',
    template: 'status',
  },
  // {
  //   field: 'bpay_switch',
  //   header: 'Bpay Switch',
  //   template: 'bpaySwitch',
  // },
  {
    field: 'created_at',
    header: t('common.created', 'Created Date'),
    style: { minWidth: '150px' },
    sortable: true,
    template: 'date',
    sortField: 'created_at',
  },
])

const searchModel = ref<Partial<Api.BidListReq>>({
  'merchant_name': '',
  'business_id': '',
  'settlement_type': '',
  'status': '',
  // 'bpay_switch': '',
  'created_at[]': [],
})

// 配置搜索字段
const searchFields = computed(() => [
  {
    name: 'bid',
    label: 'Business ID',
    type: SearchFieldType.TEXT,
    placeholder: 'Search for Business ID',
    maxlength: 50,
    defaultValue: '',
  },
])

// 排序处理
const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  requestList.setParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  requestList.search()
}

// 获取状态样式
const getStatusSeverity = (status: number | undefined) => {
  if (status === undefined) { return 'upcoming' }

  const severityMap: Record<string, string> = {
    1: 'paid',
    0: 'failed',
  }
  return severityMap[String(status)] || 'upcoming'
}

// 导航到详情页
const navigateToDetail = ({ data: row }: { data: User.BidInfo }) => {
  if (row.status !== 1) {
    window.$toast.add({
      severity: 'warn',
      summary: 'Error',
      detail: 'Merchant is not enabled',
    })
    return
  }
  router.push({
    name: 'userBidDetail',
    params: {
      id: row.id.toString(),
    },
  })
}

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

const { getLabel: getSettlementTypeLabel } = useDict('settlement_type', (res) => {
  options.value.settlementType = res
})

const { getLabel: getMerchantBusinessStatusLabel } = useDict('merchant_business_status', (res) => {
  options.value.merchantBusinessStatus = res
})

const { getLabel: getBpaySwitchLabel } = useDict('bpay_switch')

const showCreateDialog = ref(false)
const formData = ref({
  business_name: '',
  location: '',
})

const createBid = () => {
  showCreateDialog.value = true
  formData.value = {
    business_name: '',
    location: '',
  }
}

const handleCreateBid = async () => {
  if (!formData.value.business_name.trim() || !formData.value.location.trim()) {
    window.$toast.add({
      severity: 'warn',
      summary: 'Warning',
      detail: 'Please fill in all required fields',
    })
    return
  }

  const { code } = await userApi.createBid({
    business_name: formData.value.business_name,
    location: formData.value.location,
  })

  if (code === 0) {
    window.$toast.add({
      severity: 'success',
      summary: t('common.success'),
      detail: t('merchant.messages.createSuccess', 'Merchant created successfully'),
    })
    showCreateDialog.value = false
    refresh()
  }
}
</script>

<template>
  <div class="merchant-list-page">
    <BaseSearch
      v-model="searchModel"
      :loading="loading"
      :basic-search-fields="searchFields"
      @search="handleSearch"
    />

    <div class="flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <Button
        label="Add New Business"
        class="!px-8"
        severity="warn"
        icon="pi pi-plus"
        @click="createBid"
      />
    </div>

    <BaseDataTable
      :columns="columns"
      :value="list"
      :loading="loading"
      :total-records="total"
      :paginator="true"
      :rows="50"
      :lazy="true"
      data-key="id"
      :show-search-bar="false"
      :scrollable="true"
      :show-multiple-column="false"
      search-placeholder="Search merchants..."
      :failed="failed"
      :failure-message="failureMessage"
      :striped-rows="true"
      :row-hover="true"
      @sort="handleSort"
      @page="(e: DataTablePageEvent) => handlePageChange(e)"
      @row-click="navigateToDetail"
    >
      <template #status="{ data }">
        <BaseTag :text="getMerchantBusinessStatusLabel(data.status)" :type="getStatusSeverity(data.status)" />
      </template>
      <template #settlementType="{ data }">
        {{ getSettlementTypeLabel(data.settlement_type) }}
      </template>
      <template #bpaySwitch="{ data }">
        {{ getBpaySwitchLabel(data.bpay_switch) }}
      </template>
      <template #date="{ data }">
        <div>{{ formatDate(data.created_at) }}</div>
      </template>
    </BaseDataTable>

    <!-- Add New Business Dialog -->
    <Dialog
      v-model:visible="showCreateDialog"
      header="Add New Business"
      :style="{ width: '450px' }"
      modal
    >
      <div class="space-y-4">
        <div class="field">
          <label for="business_name" class="block font-medium mb-2">Business Name *</label>
          <InputText
            id="business_name"
            v-model="formData.business_name"
            class="w-full"
            placeholder="Enter business name"
          />
        </div>

        <div class="field">
          <label for="location" class="block font-medium mb-2">Location *</label>
          <InputText
            id="location"
            v-model="formData.location"
            class="w-full"
            placeholder="Enter location"
          />
        </div>
      </div>

      <template #footer>
        <Button
          label="Cancel"
          icon="pi pi-times"
          severity="secondary"
          @click="showCreateDialog = false"
        />
        <Button
          label="Create"
          icon="pi pi-check"
          severity="warn"
          @click="handleCreateBid"
        />
      </template>
    </Dialog>
  </div>
</template>

<style scoped>
.p-dialog .p-dialog-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem;
}

.p-dialog .p-dialog-footer {
  border-top: 1px solid #dee2e6;
  padding: 1.5rem;
  text-align: right;
}

.p-dialog .p-dialog-content {
  padding: 2rem;
}

.field {
  margin-bottom: 1.5rem;
}

.confirmation-content {
  display: flex;
  align-items: center;
}
</style>
