import type { Theme } from '@/views/communication/theme'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useListRefresh } from '@/composables/useListRefresh'
import { customer as customerApi, plan as planApi } from '@/services/api'
import { useUserStore } from '@/store/modules/user'
import { THEME_COLOR_MAP, THEME_COLORS } from '@/views/communication/theme'

interface submitState {
  customer_name: string
  email_primary: string
  phone_mobile?: string
  plan?: Api.CustomerCreateReqPlan[]
  theme?: Theme
  logo?: string | FileUpload.UploadFileItem[]
  units?: number
  surcharge_rate?: {
    fee_rate: string
    fee_value: string
  }
  gst?: {
    fee_rate: string
    fee_value: string
  }
}

export enum FormStep {
  PlanSubscription = '1',
  ConfirmFormat = '2',
  Success = '3',
}

export function useCustomerForm(type: 'create' | 'invite') {
  const { activeBid } = useUserStore()

  const router = useRouter()

  const { withRefresh } = useListRefresh('customersList', () => {})
  // 加载和提交状态
  const loading = ref(false)
  const submitting = ref(false)
  const step = ref<FormStep>(FormStep.PlanSubscription)

  const customerId = ref<string | null>(null)

  const state = ref<submitState>({
    customer_name: '',
    email_primary: '',
    phone_mobile: '',
    plan: [],
    theme: {
      key: 'light_orange',
      backgroundColor: THEME_COLOR_MAP.light_orange.backgroundColor,
      color: THEME_COLOR_MAP.light_orange.color,
    },
    logo: [],
    units: 1,
    surcharge_rate: {
      fee_rate: '',
      fee_value: '',
    },
    gst: {
      fee_rate: '',
      fee_value: '',
    },
  })

  const getLogoUrl = () => {
    if (state.value.logo && state.value.logo.length > 0) {
      return (state.value.logo[0] as FileUpload.UploadFileItem).url
    }
    return ''
  }

  const handleSubmit = async (formData: Api.PlanCreateReq) => {
    const isNoPlan = Object.keys(formData).length === 0
    try {
      if (type === 'create') {
        submitting.value = true
        const { code, data } = await customerApi.create({
          customer_name: state.value.customer_name,
          email_primary: state.value.email_primary,
          phone_mobile: state.value.phone_mobile,
          plan: isNoPlan
            ? null
            : {
                logo: state.value.logo,
                theme: JSON.stringify(state.value.theme),
                ...formData,
                units: state.value.units,
              },
        }, {
          headers: {
            'Business-Id': activeBid,
          },
        })
        if (code === 0) {
          window.$toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Customer created successfully',
          })
          if (isNoPlan) {
            withRefresh()
            router.back()
            return
          }
          customerId.value = data.customer_id
          step.value = FormStep.ConfirmFormat
          withRefresh()
        }
        else {
          submitting.value = false
        }
      }
      else if (type === 'invite') {
        submitting.value = true
        const { code: planCode } = await planApi.createByCustomer({
          ...formData,
          customer_id: customerId.value as string,
          units: state.value.units,
        }, {
          headers: {
            'Business-Id': activeBid,
          },
        })
        if (planCode === 0) {
          window.$toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Subscription created successfully',
          })
          step.value = FormStep.ConfirmFormat
          withRefresh()
        }
      }
    }
    catch (error) {
      console.error(error)
    }
    finally {
      submitting.value = false
    }
  }

  const sendEmailInvite = async () => {
    if (!customerId.value) {
      return
    }
    submitting.value = true
    try {
      const { code } = await customerApi.sendEmailInvite({
        customer_id: customerId.value as string,
        logo: getLogoUrl(),
        theme: JSON.stringify(state.value.theme),
      })
      if (code === 0) {
        window.$toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Email sent successfully',
        })
        step.value = FormStep.Success
      }
    }
    finally {
      submitting.value = false
    }
  }

  function setCustomerId(id: string) {
    customerId.value = id
  }

  async function fetchCustomerConfig() {
    const { code, data } = await customerApi.getCustomerConfig()
    if (code === 0) {
      state.value.logo = [{ url: data.logo }]
      try {
        state.value.theme = JSON.parse(data.theme)
      }
      catch {
        state.value.theme = {
          key: THEME_COLORS.CUSTOM,
          backgroundColor: data.theme,
          color: THEME_COLOR_MAP.light_orange.color,
        }
      }
      const findConfig = data.fee_config?.find(item => item.business_id === activeBid)
      state.value.surcharge_rate = findConfig?.surcharge_rate
      state.value.gst = findConfig?.gst
    }
  }

  fetchCustomerConfig()

  return {
    loading,
    submitting,
    step,
    state,
    handleSubmit,
    setCustomerId,
    customerId,
    sendEmailInvite,
  }
}
