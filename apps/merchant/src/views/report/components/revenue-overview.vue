<script setup lang="ts">
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts'
import { GraphicComponent, GridComponent, LegendComponent, TitleComponent, ToolboxComponent, TooltipComponent } from 'echarts/components'
import * as echarts from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { getRevenue } from '@/services/api/report'
import { formatNumber, formatYAxis } from '@/utils/format'

const router = useRouter()

echarts.use([TitleComponent, GridComponent, BarChart, CanvasRenderer, GraphicComponent, LegendComponent, ToolboxComponent, LineChart, TooltipComponent])

const grossChart = ref<echarts.ECharts>()
const grossOption = ref({
  title: {
    text: 'Gross Transaction Value ($)',
    subtext: '',
    left: '10px',
    top: '10px',
    textStyle: {
      fontSize: 14,
    },
    subtextStyle: {
      fontSize: 28,
      fontWeight: 'bold',
      color: '#333',
      lineHeight: 36,
      height: 36,
    },
  },
  graphic: [
    {
      type: 'text',
      left: 'center',
      top: '50px',
      height: 28,
      lineHeight: 28,
      textVerticalAlign: 'middle',
      style: {
        text: '',
        fill: '#ff1111', // 标题颜色
      },
    },
  ],
  grid: {
    bottom: '20px',
    left: '60px',
    right: '20px',
    top: '100px',
  },
  color: ['#9373f6'],
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: formatYAxis,
    },
    max: 250,
    interval: 50,
  },
  series: [
    {
      data: [1],
      type: 'bar',
    },
  ],
})

const amountChart = ref<echarts.ECharts>()
const amountOption = ref({
  title: {
    text: 'Net Transaction Value ($)',
    subtext: '',
    left: '10px',
    top: '10px',
    textStyle: {
      fontSize: 14,
    },
    subtextStyle: {
      fontSize: 28,
      fontWeight: 'bold',
      color: '#333',
      lineHeight: 36,
      height: 36,
    },
  },
  graphic: [
    {
      type: 'text',
      left: 'center',
      top: '50px',
      height: 28,
      lineHeight: 28,
      textVerticalAlign: 'middle',
      style: {
        text: '',
        fill: '#ff1111', // 标题颜色
      },
    },
  ],
  grid: {
    bottom: '20px',
    left: '60px',
    right: '20px',
    top: '100px',
  },
  color: ['#9373f6'],
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: formatYAxis,
    },
    max: 250,
    interval: 50,
  },
  series: [
    {
      data: [1],
      type: 'bar',
    },
  ],
})

const breakdownChart = ref<echarts.ECharts>()
const breakdownOption = ref({
  title: {
    text: 'Gross volume by month',
    left: '10px',
    top: '10px',
    textStyle: {
      fontSize: 14,
    },
  },
  grid: {
    bottom: '20px',
    left: '60px',
    right: '40px',
    top: '45px',
  },
  color: ['#9373f6', '#dc9256'],
  xAxis: [
    {
      type: 'category',
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      axisPointer: {
        type: 'shadow',
      },
      axisLabel: {
        formatter: (value: string, index: number) => {
          return index === 0 || index === 11 ? value : ''
        },
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
      min: 0,
      max: 250,
      interval: 50,
      axisLabel: {
        formatter: formatYAxis,
      },
    },
    {
      type: 'value',
      min: 0,
      max: 25,
      interval: 5,
      axisLabel: {
        formatter: '{value} %',
      },
    },
  ],
  series: [
    {
      name: 'Evaporation',
      type: 'bar',
      data: [
        2.0,
        4.9,
        7.0,
        23.2,
        25.6,
        76.7,
        135.6,
        162.2,
        32.6,
        20.0,
        6.4,
        3.3,
      ],
    },
    {
      name: 'Temperature',
      type: 'line',
      yAxisIndex: 1,

      data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2],
    },
  ],
  toolbox: {
    show: true,
    itemSize: 15,
    feature: {
      myTool2: {
        show: true,
        title: 'Expand',
        icon: 'path://M432.45,595.444c0,2.177-4.661,6.82-11.305,6.82c-6.475,0-11.306-4.567-11.306-6.82s4.852-6.812,11.306-6.812C427.841,588.632,432.452,593.191,432.45,595.444L432.45,595.444z M421.155,589.876c-3.009,0-5.448,2.495-5.448,5.572s2.439,5.572,5.448,5.572c3.01,0,5.449-2.495,5.449-5.572C426.604,592.371,424.165,589.876,421.155,589.876L421.155,589.876z M421.146,591.891c-1.916,0-3.47,1.589-3.47,3.549c0,1.959,1.554,3.548,3.47,3.548s3.469-1.589,3.469-3.548C424.614,593.479,423.062,591.891,421.146,591.891L421.146,591.891zM421.146,591.891',
        onclick() {
          window.$toast.add({
            summary: 'myToolHandler2',
            detail: 'myToolHandler2',
            life: 3000,
          })
        },
      },
    },
  },
})

const lastMonthChart = ref<echarts.ECharts>()
const lastQuarterOption = ref({
  title: {
    text: 'Gross volume by quarter',
    left: '10px',
    top: '10px',
    textStyle: {
      fontSize: 14,
    },
  },
  grid: {
    bottom: '50px',
    left: '60px',
    right: '40px',
    top: '50px',
  },
  legend: {
    data: ['Gross volume', 'Year on year'],
    left: 'left',
    bottom: 5,
  },
  color: ['#9373f6', '#dc9256'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  xAxis: [
    {
      type: 'category',
      data: ['Q1', 'Q2', 'Q3', 'Q4'],
      axisPointer: {
        type: 'shadow',
      },

    },
  ],
  yAxis: [
    {
      type: 'value',
      min: 0,
      max: 250,
      interval: 50,
      axisLabel: {
        formatter: formatYAxis,
      },
    },
    {
      type: 'value',
      min: 0,
      max: 25,
      interval: 5,
      axisLabel: {
        formatter: '{value} %',
      },
    },
  ],
  series: [
    {
      name: 'Gross volume',
      type: 'bar',
      data: [
        2.0,
        25.6,
        76.7,
        135.6,
        20.0,
        6.4,
        3.3,
      ],
      tooltip: {
        valueFormatter(value: string) {
          return `${value}$`
        },
      },
    },
    {
      name: 'Year on year',
      type: 'line',
      yAxisIndex: 1,
      tooltip: {
        valueFormatter(value: string) {
          return `${value}%`
        },
      },
      data: [2.0, 6.3, 10.2, 20.3, 16.5, 12.0, 6.2],
    },
  ],
})

const fetchData = () => {
  getRevenue().then((res) => {
    const data = res.data
    grossOption.value.title.subtext = `$${formatNumber(data.total.payment_amount)}`
    grossOption.value.graphic[0].style.text = `${data.yoy.total.payment_amount > 0 ? '↑' : '↓'}${Math.abs(data.yoy.total.payment_amount)}% YoY`
    grossOption.value.graphic[0].style.fill = data.yoy.total.payment_amount > 0 ? '#4CAF50' : '#FF5252'
    grossOption.value.series[0].data = Object.values(data.monthly).map((item) => {
      return Number(item.payment_amount)
    })

    const maxPayment = Math.max(...Object.values(data.monthly).map(item => item.payment_amount))
    const maxYoy = Math.max(...Object.values(data.yoy.monthly).map(item => item.payment_amount))
    const maxQuarterly = Math.max(...Object.values(data.quarterly).map(item => item.payment_amount))
    const maxQuarterlyYoy = Math.max(...Object.values(data.yoy.quarterly).map(item => item.payment_amount))
    const { max, interval } = calculateAxisRange(maxPayment)

    grossOption.value.yAxis = {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => formatYAxis(value),
      },
      max,
      interval,
    }

    amountOption.value.title.subtext = `$${formatNumber(data.total.net_amount)}`
    amountOption.value.graphic[0].style.text = `${data.yoy.total.net_amount > 0 ? '↑' : '↓'}${Math.abs(data.yoy.total.net_amount)}% YoY`
    amountOption.value.graphic[0].style.fill = data.yoy.total.net_amount > 0 ? '#4CAF50' : '#FF5252'
    amountOption.value.series[0].data = Object.values(data.monthly).map((item) => {
      return Number(item.net_amount)
    })

    amountOption.value.yAxis = {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => formatYAxis(value),
      },
      max,
      interval,
    }

    breakdownOption.value.series[0].data = Object.values(data.monthly).map((item) => {
      return Number(item.payment_amount)
    })
    breakdownOption.value.series[1].data = Object.values(data.yoy.monthly).map((item) => {
      return Number(item.payment_amount)
    })

    breakdownOption.value.xAxis[0].data = Object.keys(data.monthly).map((item) => {
      return item
    })

    breakdownOption.value.yAxis[0] = {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => formatYAxis(value),
      },
      min: 0,
      max: calculateAxisRange(maxPayment).max,
      interval: calculateAxisRange(maxPayment).interval,
    }

    breakdownOption.value.yAxis[1] = {
      type: 'value',
      min: 0,
      max: calculateAxisRange(maxYoy).max,
      interval: calculateAxisRange(maxYoy).interval,
      axisLabel: {
        formatter: '{value} %',
      },
    }

    lastQuarterOption.value.series[0].data = Object.values(data.quarterly).map((item) => {
      return Number(item.payment_amount)
    })

    lastQuarterOption.value.series[1].data = Object.values(data.yoy.quarterly).map((item) => {
      return Number(item.payment_amount)
    })

    lastQuarterOption.value.xAxis[0].data = Object.keys(data.quarterly).map((item) => {
      return item
    })

    lastQuarterOption.value.yAxis[0] = {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => formatYAxis(value),
      },
      min: 0,
      max: calculateAxisRange(maxQuarterly).max,
      interval: calculateAxisRange(maxQuarterly).interval,
    }

    lastQuarterOption.value.yAxis[1] = {
      type: 'value',
      min: 0,
      max: calculateAxisRange(maxQuarterlyYoy).max,
      interval: calculateAxisRange(maxQuarterlyYoy).interval,
      axisLabel: {
        formatter: '{value} %',
      },
    }

    if (grossChart.value) {
      grossChart.value.setOption(grossOption.value)
    }

    if (amountChart.value) {
      amountChart.value.setOption(amountOption.value)
    }

    if (breakdownChart.value) {
      breakdownChart.value.setOption(breakdownOption.value)
    }

    if (lastMonthChart.value) {
      lastMonthChart.value.setOption(lastQuarterOption.value)
    }
  })
}

function calculateAxisRange(maxValue: number) {
  const exponent = Math.floor(Math.log10(maxValue))
  const magnitude = 10 ** exponent
  const max = Math.ceil(maxValue / magnitude) * magnitude
  const interval = max / 5

  return { max, interval }
}

onMounted(() => {
  const grossChartDom = document.getElementById('gross')
  const amountChartDom = document.getElementById('netAmount')
  const breakdownChartDom = document.getElementById('breakdown')
  const lastMonthChartDom = document.getElementById('last-month')
  if (grossChartDom) {
    grossChart.value = echarts.init(grossChartDom)
    grossOption.value && grossChart.value.setOption(grossOption.value)
  }
  if (amountChartDom) {
    amountChart.value = echarts.init(amountChartDom)
    amountOption.value && amountChart.value.setOption(amountOption.value)
  }
  if (breakdownChartDom) {
    breakdownChart.value = echarts.init(breakdownChartDom)
    breakdownOption.value && breakdownChart.value.setOption(breakdownOption.value)
  }
  if (lastMonthChartDom) {
    lastMonthChart.value = echarts.init(lastMonthChartDom)
    lastQuarterOption.value && lastMonthChart.value.setOption(lastQuarterOption.value)
  }

  Promise.all([
    fetchData(),
  ])
})

const viewDetail = () => {
  router.push({
    name: 'reportDetail',
  })
}
</script>

<template>
  <div>
    <div class="text-xl font-semibold">
      Revenue Overview
    </div>

    <div>
      <div class="mt-6">
        <div class="describe">
          Here's an overview of your key metrics over the last 12 months.
        </div>
        <div class="chart flex gap-8 mt-6">
          <div class=" border border-gray-200 rounded-lg  h-[280px] w-md shadow-sm transition-shadow hover:shadow-md">
            <div id="gross" @click="viewDetail" />
          </div>
          <div class=" border border-gray-200 rounded-lg  h-[280px] w-md shadow-sm transition-shadow hover:shadow-md">
            <div id="netAmount" />
          </div>
        </div>
      </div>
      <div class="breakdown mt-10">
        <div class="title font-semibold text-lg">
          Gross volume breakdown
        </div>
        <Divider />
        <div class="describe w-2xl">
          <p class="leading-8">
            <!-- January's gross volume was $2.12K, a decrease of 89.1% year-on-year. Gross volume for the month of February
            so far is $12.21K. Here's the trend over the previous 12 months. -->
          </p>
        </div>
        <div class="w-5xl h-[280px] mt-6">
          <div id="breakdown" />
        </div>
        <div class="mt-8">
          <div class="describe w-2xl">
            <p class="leading-8">
              <!-- Q4's gross volume was $4.95K, a decrease of 88.59$ over Q3, and 93.6% year-on-year, With 33 days left in
              Q1, gross volume is $14.32K for the quarter, an increase of 189.5% compared to Q4 and p decrease of 78.9%
              year-on-year. -->
            </p>
            <div class="w-5xl h-[280px] mt-6">
              <div id="last-month" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 组件样式 */
.describe {
  color: #666;
}

#gross {
  width: 100%;
  height: 100%;
}

#netAmount {
  width: 100%;
  height: 100%;
}

#breakdown {
  width: 100%;
  height: 100%;

}

#last-month {
  width: 100%;
  height: 100%;

}
</style>
