export interface Theme {
  key: keyof typeof THEME_COLOR_MAP | string
  backgroundColor: string
  color: string
}

export const THEME_COLORS = {
  LIGHT_ORANGE: 'light_orange',
  LIGHT_YELLOW: 'light_yellow',
  BLUE: 'blue',
  LIGHT_RED: 'light_red',
  PINK: 'pink',
  PURPLE: 'purple',
  LIGHT_PINK: 'light_pink',
  CYAN: 'cyan',
  DARK_GRAY: 'dark_gray',
  RED: 'red',
  CUSTOM: 'custom',
} as const

export const THEME_COLOR_MAP = {
  [THEME_COLORS.LIGHT_ORANGE]: {
    name: 'Light Orange',
    backgroundColor: '#FE4C1C',
    color: '#ffffff',
  },
  [THEME_COLORS.LIGHT_YELLOW]: {
    name: 'Light Yellow',
    backgroundColor: '#fff453',
    color: '#fc7690',
  },
  [THEME_COLORS.BLUE]: {
    name: 'Blue',
    backgroundColor: '#6200f5',
    color: '#FFFFFF',
  },
  [THEME_COLORS.LIGHT_RED]: {
    name: 'Light Red',
    backgroundColor: '#E66608',
    color: '#FFFFFF',
  },
  [THEME_COLORS.PINK]: {
    name: 'Pink',
    backgroundColor: '#C34469',
    color: '#FFFFFF',
  },
  [THEME_COLORS.PURPLE]: {
    name: 'Purple',
    backgroundColor: '#574B90',
    color: '#FFFFFF',
  },
  [THEME_COLORS.LIGHT_PINK]: {
    name: 'Light Pink',
    backgroundColor: '#F78FA3',
    color: '#8B008B',
  },
  [THEME_COLORS.CYAN]: {
    name: 'Cyan',
    backgroundColor: '#3EC1D3',
    color: '#FFFFFF',
  },
  [THEME_COLORS.DARK_GRAY]: {
    name: 'Dark Gray',
    backgroundColor: '#303A52',
    color: '#FFFFFF',
  },
  [THEME_COLORS.RED]: {
    name: 'Red',
    backgroundColor: '#FF0000',
    color: '#FFFFFF',
  },
} as const

export const pickColor = (color: keyof typeof THEME_COLOR_MAP | string): { backgroundColor: string, color: string } => {
  // 如果是预定义的主题颜色，直接返回
  if (THEME_COLOR_MAP[color as keyof typeof THEME_COLOR_MAP]) {
    return THEME_COLOR_MAP[color as keyof typeof THEME_COLOR_MAP]
  }

  // 处理旧数据兼容性
  let backgroundColor = color

  // 如果是6位十六进制颜色但没有#前缀，添加#
  if (/^[0-9A-F]{6}$/i.test(color)) {
    backgroundColor = `#${color}`
  }
  // 如果已经是#开头的十六进制颜色，直接使用
  else if (/^#[0-9A-F]{6}$/i.test(color)) {
    backgroundColor = color
  }

  return { backgroundColor, color: '#ffffff' }
}
