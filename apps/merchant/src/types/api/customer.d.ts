declare namespace Api {

  interface CustomerListReq {
    'page'?: number
    'page_size'?: number
    'query'?: string
    'filterStatus'?: string
    'sortField'?: string
    'sortOrder'?: 'asc' | 'desc'
    'created_at[]'?: string[]
    'keyword'?: string
    'category'?: number | string | null
    'status'?: number | string | null
    'sort_by'?: string
    'sort_order'?: string
  }

  interface CustomerCreateReqPlan {
    /**
     * 计划开始生效时间
     */
    start_date?: string
    /**
     * 数量，数量
     */
    units?: string | number
  }

  interface CustomerCreateSimpleReq {
    /**
     * email
     */
    email: string
    /**
     * 名称
     */
    name: string
    /**
     * 手机号
     */
    phone_number?: string
  }

  //
  interface CustomerCreateReq {
    customer_name: string
    email_primary: string
    phone_mobile?: string
    plan: Api.PlanCreateReq & {
      theme?: string
      logo?: string | FileUpload.UploadFileItem[]
      units?: number
    } | null
  }

  interface CustomerSendEmailInviteReq {
    customer_id: string
    logo?: string
    theme?: string
  }

  interface CustomerInviteBank {
    /**
     * 账户名称
     */
    account_name: string
    /**
     * 账户号码
     */
    account_no: string
    /**
     * BSB号码
     */
    bsb: string
    /**
     * 城市
     */
    city: string
    /**
     * 公司名称
     */
    company_name?: string
    /**
     * 邮箱
     */
    email: string
    /**
     * 名
     */
    first_name: string
    /**
     * 姓
     */
    last_name: string
    /**
     * 地址1
     */
    line_1: string
    /**
     * 地址2
     */
    line_2?: string
    /**
     * 邮编
     */
    postcode: string
    /**
     * 州
     */
    state: string
  }

  interface CustomerInviteCard {
    /**
     * 卡号
     */
    card_number: string
    /**
     * 有效期月，格式MM
     */
    expiration_month: string
    /**
     * 有效期年，格式YY
     */
    expiration_year: string
    /**
     * 持卡人姓名
     */
    name_on_card: string
    /**
     * CVV
     */
    security_code: string
  }

  interface BillingAddress {
    /**
     * 国家
     */
    country: string
    /**
     * 名
     */
    first_name: string
    /**
     * 姓
     */
    last_name: string
    /**
     * 公司名称
     */
    company?: string
    /**
     * 地址1
     */
    line_1: string
    /**
     * 地址2
     */
    line_2?: string
    /**
     * 城市
     */
    city: string
    /**
     * 州/省
     */
    state: string
    /**
     * 邮编
     */
    postal_code: string
    /**
     * 电话
     */
    phone: string
  }

  interface CreateCustomerInviteReq {
    /**
     * 银行账号信息，type为Bank Account时必需
     */
    bank?: CustomerInviteBank
    /**
     * 卡信息，type为Credit时必需
     */
    card?: Card
    /**
     * 邮箱
     */
    customer_email: string
    /**
     * 加密customer_id
     */
    customer_id: string
    /**
     * 名称
     */
    customer_name: string
    /**
     * 手机号
     */
    phone_number: string
    /**
     * 跳转地址
     */
    return_url?: string
    /**
     * 账户类型，1：Bank Account / 2：Credit
     */
    type: number

    /**
     * 货币
     */
    currency: string

    /**
     * 账单地址
     */
    billing_address?: BillingAddress
  }

  interface CreateCustomerInviteChangePaymentMethodReq {
    /**
     * 客户编号
     */
    customer_id: string
    /**
     * payment_method_id
     */
    payment_method_id: number
    /**
     * 邮箱
     */
    customer_email: string
    /**
     * 加密customer_id
     */
    customer_id: string
    /**
     * 名称
     */
    customer_name: string
    /**
     * 手机号
     */
    phone_number: string
    /**
     * 货币
     */
    currency: string
  }

  interface UpdateCustomerPlanReq {
    /**
     * 客户编号，客户编号
     */
    customer_id: string
    /**
     * 是否启用，1是0否，不传默认就是启用1
     */
    enable?: string
    /**
     * plan编号，plan编号，支持多个数组，[1，2]
     */
    plan_ids: string[]
    /**
     * 计划开始生效时间
     */
    start_date: string
    /**
     * 数量
     */
    units: number
  }

  /**
   * 创建客户支付请求
   */
  interface CreateCustomerPaymentReq {
    /**
     * 客户编号
     */
    customer_id: string
    /**
     * 支付金额
     */
    amount: number
    /**
     * 支付描述
     */
    description?: string
    /**
     * 支付日期，格式YYYY-MM-DD
     */
    date: string
  }

  interface UpdateCustomerReq {
    customer_id: string
    name?: string
    customer_name?: string
    email_primary?: string
    phone_mobile?: string
    billing_details?: string
    language?: string
  }

  interface CreateCustomerInviteBank {
    account_no: string
    credit_brand: number
    credit_type: number
    id: number
    type: number

    /**
     * 是否禁用
     */
    disabled?: boolean
  }

  interface GetCustomerInviteRes {
    /**
     * 邮箱
     */
    customer_email: string
    /**
     * 名称
     */
    customer_name: string
    /**
     * logo地址
     */
    logo: string
    /**
     * 手机号
     */
    phone_number: string
    /**
     * 主题
     */
    theme: string

    /**
     * 商户名称
     */
    merchant_name: string

    /**
     * 计划
     */
    customer_plan?: Plan.Info | null
    /**
     * 是否已设置支付方式
     */
    is_payment_method: boolean

    /**
     * 银行账号信息
     */
    customer_bankings?: CreateCustomerInviteBank[]

    /**
     * 计划银行账号信息
     */
    customer_plan_bankings?: CreateCustomerInviteBank

    /**
     * 是否开启银行交易
     */
    open_banking_transactions?: boolean
  }

  interface RefundReq {
    ori_trans_no: string
  }

  interface CreatePlanByCustomerReq extends PlanCreateByCustomerReq {
    customer_id: string
    units?: number
    theme?: string
    logo?: string
  }

  interface CustomerHistoryListReq {
    customer_id: string
    page?: number
    page_size?: number
  }

  interface GetCustomerConfigRes {
    logo: string
    /**
     * 附加费率
     */
    fee_config: {
      business_id: string
      surcharge_rate: {
        /**
         * 类型，1-百分比 2-固定值
         */
        fee_rate: string
        /**
         * 费率
         */
        fee_value: string
      }
      gst: {
        /**
         * 类型，1-百分比 2-固定值
         */
        fee_rate: string
        /**
         * 费率
         */
        fee_value: string
      }
    }[]

    /**
     * 主体
     */
    theme: string
  }

  interface CustomerUpdateSubscriptionReq {
    /**
     * 自定义周期数量，process_type为custom时必填，every x，每x days/weeks等
     */
    custom_cycle?: number
    /**
     * 自定义周期类型，process_type为custom时必填，说明如下：
     * 1：days 2：weeks 3：months 4：years
     */
    custom_cycle_type?: number
    /**
     * 客户编号
     */
    customer_id: string
    /**
     * 描述
     */
    description?: string
    /**
     * 有效期截止日期，当 end_date_type=1 必填，yyyyMMdd
     */
    end_date?: string
    /**
     * 模板启用结束时间类型，1：by date 具体日期 2：by term 次数 3：good_till_cancel一直有效
     */
    end_date_type: number
    /**
     * 模板启用执行次数，当 end_date_type=2  必填
     */
    end_terms?: number
    /**
     * 是否包含GST，true / false or  1 / 0
     */
    is_inclusive_gst: number
    /**
     * 是否收取附加费，true / false or  1 / 0
     */
    is_surcharge: number
    /**
     * 计划编号
     */
    plan_id: string
    /**
     * 计划名称
     */
    plan_name: string
    /**
     * 价格表，一条或多条价格数据，pricing_model=3（Tier）时必填
     */
    prices: CustomerSubscriptionPrice[]
    /**
     * 费用类型，1：Flat Rate 固定金额(One-off固定是这个)；2：Standard Pricing 标准金额；3：Tiered Pricing
     */
    pricing_model: number
    /**
     *
     * 执行频率类型，schedule_type为Recurring时必填，1：one-off，2：daily，3：weekly，4：fortnightly，5：monthly，6：Every
     * 3 months， 7：Every 6 months，8：yearly，9：minutely，10：hourly，11：datetime，12：custom
     */
    process_type?: number
    /**
     * 任务类型，1：Recurring 持续的，2：One-off 一次的
     */
    schedule_type: number
    /**
     * 阶梯计费类型，pricing_model 为 Tiered Pricing 时必填，说明如下：
     * 1：Volume 批量定价，只收对应阶梯的费用 2：Graduated 阶梯式定价，每个阶梯分别都收费
     */
    tiered_type?: number
    /**
     * 单位数量，pricing_model=3 Tiered Pricing时必填
     */
    units: number
  }

  interface CustomerSubscriptionPrice {
    /**
     * 额外费用
     */
    amount_flat_fee?: number
    /**
     * 金额类型，每一个单位的单价
     */
    amount_per_unit: number
    /**
     * 币种，三位ISO码，如：AUD
     */
    currency: string
    /**
     * 起始单位数
     */
    first_unit: number
    /**
     * 结束单位数，无限使用这个：∞
     */
    last_unit: number | string
  }

  interface UpdateCommunicationConfigReq {
    /**
     * 客户编号
     */
    customer_id: string
    /**
     * 通知类型
     */
    notification_type: number
    /**
     * 状态（0：关闭 1：开启）
     */
    status: string
  }

  interface GetAllCustomerReq {
    query?: string
  }

  interface UpdateCommunicationThemeReq {
    /**
     * logo图片
     */
    logo?: string
    /**
     * 主题颜色 json 字符串
     */
    theme: string
  }
}
