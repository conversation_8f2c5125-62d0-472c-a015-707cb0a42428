import type { ConfirmationServiceMethods } from 'primevue/confirmationservice'
import type { DialogServiceMethods } from 'primevue/dialogservice'
import type { ToastServiceMethods } from 'primevue/toastservice'

declare global {
  interface Window {
    $toast: ToastServiceMethods
    $dialog: DialogServiceMethods
    $confirm: ConfirmationServiceMethods
    grecaptcha: {
      render: (
        container: HTMLElement,
        parameters: {
          'sitekey': string
          'theme'?: string
          'size'?: string
          'tabindex'?: number
          'callback'?: (response: string) => void
          'expired-callback'?: () => void
          'error-callback'?: () => void
        }
      ) => number
      reset: (widgetId: number) => void
      getResponse: (widgetId: number) => string
    }
  }

  interface CommonListRes<T> {
    data: T
    total?: number
    [key: string]: any
  }

  interface CommonRes<T = any> {
    code: number
    data: T
    message: string
  }

  interface CommonSearchListParams {
    /**
     * 页码
     */
    page?: number
    /**
     * 每页条数
     */
    page_size?: number
    /**
     * 排序字段
     */
    sort_by?: string
    /**
     * 排序方向
     */
    sort_order?: string
  }

  interface TableColumnItem {
    field: string
    header: string
    sortable?: boolean
    template?: string
    format?: (value: any) => string
    style?: string | CSSStyleDeclaration | Record<string, string>
    sortField?: string
    alignFrozen?: 'left' | 'right' | undefined
    frozen?: boolean
    headerStyle?: any
    headerClass?: string
  }

  interface TableSearchField {
    [key: string]: TableSearchFieldItem
  }

  interface TableSearchFieldItem {
    label: string
    component: 'Input' | 'InputNumber' | 'InputRange' | 'InputNumberRange' | 'Select' | 'Checkbox' | 'DatePicker'
    componentProps?: Record<string, any>
    value: string | number | number [] | string []
    isSuperSearch?: boolean
    isHide?: boolean
    formatDate?: string
  }
}
