export interface Notification {
  id: string
  title: string
  content: string
  type: 'info' | 'payment' | 'error' | 'system'
  time: Date
  read: boolean
}

export interface NotificationGroup {
  today: Notification[]
  yesterday: Notification[]
  lastWeek: Notification[]
}

export type NotificationType = 'info' | 'payment' | 'error' | 'system'

export interface NotificationColor {
  [key: string]: string
}
export interface FlexiratesNotifyListReq {
  page: number
  page_size: number
  category?: number | undefined | string
  /**
   * YYYY-MM-DD HH:MM:SS
   */
  end_date?: string
  event?: number
  keyword?: string
  /**
   * YYYY-MM-DD HH:MM:SS
   */
  start_date?: string
  [property: string]: any
}

export interface EditNotify {
  /**
   * 是否标记全部，可选：0，1
   */
  is_all: number
  id?: number
  /**
   * is_all=0时必填，多个id用英文逗号隔开
   */
  ids?: string
  /**
   * 是否已读，可选：0，1
   */
  is_read?: number
  [property: string]: any
}
export interface PreferencesUpdate {
  /**
   * ID 编号，来自配置的 events.id
   */
  id: string
  /**
   * 可选值: 0,1，不修改的传原值
   */
  is_enable?: string
  /**
   * 可选值: 0,1，不修改的传原值
   */
  is_send_email?: string
  /**
   * 可选值: 0,1，不修改的传原值
   */
  is_send_in_app?: string
  /**
   * 可选值: 0,1，不修改的传原值
   */
  is_send_sms?: string
  [property: string]: any
}
