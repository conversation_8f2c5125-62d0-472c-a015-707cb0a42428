import type { AxiosRequestConfig } from 'axios'
import { GET, POST } from '../http'

export const xeroSubmitPayment = (data: Api.XeroSubmitPaymentReq, options?: AxiosRequestConfig) => POST<{ url: string }>('xero/submit', data, options)

export const xeroDisconnect = (options?: AxiosRequestConfig) => GET('xero/break', { ...options })

export const getXeroInfo = () => GET<Api.XeroInfoRes>('xero/info')

export const refreshXeroStatus = (_: any, options?: AxiosRequestConfig) => GET('xero/refreshAccessToken', { ...options })

export const getXeroChartOfAccounts = () => GET<Api.XeroChartOfAccountsRes[]>('xero/getAllAccountCode')

export const getXeroInvoiceTemplates = () => GET<Api.XeroInvoiceTemplatesRes[]>('xero/getThemeList')

export const createXeroInvoice = (data: Api.XeroCreateInvoiceReq, options?: AxiosRequestConfig) => POST<{ id: string }>('xero/addInvoice', data, options)

export const updateXeroInvoice = (data: Api.XeroEditInvoiceReq) => POST<{ id: string }>('xero/updateInvoice', data)

export const getXeroInvoiceConfig = (query: Api.GetXeroInvoiceConfigReq) => GET<Api.InvoiceConfigRes>('xero/getInvoicePaymentDetail', { params: query })

export const pay = (data: Api.XeroPayReq) => POST<CommonRes<Api.XeroPayRes>>('xero/invoicePaymentSubmit', data)

export const syncXeroData = (_: any, options?: AxiosRequestConfig) => POST<CommonRes<Api.XeroPayRes>>('xero/syncChannelData', { ...options })

export const getXeroAccountSettings = () => GET<Api.XeroSettingsRes>('xero/getAccountConfig')

export const updateXeroSettings = (data: { update_list: Api.XeroSettingsRes['branding_themes'] }) => POST<CommonRes<Api.XeroSettingsRes>>('xero/updateBrandingThemeConfig', data)

export const updateXeroBankAccount = (data: { bank_account_id: number, logo: string, file_name: string }) => POST<CommonRes<Api.XeroSettingsRes>>('xero/updateInvoiceChannelConfig', data)
