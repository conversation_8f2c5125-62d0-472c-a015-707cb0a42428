import type { EditNotify, FlexiratesNotifyListReq, PreferencesUpdate } from '@/types/notification'
import { GET, POST } from '@/services/http'

export const getTemplate = (notification_type?: number) => GET('/communicationConfig/list', { params: { notification_type } })

export const updateNotification = (data: Api.NotificationReq) => POST<CommonRes<Api.NotificationListRes>>('/communicationConfig/update', data)
export const getUnreadCount = () => GET('/notify/unreadCount')
export const getNotifyList = (params: FlexiratesNotifyListReq) => GET('/notify/list', { params })
export const deleteNotify = (data: EditNotify) => POST('/notify/delete', data)
export const markNotify = (data: EditNotify) => POST('/notify/markReadStatus', data)
export const getPreferencesList = (params?: any) => GET('/notify/preferences', { params })
export const updatePreferences = (data?: PreferencesUpdate) => POST('/notify/preferencesUpdate', data)
