{"planSubscription": {"columns": {"name": "Name", "pricing": "Pricing", "created": "Created", "updated": "Updated", "status": "Status"}, "tieredPricing": {"firstUnit": "First unit", "lastUnit": "Last unit", "perUnit": "Per unit", "flatFee": "Flat fee", "addAnotherTier": "Add another tier", "lessPricingOptions": "Less pricing options", "morePricingOptions": "More pricing options", "tier": "Tier"}, "filters": {"all": "All Plans", "active": "Active", "archived": "Archived", "inactive": "Inactive"}, "actions": {"addPlan": "Add Product", "editPlan": "Edit Product", "deletePlan": "Delete Product"}, "dialogs": {"addPlan": "Add Product", "editPlan": "Edit Product", "planDetails": "Product Details", "confirmDelete": "Confirm Delete", "deletePlanMessage": "Are you sure you want to delete ?"}, "sections": {"basicInfo": "Basic Information", "pricingInfo": "Pricing Information", "timeInfo": "Time Information", "systemInfo": "System Information"}, "fields": {"planId": "Plan ID", "amount": "Amount", "processType": "Process Type", "feeType": "Fee Type", "startDate": "Start Date", "endDate": "End Date"}}, "message": {"hello": "Hello", "welcome": "Welcome", "pageNotFound": "Sorry, the page you visited does not exist", "pageNotFoundDescription": "The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.", "redirectingToHome": "Redirecting to home page..."}, "button": {"submit": "Submit", "cancel": "Cancel", "backToHome": "Back to Home", "backToList": "Back to List", "action": "Action", "goBack": "Go Back"}, "common": {"error": "Error", "success": "Success", "forbidden": "Forbidden", "unauthorized": "Unauthorized", "internalServerError": "Internal Server Error", "networkError": "Network error", "anErrorOccurred": "An error occurred", "warning": "Warning", "info": "Information", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "add": "Add", "search": "Search", "loading": "Loading...", "noData": "No data available", "noDataFound": "No data found", "noDataFoundDescription": "Try adjusting your search or filter to find what you're looking for.", "requestFailed": "Request Failed", "requestFailedDescription": "An error occurred while fetching data. Please try again later.", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "submit": "Submit", "reset": "Reset", "download": "Download", "downloads": "Downloads", "created": "Created", "copy": "Copy", "copied": "Copied to clipboard", "copyFailed": "Co<PERSON> failed", "refresh": "Refresh", "language": "Language", "saveSuccess": "Saved successfully", "themes": "Themes", "custom": "Custom", "themeType": "Theme Type", "themeColor": "Theme Color", "filters": {"search": "Search", "filterBy": "Filter by"}}, "validation": {"required": "This field is required", "minLength": "Minimum {min} characters required", "maxLength": "Maximum {max} characters allowed", "emailRequired": "Email is required", "emailInvalid": "Invalid email format", "usernameRequired": "Username is required", "usernameInvalid": "Invalid username format", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 8 characters", "passwordUppercase": "Password must contain an uppercase letter", "passwordLowercase": "Password must contain a lowercase letter", "passwordNumber": "Password must contain a number", "confirmPasswordRequired": "Please confirm your password", "passwordsNotMatch": "Passwords do not match", "invalidMerchantIdRequired": "Merchant ID is required", "alphanumericDashOnly": "Can only contain lowercase letters, numbers, and hyphens", "agreeTerms": "Please agree to the terms of service", "tiered_pricing_overlap": "Tiered pricing ranges cannot overlap", "per_unit_invalid": "Per unit price must be a positive number", "flat_fee_invalid": "Flat fee must be a non-negative number", "first_unit_invalid": "First unit must be a positive integer", "last_unit_invalid": "Last unit must be greater than first unit", "tiered_pricing_gap": "There should be no gaps between tiers", "first_tier_must_start_with_one": "First tier must start with unit 1", "googleTokenRequired": "Please check Google"}, "login": {"title": "<PERSON><PERSON>", "merchant_id": "Merchant ID", "username": "Username", "password": "Password", "rememberMe": "Remember me", "signIn": "Sign in", "invalidUsernameRequired": "Username is required", "invalidCredentials": "Invalid username or password", "subtitle": "Please enter your username and password to login", "email": "Email", "emailPlaceholder": "Please enter your email", "forgotPassword": "Forgot Password ?", "noAccount": "No account ?", "signUp": "Sign up"}, "register": {"title": "Register", "subtitle": "Create a new account", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "agreeTerms": "I have read and agree to the Terms of Service", "haveAccount": "Already have an account ?"}, "menu": {"home": "Dashboard", "orderManagement": "Order Management", "orderList": "Order List", "orderType": "Order Type", "physicalGoods": "Physical Goods", "virtualGoods": "Virtual Goods", "services": "Services", "orderSettings": "Order Settings", "transactions": "Transactions", "transactionsDetail": "Transactions Detail", "customers": "Customers", "customersAdd": "Add Customer", "customersList": "Customers List", "customersDetail": "Customers Detail", "customersCreateInvoice": "Create Invoice", "accounting": "Accounting", "user": "User", "profile": "Profile", "settings": "Settings", "addCustomer": "Add Customer", "planSubscription": "Plan & Subscription", "planSubscriptionAdd": "Add", "planSubscriptionEdit": "Edit", "planSubscriptionList": "List", "planSubscriptionDetails": "Details", "balance": "Balance", "council": "Council", "report": "Report", "users": "Users", "userList": "User List", "userDetail": "User Detail", "userCreate": "Create a user", "userEdit": "Edit User", "userRole": "Role Management", "userRoleCreate": "Create Role", "userRoleEdit": "Edit Role", "userRoleDetails": "Role Details", "support": "Support", "supportCancellation": "Cancel Registration Requests", "downloadCenter": "Download Center", "payout": "Payout", "payoutList": "List", "notification": "Notification", "NotificationList": "List", "notificationSetting": "Setting", "ratePayers": "Ratepayers", "ratePayersCreate": "Create", "ratePayersList": "List", "properties": "Properties", "payments": "Payments", "reports": "Reports", "integrations": "Integrations", "schedules": "Schedules", "paymentMethods": "Payment Methods", "myProfile": "My Profile", "security": "Security", "contact": "Contact", "account": "Account", "invoiceList": "Invoice", "payMyInvoice": "PayMyInvoice", "payMyInvoiceDashboard": "Dashboard", "payMyInvoiceIntegrations": "Integrations", "payMyInvoiceCreateInvoice": "Create Invoice", "payMyInvoiceList": "Invoice List", "payMyInvoiceXeroConfig": "Xero Config", "propertyList": "Property List", "arrear": "Arrear", "arrearList": "List", "settlementList": "Settlement List", "settlementDetail": "Settlement Detail", "settlementDetailInfo": "Settlement Details", "settlementDetailInfoDetail": "Details", "flexiratesNotification": "Notifications", "flexiratesProfile": "Profile", "flexiratesMerchantRatepayers": "Ratepayers", "flexiratesMerchantRatepayersList": "List", "flexiratesMerchantRatepayersCreate": "New Ratepayer", "flexiratesMerchantRatepayersDetail": "Details", "flexiratesMerchantRatepayersEdit": "Edit", "flexiratesMerchantPropertiesDetail": "Details", "flexiratesMerchantPropertiesEdit": "Edit", "flexiratesMerchantUserManagement": "User Management", "flexiratesMerchantPayments": "Payments", "flexiratesMerchantPaymentsList": "List", "flexiratesMerchantPaymentsDetail": "Details", "flexiratesMerchantUserProfile": "Profile", "flexiratesMerchantUserSettings": "Settings", "flexiratesMerchantProperties": "Properties", "flexiratesMerchantPropertiesCreate": "New Property", "flexiratesMerchantPropertiesList": "List", "flexiratesMerchantSchedules": "Schedules", "flexiratesMerchantSchedulesList": "List"}, "user": {"twoFactorAuth": {"title": "Two-Factor Authentication", "subtitle": "Enhance your account security with two-factor authentication", "setupStep": "Setup Authentication App", "verificationStep": "Verification Code", "downloadApp": "Download an authentication app like Google Authenticator or Authy on your mobile device", "scanQRCode": "Scan the QR code below with your authentication app", "getVerificationCode": "Get the 6-digit verification code from the app", "manualSetup": "Can't scan the QR code?", "enterSecretKey": "Manually enter this secret key in your authenticator app:", "verificationCode": "Authentication Code", "codePlaceholder": "Enter the 6-digit code", "enterCodeFromApp": "Enter the 6-digit code from your authentication app to verify and enable two-factor authentication", "verifyAndEnable": "Verify and Enable", "success": "Success", "error": "Error", "enabled": "Two-factor authentication has been enabled successfully", "disabled": "Two-factor authentication has been disabled", "setupComplete": "Two-factor authentication setup complete", "alreadyEnabled": "Already Enabled", "alreadyEnabledDetails": "Two-factor authentication is already enabled for your account", "invalidCode": "Invalid verification code. Please try again.", "invalidCodeFormat": "Please enter a valid 6-digit code", "errorCheckingStatus": "Error checking 2FA status", "errorGettingQRCode": "Error generating QR code", "errorVerifyingCode": "Error verifying code", "errorEnabling": "Error enabling two-factor authentication", "errorDisabling": "Error disabling two-factor authentication", "secretKeyCopied": "Secret key copied to clipboard"}, "profile": "Profile", "logout": "Logout", "profileSettings": {"title": "Profile", "subtitle": "Manage your personal information", "fullName": "Full Name", "nickname": "Nickname", "gender": "Gender", "preferNotToSay": "Prefer not to say", "email": "Email", "editAvatar": "Edit Avatar", "primaryEmail": "Primary Email", "verifiedEmail": "Verified", "unverifiedEmail": "Unverified", "makeEmailPrimary": "Make Primary", "removeEmail": "Remove", "verifyEmail": "Verify", "addNewEmail": "Add New Email", "saveChanges": "Save Changes", "cancel": "Cancel", "emailPlaceholder": "Enter your email address", "profileUpdated": "Profile updated successfully", "namePlaceholder": "Your name", "nicknamePlaceholder": "Your nickname", "nameRequired": "Name is required", "emailInvalid": "Please enter a valid email address", "updateSuccess": "Profile updated successfully", "updateError": "Failed to update profile", "loadError": "Failed to load user profile", "maxLength": "Maximum 50 characters", "enable2FA": "Enable Two-Factor Authentication", "2FADescription": "Enable two-factor authentication to add an extra layer of security to your account", "avatarUploadHint": "Select an image file (max 1MB)", "uploadAvatar": "Upload", "avatarUpdateSuccess": "Avatar updated successfully", "avatarUpdateError": "Failed to update avatar"}, "settings": {"title": "Settings", "subtitle": "Manage your account settings and preferences", "general": "General", "security": "Security", "language": "Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "notifications": "Notifications", "emailNotifications": "Email Notifications", "emailNotificationsDescription": "Receive email notifications for important updates and activities", "pushNotifications": "Push Notifications", "pushNotificationsDescription": "Receive push notifications on your desktop or mobile device", "smsNotifications": "SMS Notifications", "smsNotificationsDescription": "Receive text message notifications for critical alerts", "sessionTimeoutHelp": "Set how long you can be inactive before being automatically logged out", "twoFactorAuth": "Two-Factor Authentication", "twoFactorAuthDescription": "Add an extra layer of security to your account with two-factor authentication", "twoFactorEnabled": "Two-Factor Authentication is enabled", "twoFactorDisabled": "Two-Factor Authentication is disabled", "enable2FA": "Enable 2FA", "disable2FA": "Disable 2FA", "backupCodes": "Backup Codes", "viewBackupCodes": "View Backup Codes", "languageOptions": {"en": "English", "zh": "中文"}, "timeoutOptions": {"15": "15 Minutes", "30": "30 Minutes", "60": "1 Hour", "120": "2 Hours", "never": "Never"}, "sessionTimeout": {"title": "Session Timeout", "options": {"minutes": "{count} Minutes", "hours": "{count} Hours", "never": "Never"}}, "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "updatePassword": "Update Password", "currentPasswordRequired": "Current password is required", "newPasswordRequired": "New password is required", "confirmPasswordRequired": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "passwordRequirements": "Password Requirements", "passwordMinLength": "At least 8 characters", "passwordUppercase": "At least one uppercase letter", "passwordLowercase": "At least one lowercase letter", "passwordNumber": "At least one number", "passwordSpecial": "At least one special character", "saveChanges": "Save Changes", "settingsSaved": "Setting<PERSON> saved successfully", "passwordUpdated": "Password updated successfully", "errorSavingSettings": "Failed to save settings", "errorUpdatingPassword": "Failed to update password"}}, "settings": {"title": "Settings", "subtitle": "Manage your account settings", "general": "General", "security": "Security", "language": "Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "notifications": "Notifications", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "smsNotifications": "SMS Notifications", "saveChanges": "Save Changes", "enableTwoFactor": "Enable Two-Factor Authentication", "twoFactorDescription": "Add an extra layer of security to your account by requiring a verification code in addition to your password.", "sessionTimeout": "Session Timeout", "sessionTimeoutDescription": "Set how long you can be inactive before being automatically logged out.", "changePassword": "Change Password", "currentPassword": "Current Password", "currentPasswordRequired": "Current password is required", "newPassword": "New Password", "newPasswordRequired": "New password is required", "confirmPassword": "Confirm Password", "confirmPasswordRequired": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "updatePassword": "Update Password", "passwordRequirements": "Password Requirements", "passwordMinLength": "At least 8 characters", "passwordUppercase": "At least one uppercase letter", "passwordLowercase": "At least one lowercase letter", "passwordNumber": "At least one number", "passwordSpecial": "At least one special character"}, "languages": {"english": "English", "chinese": "Chinese"}, "forgotPassword": {"title": "Forgot Password", "subtitle": "Enter your email and we'll send you a link to reset your password.", "email": "Email", "backToLogin": "Back to Login"}, "customers": "Customers", "addCustomer": "Add Customer", "all": "All", "topCustomers": "Top Customers", "firstTimeCustomers": "First-time Customers", "repeatCustomers": "Repeat Customers", "recentCustomers": "Recent Customers", "highRefunds": "High Refunds", "highDisputes": "High Disputes", "copy": "Copy", "export": "Export", "actions": "Actions", "analyze": "Analyze", "editColumns": "<PERSON>", "name": "Name", "email": "Email", "defaultPaymentMethod": "Default Payment Method", "created": "Created", "totalSpend": "Total Spend", "payments": "Payments", "refunds": "Refunds", "disputeLosses": "Dispute Losses", "lastPayment": "Last Payment", "enterName": "Enter name", "enterEmail": "Enter email", "paymentMethod": "Payment Method", "selectPaymentMethod": "Select payment method", "cardNumber": "Card Number", "enterCardNumber": "Enter card number", "cancel": "Cancel", "save": "Save", "availableColumns": "Available Columns", "viewDetails": "View Details", "edit": "Edit", "delete": "Delete", "invalidEmail": "Please enter a valid email address", "customerAnalysis": "Customer Analysis", "totalCustomers": "Total Customers", "averageSpend": "Average Spend", "totalPayments": "Total Payments", "totalRefunds": "Total Refunds", "totalDisputes": "Total Disputes", "close": "Close", "checkout": {"setupTitle": "Set up a Direct Debit Request with", "merchantName": "One Tao Kung Fu Academy", "description": "Your Direct Debit Request will be set up now, but we'll confirm the amount and let you know before future payments are taken.", "currency": {"label": "Pay with", "select": "Select currency", "AUD": "Australian Dollar (AUD)", "USD": "US Dollar (USD)", "CNY": "Chinese Yuan (CNY)"}, "title": "Payment Information", "basicInformation": "Basic Information", "paymentMethods": {"card": "Credit or debit card", "bankAccount": "Bank Account"}, "form": {"name": {"label": "Name", "placeholder": "Enter your name"}, "email": {"label": "Email", "placeholder": "Enter your email"}, "phoneNumber": {"label": "Phone Number", "placeholder": "Enter your phone number"}}, "submit": "Pay {currency}{amount}", "security": "Payment information is encrypted", "success": {"title": "Payment Successful", "message": "Order completed", "redirect": "Redirecting to homepage in {seconds} seconds", "redirectButton": "Return to Home"}}, "merchant": {"columns": {"name": "Username", "email": "Email", "status": "Status", "type": "Type", "2fa": "2FA Status", "action": "Action"}, "search": {"namePlaceholder": "Search by username", "emailPlaceholder": "Search by email", "datePlaceholder": "Select Date Range"}, "buttons": {"create": "Create a user"}, "dialogs": {"confirmDelete": "Confirm Delete", "deleteConfirmMessage": "Are you sure you want to delete merchant \"{name}\"? This action cannot be undone."}, "messages": {"deleteSuccess": "User deleted successfully", "deleteFailed": "Failed to delete merchant", "createSuccess": "User created successfully", "createFailed": "Failed to create User"}, "form": {"name": "Username", "email": "Email", "password": "Password", "2fa": "2FA Authentication", "roles": "Roles", "selectRoles": "Select Roles", "select2FA": "Select 2FA Option"}, "role": {"columns": {"name": "Role Name", "slug": "Slug", "action": "Action", "permissions": "Permissions"}, "search": {"namePlaceholder": "Search by role name"}, "buttons": {"create": "Create Role"}, "dialogs": {"confirmDelete": "Confirm Delete", "deleteConfirmMessage": "Are you sure you want to delete role \"{name}\"? This action cannot be undone."}, "messages": {"deleteSuccess": "Role deleted successfully", "deleteFailed": "Failed to delete role", "createSuccess": "Role created successfully", "createFailed": "Failed to create role", "fetchRolesFailed": "Failed to fetch roles", "updateSuccess": "Role updated successfully", "updateFailed": "Failed to update role"}, "permissions": {"title": "Permissions", "selectAll": "Select All", "deselectAll": "Deselect All", "permissionName": "Permission Name"}, "details": {"title": "Role Details", "notFound": "Role not found", "basicInfo": "Basic Information"}, "placeholder": "Select permissions"}}, "invoicePaymentPage": {"invoiceNumber": "Invoice Number", "amountOfPayment": "Amount of Payment", "email": "Your Email", "name": "Your Name", "creditCardNumber": "Credit Card Number", "expireDate": "Expire Date", "securityCode": "CVV(Security code)", "whatIsThis": "what is this?", "termsAndConditions": "I have read and agree with the Terms and Conditions and Privacy Policy.", "submit": "Submit", "invoiceSummary": "Invoice Summary", "invoiceTotal": "Invoice total", "invoiceAmountPaid": "Invoice AmountPaid", "invoiceAmountDue": "Invoice AmountDue", "paymentAmount": "Payment Amount", "processingFee": "Processing Fee (2.5% + $0.30)", "total": "Total:", "fetchInvoice": "FETCH INVOICE", "required": "Required", "atLeastTwoDecimalPlaces": "At least two decimal places", "verification": "Human Verification:", "paymentSuccessTitle": "Payment Successful!", "paymentFailedTitle": "Payment Failed", "paymentSuccessMessage": "Your payment has been processed successfully. Thank you for your payment.", "paymentFailedMessage": "We couldn't process your payment. Please check your details and try again.", "amountPaid": "Amount Paid:", "paymentDate": "Payment Date:", "done": "Done", "tryAgain": "Try Again"}}