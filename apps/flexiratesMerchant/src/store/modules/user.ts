import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { user as user<PERSON><PERSON>, user<PERSON><PERSON> as user<PERSON><PERSON><PERSON><PERSON> } from '@/services/api'
import { handleKeepAlive, setRouter } from '@/utils/router'

export const useUserStore = defineStore('user', () => {
  const token = ref<string | null>(null)
  const refresh_token = ref<string | null>(null)
  const user = ref<User.Info | null>(null)
  const rememberMe = ref(false)
  const userMenu = ref<Menu.Item[]>([])
  const isNeed2FA = ref(true)
  const permissions = ref<string[]>([])
  // token 过期时间
  const expiresAt = ref<number | null>(null)

  const setToken = (newToken: string) => {
    token.value = newToken
  }

  // actions
  const login = async (email: string, password: string, google_token: string, remember: boolean) => {
    try {
      const { data, code } = await userApi.login({ email, password, rememberMe: remember, google_token })

      if (code === 1) {
        throw new Error('Invalid credentials')
      }

      const { access_token, refresh_token: r_token, expires_in = 3600 } = data
      expiresAt.value = Date.now() + expires_in * 1000
      // 模拟 10 秒后过期
      token.value = access_token
      refresh_token.value = r_token
      rememberMe.value = remember
      await getUserInfo()
      await getMenus()
      await getPermissions()
    }
    catch {
      throw new Error('Invalid credentials')
    }
  }

  const register = async (email: string, password: string) => {
    try {
      await userApi.register({ email, password })
    }
    catch {
      throw new Error('Registration failed')
    }
  }

  const forgotPassword = async (email: string) => {
    try {
      const { code } = await userApi.forgotPassword({ email })
      if (code !== 0) {
        throw new Error('Failed to send password reset email')
      }
    }
    catch {
      throw new Error('Failed to send password reset email')
    }
  }

  const logout = async () => {
    try {
      await userApi.logout()
    }
    finally {
      setToken('')
      user.value = null
      rememberMe.value = false
      location.reload()
    }
  }

  const initializeFromStorage = async () => {
    await getUserInfo()
    await getMenus()
    await getPermissions()
  }

  // 转换 menu 菜单
  const transformMenu = (menu: Api.RouterItem): Menu.Item => {
    let redirect: string | { name: string } = menu?.redirect || ''
    if (redirect && !String(redirect)?.startsWith('/')) {
      redirect = {
        name: redirect,
      }
    }
    return {
      path: menu.path,
      name: menu.name,
      redirect,
      children: menu.children?.map(transformMenu) || [],
      meta: {
        isSeparator: menu?.isSeparator === 1,
        breadcrumbTitle: menu?.breadcrumbTitle,
        isHideBreadcrumb: menu?.isHideBreadcrumb === 1,
        keepAlive: menu?.isKeepAlive === 1,
        i18nKey: menu.i18nKey,
        icon: menu.icon,
      },
    }
  }

  // 获取菜单
  const getMenus = async () => {
    const { data } = await userApi.getUserMenu()
    return new Promise<boolean>((resolve) => {
      const keeps: string[] = []
      // 递归过滤和转换菜单
      const filterAndTransformMenu = (items: Api.RouterItem[]): Menu.Item[] => {
        return items
          .filter((item) => {
            if (item?.isKeepAlive === 1) {
              keeps.push(item.name)
            }
            return !item.isHide
          })
          .map((item) => {
            const menuItem = transformMenu(item)
            if (item.children && item.children.length > 0) {
              menuItem.children = filterAndTransformMenu(item.children)
            }
            return menuItem
          })
      }
      // add redirect route
      // const redirectRoute = {
      //   id: 9999,
      //   parent_id: 0,
      //   i18nKey: 'menu.notification',
      //   label: null,
      //   component: '',
      //   icon: 'merchant/menu-icons/accounting.png',
      //   name: 'notification',
      //   path: '/notification',
      //   isHide: 0,
      //   redirect: 'notificationList',
      //   breadcrumbTitle: '',
      //   isKeepAlive: 0,
      //   isHideBreadcrumb: 0,
      //   isSeparator: 0,
      //   children: [
      //     {
      //       id: 999999,
      //       parent_id: 9999,
      //       i18nKey: 'menu.NotificationList',
      //       label: null,
      //       component: 'views/notifications/list.vue',
      //       icon: 'pi pi-list',
      //       name: 'NotificationList',
      //       path: 'list',
      //       isHide: 0,
      //       redirect: '',
      //       breadcrumbTitle: '',
      //       isKeepAlive: 0,
      //       isHideBreadcrumb: 1,
      //       isSeparator: 0,
      //     },
      //     {
      //       id: 14,
      //       parent_id: 12,
      //       i18nKey: 'menu.notificationSetting',
      //       label: null,
      //       component: 'views/notifications/setting.vue',
      //       icon: 'merchant/menu-icons/settings.png',
      //       name: 'notificationSetting',
      //       path: 'setting',
      //       isHide: 1,
      //       redirect: '',
      //       breadcrumbTitle: '',
      //       isKeepAlive: 0,
      //       isHideBreadcrumb: 0,
      //       isSeparator: 0,
      //     },
      //   ],
      // } as Api.RouterItem
      // 设置路由
      setRouter([...data])
      // 设置菜单
      userMenu.value = filterAndTransformMenu([...data])
      // 设置 keepAlive 名称
      handleKeepAlive(keeps)
      resolve(true)
    })
  }

  const getUserInfo = async (): Promise<User.Info> => {
    const data = await userApi.getUserInfo()
    isNeed2FA.value = data.code === 403
    user.value = data.data
    return user.value
  }

  const getPermissions = async () => {
    const { data, code } = await userRoleApi.getPermissionList()
    if (code === 0) {
      permissions.value = data.map(item => item.slug)
    }
  }

  const updateUserInfo = async (updateData: User.UserInfoUpdateReq) => {
    const { data, code } = await userApi.updateUserInfo(updateData)
    if (code === 0) {
      user.value = data
    }
    return user.value
  }

  // 权限检查方法
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }

  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(permission => permissions.value.includes(permission))
  }

  const hasAllPermissions = (permissionList: string[]): boolean => {
    return permissionList.every(permission => permissions.value.includes(permission))
  }

  // getters
  const isLoggedIn = computed(() => !!token.value)
  const currentUsername = computed(() => user.value?.name)
  const userPermissions = computed(() => permissions.value)

  return {
    // state
    token,
    user,
    rememberMe,
    userMenu,
    isNeed2FA,
    permissions,
    expiresAt,

    // actions
    login,
    register,
    forgotPassword,
    logout,
    initializeFromStorage,
    getMenus,
    getUserInfo,
    updateUserInfo,
    setToken,
    getPermissions,

    // permission methods
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,

    // getters
    isLoggedIn,
    currentUsername,
    userPermissions,
  }
}, {
  persist: {
    omit: ['userMenu', 'isLogin', 'user', 'isNeed2FA'],
  },
})
