import { publicRouterName } from './publicRouterName'

export const flexiratesMerchantRoutes = [
  {
    path: 'login',
    name: publicRouterName.LOGIN,
    component: () => import('@/views/login/loginView.vue'),
  },
  {
    path: 'register',
    name: publicRouterName.REGISTER,
    component: () => import('@/views/login/registerView.vue'),
  },
  {
    path: 'forgot-password',
    name: publicRouterName.FORGOT_PASSWORD,
    component: () => import('@/views/login/forgotPasswordView.vue'),
  },
  {
    path: 'new-password/:token',
    name: publicRouterName.NEW_PASSWORD,
    component: () => import('@/views/login/newPasswordView.vue'),
  },
  {
    path: 'two-factor-auth',
    name: publicRouterName.TWO_FACTOR_AUTH,
    component: () => import('@/views/user/two-factor-auth.vue'),
    meta: {
      title: 'Two-Factor Authentication',
      requiresAuth: true,
    },
  },
]
