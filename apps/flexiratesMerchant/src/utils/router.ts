import type { RouteRecordRaw } from 'vue-router'
import router from '@/router'
import { publicRouterName } from '@/router/publicRouterName'
import { useAppStore } from './../store/modules/app'

const modules = import.meta.glob('../views/**/*.vue')

function transformRoute(route: Api.RouterItem): RouteRecordRaw {
  if (modules[`../${route.component}`]) {
    return {
      path: route.path,
      name: route.name,
      component: () => modules[`../${route.component}`](),
      children: route.children?.map(transformRoute),
      meta: {
        breadcrumbTitle: route?.breadcrumbTitle,
        isHideBreadcrumb: !!route?.isHideBreadcrumb,
        keepAlive: !!route?.isKeepAlive,
        i18nKey: route.i18nKey,
        icon: route.icon,
      },
    }
  }
  else {
    let redirect: string | { name: string } = route?.redirect || ''
    if (redirect && !String(redirect)?.startsWith('/')) {
      redirect = {
        name: redirect,
      }
    }
    return {
      path: route.path,
      name: route.name,
      redirect,
      children: route.children?.map(transformRoute),
      meta: {
        breadcrumbTitle: route?.breadcrumbTitle,
        isHideBreadcrumb: !!route?.isHideBreadcrumb,
        keepAlive: !!route?.isKeepAlive,
        i18nKey: route.i18nKey,
        icon: route.icon,
      },
    }
  }
}

export function setRouter(routes: Api.RouterItem[]) {
  try {
    const routers = routes.filter(route => !route.isSeparator).map(transformRoute)
    const publicChildren = router.getRoutes().find(route => route.name === publicRouterName.ROOT)?.children
    // 移除 base 路由
    router.removeRoute(publicRouterName.ROOT)
    const baseRoute = {
      path: `/`,
      component: () => import(`@/layout/index.vue`),
      redirect: `/home`,
      name: 'index',
      children: [...routers],
      meta: {
        isHideBreadcrumb: true,
      },
    } as RouteRecordRaw
    publicChildren?.forEach((child) => {
      router.addRoute({
        ...child,
        path: `/${child.path}`,
      })
    })
    // 添加 base 路由
    router.addRoute(baseRoute)
  }
  catch (error) {
    console.log(error)
  }
}

export function handleKeepAlive(keeps: string[]) {
  const appStore = useAppStore()
  appStore.setKeepAlive(keeps)
}
