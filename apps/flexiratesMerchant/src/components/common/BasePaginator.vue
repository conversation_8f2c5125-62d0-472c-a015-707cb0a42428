<script setup lang="ts">
import type { PageState } from 'primevue/paginator'
import But<PERSON> from 'primevue/button'
import Divider from 'primevue/divider'
import Paginator from 'primevue/paginator'
import Select from 'primevue/select'

interface Props {
  /**
   * Number of current page
   */
  page: number
  /**
   * Number of rows per page
   */
  rows: number
  /**
   * Number of Total records
   */
  total: number
  /**
   * Number of total page
   */
  pageCount?: number
  /**
   * Available options for rows per page
   */
  rowsPerPageOptions?: number[]
  /**
   * Size
   */
  size?: 'small' | 'large' | undefined
}

const props = withDefaults(defineProps<Props>(), {
  page: 1,
  rows: 15,
  total: 15,
  pageCount: 0,
  rowsPerPageOptions() {
    return [15, 20, 50, 100]
  },
  size: undefined,
})
// Emit events that might be needed by parent components
const emits = defineEmits<{
  (e: 'pageChange', value: object): void
}>()

const onPageChange = (event: PageState) => {
  emits('pageChange', { ...event, page: event?.page + 1 })
}
</script>

<template>
  <Paginator
    :page="props?.page" :rows="props?.rows" :total-records="props?.total" :page-count="props.total"
    :page-links="Math.ceil(props?.total / props?.rows)" @page="onPageChange"
  >
    <template
      #container="{
        rows,
        page,
        pageCount = 1,
        pageLinks,
        prevPageCallback,
        nextPageCallback,
        rowChangeCallback,
        changePageCallback,
      }"
    >
      <div :class="props?.size" class="paginator-container">
        <div class="flex items-center gap-2">
          <span class="hidden sm:block">Show</span>
          <Select
            :class="props?.size" :model-value="rows"
            :options="props.rowsPerPageOptions?.map(item => ({ label: item, value: item }))" option-label="label"
            option-value="value" @change="e => rowChangeCallback(e.value)"
          />
        </div>
        <div class="paginator-button-container">
          <Divider align="center" layout="vertical" />
          <Button
            v-if="page !== 0" icon="pi pi-chevron-left" class="paginator-button" label="PREV" rounded text
            :size="props?.size" @click="prevPageCallback"
          />
          <Button
            v-for="i in pageLinks" :key="i" :label="String(i)" :disabled="i === page + 1"
            class="paginator-button-page" :size="props?.size" @click="changePageCallback(i - 1)"
          />
          <Button
            v-if="page !== pageCount - 1" label="NEXT" class="paginator-button" icon="pi pi-chevron-right"
            icon-pos="right" rounded text :size="props?.size" @click="nextPageCallback"
          />
        </div>
      </div>
    </template>
  </Paginator>
</template>

<style scoped lang="scss">
.paginator-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &.small {
    justify-content: flex-end;

  }

  :deep(.p-select) {
    border-radius: 12px;
    --p-select-background: transparent;

    &.small {
      height: 26px;
      font-size: 12px;

      .p-select-label {
        line-height: 10px;
      }
    }

    &.large {
      height: 38px;
      font-size: 16px;

      .p-select-label {
        line-height: 22px;
      }
    }
  }

  .paginator-button-container {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .paginator-button-page {
    border-radius: 12px;
    padding-inline: 12px;
    border: 1px solid var(--p-primary-500);
    background: transparent;
    color: var(--p-primary-500);
  }

  .paginator-button {
    border-radius: 12px;
    padding-inline: 18px;
    border: 1px solid var(--p-primary-500);
  }
}

:deep(.p-datatable-paginator-bottom) {
  --p-paginator-padding: 12px 0;
  border-bottom: none;

  .p-paginator {
    background-color: transparent;
  }
}
</style>
