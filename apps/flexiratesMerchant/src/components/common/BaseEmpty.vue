<script setup lang="ts">
interface Props {
  loading?: boolean
  failed?: boolean
  failureMessage?: string
  message?: string

}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  failed: false,
  failureMessage: 'Request Failed',
  message: '',
})
</script>

<template>
  <div class="empty-state-container">
    <BlockUI :blocked="loading" style="width: 100%;">
      <div class="empty-state-card" :class="{ 'error-state': props.failed }">
        <i v-if="loading" class="pi pi-spin pi-spinner loading-icon" />
        <div class="empty-state-icon-container" :class="{ 'error-icon': props.failed }">
          <i :class="props.failed ? 'pi pi-exclamation-triangle' : 'pi pi-inbox'" />
        </div>
        <h3 class="empty-state-title">
          <template v-if="props.failed">
            {{ $t('common.requestFailed') || 'Request Failed' }}
          </template>
          <template v-else-if="!loading">
            {{ $t('common.noDataFound') || 'No data found' }}
          </template>
          <template v-else-if="loading">
            Loading ...
          </template>
        </h3>
        <p class="empty-state-message">
          <template v-if="!loading && props.failed && props.failureMessage">
            {{ props.failureMessage }}
          </template>
          <template v-else-if="!loading && props.failed">
            {{ $t('common.requestFailedDescription') || 'An error occurred while fetching data. Please try again later.' }}
          </template>
          <template v-else-if="!loading">
            {{ message || $t('common.noDataFoundDescription') || 'Try adjusting your search or filter to find what you\'re looking for.' }}
          </template>
        </p>
        <slot name="empty-action" />
      </div>
    </BlockUI>
  </div>
</template>

<style scoped lang="scss">
.empty-state-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.empty-state-card {
    background: var(--surface-card, #ffffff);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    // max-width: 400px;
    width: 100%;
    position: relative;

    .loading-icon {
        display: block;
        position: absolute;
        top: calc(50% - 16px);
        left: calc(50% - 16px);
        font-size: 32px;
        color: #ffffff;
        z-index: 99999999;
    }
}

.error-state {
    border: 1px solid var(--red-200, #ef9a9a);
}

.empty-state-icon-container {
    background: var(--p-primary-500);
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.error-icon {
    background: var(--red-500, #f44336);
}

.empty-state-icon-container i {
    font-size: 2rem;
    color: white;
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-color, #495057);
}

.empty-state-message {
    color: var(--text-color-secondary, #6c757d);
    margin-bottom: 1.5rem;
    line-height: 1.5;
}
</style>
