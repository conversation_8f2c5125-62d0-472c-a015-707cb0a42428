<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import userDefaultImage from '@/assets/merchant/account-icon.png'
import noticeImage from '@/assets/merchant/notification-bell.png'
import { publicRouterName } from '@/router/publicRouterName'
import { getNotifyList } from '@/services/api/notifications'
// import { notifications } from '@/services/api'
import { useAppStore } from '@/store/modules/app'
import { useLayoutStore } from '@/store/modules/layout'
import { useNotificationStore } from '@/store/modules/notifications'
import { useUserStore } from '@/store/modules/user'
import Breadcrumbs from './appBreadcrumbs.vue'

const router = useRouter()
const { t } = useI18n()
const userSettingMenu = ref()
const userStore = useUserStore()
const appStore = useAppStore()

const { locale } = storeToRefs(appStore)

watch(locale, (newValue) => {
  appStore.setLocale(newValue as 'en' | 'zh')
})

const avatarUrl = computed(() => {
  return userStore.user?.avatar || userDefaultImage
})

const notificationStore = useNotificationStore()
const { unreadCount } = storeToRefs(notificationStore)
// const hasUnreadNotice = computed(() => unreadCount.value > 0)
const noticePopoverRef = ref()
const noticePopoverList = ref<any[]>([])
const notice_is_read = ref<number | string>('ALL')
const noticeLoading = ref<boolean>(true)

onMounted(() => {
  notificationStore.startPolling()
})
onUnmounted(() => {
  notificationStore.stopPolling()
})

const layoutStore = useLayoutStore()
const { isSidebarSlim } = storeToRefs(layoutStore)

const userSettingItems = computed(() => {
  return [
    {
      label: t('user.profile'),
      icon: 'pi pi-user',
      command: () => router.push('/user/profile'),
    },
    {
      label: t('user.settings.title'),
      icon: 'pi pi-cog',
      command: () => router.push('/user/settings'),
    },
    { separator: true },
    {
      label: t('user.logout'),
      icon: 'pi pi-power-off',
      command: async () => {
        await userStore.logout()
        router.replace({ name: publicRouterName.LOGIN })
      },
    },
  ]
})

const toggleSidebar = () => {
  layoutStore.setSidebarMode(isSidebarSlim.value ? 'expanded' : 'slim')
}

const toggleMobileMenu = () => {
  appStore.toggleMobileMenu()
}

const handleNotification = (is_read: number | string, event?: any) => {
  notice_is_read.value = is_read
  !!event && noticePopoverRef.value.toggle(event)
  noticeLoading.value = true
  getNotifyList({
    page: 1,
    page_size: 8,
    is_read: is_read === 'ALL' ? undefined : is_read as number,
  }).then((resp: { data: { data: any[] } }) => {
    noticePopoverList.value = resp?.data?.data
  }).finally(() => { noticeLoading.value = false })
}

const handleViewAllNotification = (event: any) => {
  router.push('/notification/list')
  noticePopoverRef.value.toggle(event)
}
</script>

<template>
  <div class="app-header">
    <div class="header-start">
      <Button class="mobile-menu-toggle" severity="secondary" @click="toggleMobileMenu">
        <i class="pi pi-bars" />
      </Button>
      <Button class="sidebar-toggle" severity="secondary" @click="toggleSidebar">
        <i class="pi" :class="isSidebarSlim ? 'pi-angle-right' : 'pi-angle-left'" />
      </Button>
      <Divider layout="vertical" />
      <Breadcrumbs />
    </div>
    <!-- notification -->
    <Popover ref="noticePopoverRef">
      <div class="notice-pop">
        <template v-if="noticeLoading">
          <BaseEmpty :loading="noticeLoading" message="No more Information" />
        </template>
        <template v-else-if="!noticeLoading">
          <div v-if="unreadCount" class="notice-pop-btn">
            <Button
              :severity="notice_is_read === 1 ? 'warn' : undefined"
              :variant="notice_is_read !== 1 ? 'outlined' : undefined" label="Unread"
              @click="() => { handleNotification(1) }"
            />
            <Button
              :severity="notice_is_read === 'ALL' ? 'warn' : undefined"
              :variant="notice_is_read !== 'ALL' ? 'outlined' : undefined" label="All"
              @click="() => { handleNotification('ALL') }"
            />
          </div>
          <div class="notice-content">
            <template v-if="!!noticePopoverList?.length">
              <div v-for="notice in noticePopoverList" :key="notice?.id" class="notice-pop-item">
                <div class="notice-title">
                  <div class="title-wrapper">
                    <span v-if="notice.is_read === 0" class="spot" />
                    <span class="title" :class="notice.is_fail === 1 ? 'fail' : ''" :title="notice?.title || 'Title'">{{ notice?.title || 'Title'
                    }}</span>
                  </div>
                  <span class="time">{{ notice?.created_at }}</span>
                </div>
                <div class="desc">
                  {{ notice?.abstract }}
                </div>
              </div>
            </template>
            <BaseEmpty v-else-if="!noticePopoverList?.length" message="No more Information" />
          </div>
        </template>
        <Button
          label="View All Notifications >" variant="link" severity="warn" class="notice-pop-more"
          @click="($event) => { handleViewAllNotification($event) }"
        />
      </div>
    </Popover>
    <div class="header-end">
      <div class="user-notice">
        <OverlayBadge v-if="!!unreadCount" :value="unreadCount > 99 ? '99+' : unreadCount" severity="danger" size="small" @click="(e: any) => { handleNotification(1, e) }">
          <img class="notice-image" :src="noticeImage" alt="notice">
        </OverlayBadge>
        <img v-else-if="!unreadCount" class="notice-image" :src="noticeImage" alt="notice" @click="(e) => { handleNotification('ALL', e) }">
      </div>
      <Menu ref="userSettingMenu" :model="userSettingItems" :popup="true" />
      <div class="user-profile" @click="userSettingMenu.toggle($event)">
        <Avatar
          :image="avatarUrl"
          shape="circle"
          class="user-avatar"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mobile-menu-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
}
.notice-pop {
  max-height: 60vh;
  overflow: hidden;
  padding: 10px;
  width: 480px;

  .notice-content {
    max-height: calc(60vh - 100px);
    overflow-y: auto;

    /* 设置整个滚动条的样式 */
    &::-webkit-scrollbar {
      width: 4px;
      /* 滚动条的宽度 */
      height: 4px;
      /* 滚动条的高度 */
    }

    /* 滚动条轨道 */
    &::-webkit-scrollbar-track {
      background: transparent;
      /* 轨道背景色 */
      border-radius: 10px;
      /* 圆角 */
    }

    /* 滚动条滑块 */
    &::-webkit-scrollbar-thumb {
      background: #f1f1f1;
      /* 滑块颜色 */
      border-radius: 10px;
      /* 圆角 */
    }

    /* 当鼠标悬停在滑块上 */
    &::-webkit-scrollbar-thumb:hover {
      background: #555;
      /* 悬停效果颜色 */
    }
  }

  .notice-pop-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 22px;

    >button {
      width: 178px;
    }
  }

  .notice-pop-item {
    border-bottom: 1px solid #000000;
    padding: 18px 0;
    margin-bottom: 4px;

    .notice-title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-wrapper {
        display: flex;
        align-items: center;
        flex: 1;

        .spot {
          display: inline-block;
          width: 10px;
          height: 10px;
          flex-shrink: 0;
          border-radius: 50%;
          margin-right: 5px;
          background: var(--color-orange-500);
          overflow: hidden;
        }

        .title {
          cursor: default;
          max-width: 300px;
          display: inline-block;
          font-weight: 600;
          font-size: 1.125rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          border-bottom: 0.0625rem solid transparent;
          margin-bottom: - 0.0625rem;
          transition: border-bottom 0.2s ease;

          &.fail {
            font-family: Jost;
            font-weight: 600;
            font-style: SemiBold;
            font-size: 1.125rem;
            line-height: 100%;
            letter-spacing: 0%;
            color: #EB001B;

            &:hover {
              border-bottom: 0.0625rem solid #EB001B;
            }
          }

          &:hover {
            border-bottom: 0.0625rem solid #545454;
          }

          &.fail {
            color: #EB001B;
          }
        }
      }

      .time {
        width: 150px;
        flex-shrink: 0;
      }
    }

    .desc {
      color: #545454;
      font-family: Jost;
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-top: 6px;

    }
  }

  .notice-pop-more {
    color: var(--p-button-warn-background);
    width: 100%;
  }

}
</style>
