<script setup lang="ts">
import type { DictItem } from '@/services/api/dict'
import { Format } from '@shared'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import { useDict } from '@/composables/useDict'
import { useExport } from '@/composables/useExport'
import { usePermissions } from '@/composables/usePermissions'
import { useRequestList } from '@/composables/useRequestList'
import { Permissions } from '@/constants/permissions'
import { SearchFieldType } from '@/constants/search'
import { transactions as transactionApi } from '@/services/api'
import { formatDate } from '@/utils/date'
import { addAllToDict } from '@/utils/dict'

defineOptions({
  name: 'flexiratesMerchantPaymentsList',
})

// 使用 useRequestList 处理交易列表数据
const requestList = useRequestList<Transaction.Info[], Api.TransactionListReq>({
  requestFn: transactionApi.getList,
})

const { hasPermission } = usePermissions()

const router = useRouter()
const route = useRoute()

const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  // other,
  setSearchParams,
} = requestList

// 列配置
const columns = ref<TableColumnItem[]>([
  {
    template: 'property_number',
    field: 'customer_plan.customer_property.property_number',
    header: 'Property Number',
    style: { minWidth: '140px' },
  },
  {
    template: 'remit_date',
    field: 'remit_date',
    header: 'Remit Date',
    style: { minWidth: '140px' },
  },
  {
    template: 'payment_amount',
    field: 'payment_amount',
    header: 'Amount',
    style: { minWidth: '140px' },
  },
  {
    template: 'payment_method',
    field: 'payment_method',
    header: 'Payment Method',
    style: { minWidth: '140px' },
  },
  {
    field: 'customer.name',
    header: 'Customer Name',
    style: { minWidth: '140px' },
  },
  {
    template: 'address',
    field: 'address',
    header: 'Address',
    style: { minWidth: '140px' },
  },
  {
    field: 'customer.email_primary',
    header: 'Email',
    style: { minWidth: '140px' },
  },
  {
    field: 'customer.phone_mobile',
    header: 'Mobile',
    style: { minWidth: '140px' },
  },
])

const searchModel = ref<Api.TransactionListReq>({
  'payment_method': null,
  'keyword': '',
  'remit_date[]': [],
})

const paymentMethodOptions = ref<DictItem[]>([])

const dateRange = ref<string[]>([])

const searchFields = computed(() => [
  {
    name: 'keyword',
    label: 'Search Payments',
    type: SearchFieldType.TEXT,
    placeholder: 'Search for a payment',
    defaultValue: '',
  },
  {
    name: 'payment_method',
    label: 'Payment Method',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: paymentMethodOptions.value,
    defaultValue: '',
  },
])

const moreSearchFields = computed(() => [
  {
    name: 'remit_date[]',
    label: 'Remit Date Range',
    type: SearchFieldType.DATE_RANGE,
    placeholder: 'Select Remit Date Range',
    defaultValue: dateRange.value,
  },
])

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value, ['refunded_date[]' as keyof Transaction.Info[]])
  search()
}

// 手动刷新数据
const refreshData = () => {
  refresh()
}

// 排序处理
const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

// Setup export functionality
const { isExporting, handleExport } = useExport({
  exportFn: transactionApi.exportTransactions,
  getParams: () => {
    return setSearchParams(searchModel.value)
  },
  onExportStart: () => {
    window.$toast.add({
      severity: 'info',
      summary: 'Export Started',
      detail: 'Preparing your export file...',
    })
  },
})

const detail = ({ data }: { data: Transaction.Info }) => {
  if (!hasPermission(Permissions.PAYMENTS_DETAIL)) {
    return
  }
  router.push({
    name: 'flexiratesMerchantPaymentsDetail',
    params: {
      id: data.id,
    },
  })
}

useDict('credit_brand', (res) => {
  paymentMethodOptions.value = addAllToDict(res)
})

onMounted(() => {
  const query = route.query
  if (query && Object.keys(query).length > 0) {
    searchModel.value = {
      'payment_method': query.payment_method ? Number(query.payment_method) : null,
      'keyword': (query.keyword as string) || '',
      'remit_date[]': Array.isArray(query['remit_date[]']) ? query['remit_date[]'] as string[] : [],
    }
    setSearchParams(searchModel.value)
    refresh()
  }
  else {
    searchModel.value = {
      'payment_method': null,
      'keyword': '',
      'remit_date[]': [],
    }
    setSearchParams(searchModel.value)
    refresh()
  }
})
</script>

<template>
  <div>
    <BaseSearch
      v-model="searchModel" :loading="loading" :basic-search-fields="searchFields"
      :advanced-search-fields="moreSearchFields" @search="handleSearch"
    />

    <div v-if="hasPermission(Permissions.PAYMENTS_EXPORT)" class="flex justify-end items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <BaseExportDialog :loading="loading" :export-loading="isExporting" @export="handleExport" />
    </div>

    <BaseDataTable
      :value="list" :columns="columns" :show-edit-column="false"
      :show-search-bar="false" :scrollable="true" :show-multiple-column="false" :loading="loading" :paginator="true"
      :rows="50" :total-records="total" :lazy="true" data-key="id" sort-mode="single" :row-hover="true"
      :sort-field="$route.query.sort_by as string" :sort-order="$route.query.sort_order === 'desc' ? -1 : 1"
      search-placeholder="Search" type-placeholder="Filter By" :failed="failed" :failure-message="failureMessage"
      :striped-rows="false" @page="handlePageChange" @sort="handleSort" @refresh="refreshData" @row-click="detail"
    >
      <template #property_number="{ data }">
        <span class="underline">
          {{ data?.customer_plan?.customer_property?.property_number }}
        </span>
      </template>
      <template #address="{ data }">
        <span v-if="data?.customer_plan?.customer_property?.street_address">
          {{ data?.customer_plan?.customer_property?.street_address }} ,
        </span>
        <span v-if="data?.customer_plan?.customer_property?.suburb">
          {{ data?.customer_plan?.customer_property?.suburb }}
        </span>
      </template>
      <template #payment_method="{ data }">
        <BaseCardType :card-type="data?.customer_banking?.credit_brand" :is-show-card-number="true" :text="data?.customer_banking?.account_no" />
      </template>
      <template #payment_amount="{ data }">
        <span>
          {{ Format.formatAmount(data?.payment_amount, data?.payment_currency) }}
        </span>
      </template>
      <template #remit_date="{ data }">
        <span>
          {{ formatDate(data.created_at) }}
        </span>
      </template>
    </BaseDataTable>
  </div>
</template>

<style lang="scss" scoped>
.card {
  padding: 1rem;
}

.p-button {
  padding: 0.5rem 1rem;
  font-weight: 500;
}

.p-button-text {
  color: var(--text-color-secondary);
}

.p-button-text:hover {
  background: var(--surface-hover);
  color: var(--text-color);
}
</style>
