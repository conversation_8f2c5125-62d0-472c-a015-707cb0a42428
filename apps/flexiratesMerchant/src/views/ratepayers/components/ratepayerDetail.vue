<script setup lang="ts">
import { Format } from '@shared'
import { EditPlanType } from '@shared/constants/flexirates'
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import { Field, Form as VeeForm } from 'vee-validate'
import { computed, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as yup from 'yup'
import { useExport } from '@/composables/useExport'
import { usePermissions } from '@/composables/usePermissions'
import { Permissions } from '@/constants/permissions'
import { ratepayer as ratepayerApi, schedules as schedulesApi } from '@/services/api'
import { formatBankAccount } from '@/utils/format'
import activityLogs from './activityLogs.vue'
import RatepayerDetailNotes from './ratepayerDetailNotes.vue'

interface Props {
  data: Ratepayer.DetailInfo | null
  customerId: string
  loading?: boolean
}

interface Emits {
  (e: 'edit'): void

  (e: 'back'): void

  (e: 'update'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const router = useRouter()
const route = useRoute()

// 编辑状态管理
const isEditing = ref(false)
const updateLoading = ref(false)
const { hasPermission } = usePermissions()

const ratepayerDetailNotesRef = ref<InstanceType<typeof RatepayerDetailNotes>>()

// 表单数据
const formData = reactive({
  first_name: '',
  last_name: '',
  email: '',
  mobile: '',
  street_address: '',
  city: '',
  state: '',
  postcode: '',
})

const bankingList = ref<Ratepayer.CustomerBanking[] | null>(null)

// 表单验证 schema
const validationSchema = toTypedSchema(yup.object({
  first_name: yup.string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .required('First name is required'),
  last_name: yup.string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .required('Last name is required'),
  email: yup.string()
    .email('Please enter a valid email address')
    .required('Email address is required'),
  mobile: yup.string()
    .matches(/^\+?[\d\s\-()]+$/, 'Please enter a valid mobile number')
    .min(10, 'Mobile number must be at least 10 characters')
    .required('Mobile number is required'),
  street_address: yup.string()
    .min(5, 'Street address must be at least 5 characters')
    .max(200, 'Street address must be less than 200 characters')
    .required('Street address is required'),
  city: yup.string()
    .min(2, 'City must be at least 2 characters')
    .max(100, 'City must be less than 100 characters')
    .required('City is required'),
  state: yup.string()
    .min(2, 'State must be at least 2 characters')
    .max(50, 'State must be less than 50 characters')
    .required('State is required'),
  postcode: yup.string()
    .matches(/^\d{4,6}$/, 'Please enter a valid postcode (4-6 digits)')
    .required('Postcode is required'),
}))

const isSkipPaymentLoading = ref(false)

// 计算属性：获取当前显示的数据
const displayData = computed(() => {
  if (isEditing.value) {
    return formData
  }

  const userProfile = props.data?.customer?.user_profile
  const customerUser = props.data?.customer?.customer_user
  return {
    first_name: userProfile?.first_name || '',
    last_name: userProfile?.last_name || '',
    email: customerUser?.email || '',
    mobile: customerUser?.mobile || '',
    street_address: userProfile?.address_line_1 || '',
    city: userProfile?.city || '',
    state: userProfile?.state || '',
    postcode: userProfile?.postcode || '',
  }
})

// 初始化表单数据
const initFormData = () => {
  if (props.data) {
    const userProfile = props.data.customer?.user_profile
    const customerUser = props.data.customer?.customer_user
    formData.first_name = userProfile?.first_name || ''
    formData.last_name = userProfile?.last_name || ''
    formData.email = customerUser?.email || ''
    formData.mobile = customerUser?.mobile || ''
    formData.street_address = userProfile?.address_line_1 || ''
    formData.city = userProfile?.city || ''
    formData.state = userProfile?.state || ''
    formData.postcode = userProfile?.postcode || ''

    if (props.data.customer_banking) {
      props.data.customer_banking.weight = 0
    }

    if (props.data.customer_banking_secondary) {
      props.data.customer_banking_secondary.weight = 1
    }

    bankingList.value = [
      props.data?.customer_banking || null,
      props.data?.customer_banking_secondary || null,
    ].filter(Boolean) as Ratepayer.CustomerBanking[]
  }
}

const handleShowScheduleDetails = () => {
  // console.log('handleShowScheduleDetails', props.data)
  router.push({
    name: 'flexiratesMerchantSchedulesDetail',
    params: {
      id: props?.data?.id,
    },
    query: {
      address: props.data?.property?.street_address,
      status: props.data?.status,
    },
  })
}

const handleViewAllTransactionHistory = () => {
  router.push({
    name: 'flexiratesMerchantPaymentsList',
    query: {
      keyword: props.data?.property?.property_number,
    },
  })
}

// 开始编辑
const startEdit = () => {
  initFormData()
  isEditing.value = true
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  initFormData() // 重置表单数据
}

const handleSkipPayment = () => {
  window.$confirm.require({
    header: 'Skip Payment',
    message: 'Are you sure you want to skip payment?',
    accept: async () => {
      isSkipPaymentLoading.value = true
      try {
        const { code } = await schedulesApi.editSchedule({
          edit_type: EditPlanType.SKIP_NEXT_PAYMENT,
          id: Number(props.data?.related_id),
        })
        if (code === 0) {
          window.$toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Payment skipped successfully',
          })
        }
      }
      finally {
        isSkipPaymentLoading.value = false
      }
    },
  })
}

// 保存编辑
const saveEdit = async () => {
  if (!props.data?.id) {
    window.$toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Unable to get user ID',
    })
    return
  }

  updateLoading.value = true

  try {
    const updateData: Api.RatepayerUpdateReq = {
      first_name: formData.first_name,
      last_name: formData.last_name,
      email: formData.email,
      mobile: formData.mobile,
      street_address: formData.street_address,
      city: formData.city,
      state: formData.state,
      postcode: formData.postcode,
    }

    const { code } = await ratepayerApi.updateRatepayer(props.data.customer_id, updateData)

    if (code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Ratepayer information updated successfully',
      })
      isEditing.value = false
      emit('update')
    }
  }
  catch (error) {
    console.error('Failed to update ratepayer information:', error)
  }
  finally {
    updateLoading.value = false
  }
}

const { handleExport, isExporting } = useExport({
  exportFn: ratepayerApi.exportActivityLog,
  getParams: () => ({
    customer_id: route.query?.customerId as string,
  }),
})

// 监听数据变化，初始化表单
watch(() => props.data, (newData) => {
  if (newData) {
    initFormData()
  }
}, { immediate: true })
</script>

<template>
  <div class="ratepayer-detail">
    <div class="bg-white p-8 py-6 rounded-2xl">
      <div class="ratepayer-header flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">
          Ratepayer Details
        </h1>
        <!-- 页面级操作按钮 -->
        <div class="flex gap-2">
          <div class="flex flex-col text-(--colors-gray) mr-4">
            <div class="text-sm">
              Logged in <span class="font-medium text-(--colors-primary)">{{
                props?.data?.customer?.customer_user?.login_times }}
                times</span>
              <template v-if="props?.data?.customer?.customer_user?.last_login_time">
                | Last logged in: <span class="font-medium text-(--colors-primary)">
                  {{ dayjs(props?.data?.customer?.customer_user?.last_login_time).format('DD MMM YYYY HH:mm:ss') }}
                </span>
              </template>
            </div>
            <div class="text-sm">
              Registered date: <span class="font-medium text-(--colors-primary)">
                {{ dayjs(props?.data?.customer?.customer_user?.created_at).format('DD MMM YYYY HH:mm:ss') }}
              </span>
            </div>
          </div>
          <Button
            v-if="!isEditing && hasPermission(Permissions.RATE_PAYER_UPDATE)" type="button" label="EDIT DETAILS"
            class="!px-6" @click="startEdit"
          />
          <Button
            v-if="isEditing && hasPermission(Permissions.RATE_PAYER_UPDATE)" type="button" label="SAVE"
            severity="success" :loading="updateLoading" @click="saveEdit"
          />
          <Button
            v-if="isEditing && hasPermission(Permissions.RATE_PAYER_UPDATE)" type="button" label="CANCEL"
            severity="secondary" outlined @click="cancelEdit"
          />
        </div>
      </div>

      <!-- 编辑模式表单容器 -->
      <VeeForm v-if="isEditing" :initial-values="formData" :validation-schema="validationSchema" class="edit-form">
        <!-- 姓名行 -->
        <div class="form-row">
          <!-- 名字 -->
          <div class="field">
            <label class="mb-2 block font-medium text-gray-700">First Name</label>
            <Field v-slot="{ field, errorMessage }" name="first_name">
              <div class="field-input-container">
                <InputText
                  v-bind="field" v-model="formData.first_name" placeholder="First Name" class="w-full"
                  :class="{ 'p-invalid': errorMessage }"
                />
                <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </div>
            </Field>
          </div>

          <div class="field">
            <label class="mb-2 block font-medium text-gray-700">Last Name</label>
            <Field v-slot="{ field, errorMessage }" name="last_name">
              <div class="field-input-container">
                <InputText
                  v-bind="field" v-model="formData.last_name" placeholder="Last Name" class="w-full"
                  :class="{ 'p-invalid': errorMessage }"
                />
                <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </div>
            </Field>
          </div>
        </div>

        <!-- 联系信息行 -->
        <div class="form-row">
          <!-- 邮箱地址 -->
          <div class="field">
            <label class="mb-2 block font-medium text-gray-700">Email Address</label>
            <Field v-slot="{ field, errorMessage }" name="email">
              <div class="field-input-container">
                <InputText
                  v-bind="field" v-model="formData.email" placeholder="Email Address" type="email"
                  class="w-full" :class="{ 'p-invalid': errorMessage }"
                />
                <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </div>
            </Field>
          </div>

          <!-- 手机号码 -->
          <div class="field">
            <label class="mb-2 block font-medium text-gray-700">Mobile Number</label>
            <Field v-slot="{ field, errorMessage }" name="mobile">
              <div class="field-input-container">
                <InputText
                  v-bind="field" v-model="formData.mobile" placeholder="Mobile Number" class="w-full"
                  :class="{ 'p-invalid': errorMessage }"
                />
                <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </div>
            </Field>
          </div>
        </div>

        <!-- 地址信息 -->
        <div class="form-row">
          <div class="field">
            <label class="mb-2 block font-medium text-gray-700">Mailing Address</label>
            <div class="flex flex-col gap-2 w-3/5">
              <!-- 街道地址 -->
              <Field v-slot="{ field, errorMessage }" name="street_address">
                <div class="field-input-container">
                  <InputText
                    v-bind="field" v-model="formData.street_address" placeholder="Street Address"
                    class="w-full" :class="{ 'p-invalid': errorMessage }"
                  />
                  <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>

              <!-- 城市 -->
              <Field v-slot="{ field, errorMessage }" name="city">
                <div class="field-input-container">
                  <InputText
                    v-bind="field" v-model="formData.city" placeholder="City" class="w-full"
                    :class="{ 'p-invalid': errorMessage }"
                  />
                  <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>

              <!-- 州和邮编 -->
              <div class="flex gap-4">
                <Field v-slot="{ field, errorMessage }" name="state">
                  <div class="field-input-container flex-1">
                    <InputText
                      v-bind="field" v-model="formData.state" placeholder="State" class="w-full"
                      :class="{ 'p-invalid': errorMessage }"
                    />
                    <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </div>
                </Field>

                <Field v-slot="{ field, errorMessage }" name="postcode">
                  <div class="field-input-container flex-1">
                    <InputText
                      v-bind="field" v-model="formData.postcode" placeholder="Postcode" class="w-full"
                      :class="{ 'p-invalid': errorMessage }"
                    />
                    <Message v-if="errorMessage" class="mt-1" severity="error" variant="simple">
                      {{ errorMessage }}
                    </Message>
                  </div>
                </Field>
              </div>
            </div>
          </div>
        </div>
      </VeeForm>

      <!-- 只读模式 -->
      <div v-else>
        <!-- 姓名行 -->
        <div class="form-row">
          <!-- 名字 -->
          <div class="field">
            <label class="mb-2 block font-medium text-gray-700">First Name</label>
            <div class="detail-value">
              {{ displayData.first_name || '-' }}
            </div>
          </div>

          <div class="field">
            <label class="mb-2 block font-medium text-gray-700">Last Name</label>
            <div class="detail-value">
              {{ displayData.last_name || '-' }}
            </div>
          </div>
        </div>

        <!-- 联系信息行 -->
        <div class="form-row">
          <!-- 邮箱地址 -->
          <div class="field">
            <label class="mb-2 block font-medium text-gray-700">Email Address</label>
            <div class="detail-value">
              {{ displayData.email || '-' }}
            </div>
          </div>

          <!-- 手机号码 -->
          <div class="field">
            <label class="mb-2 block font-medium text-gray-700">Mobile Number</label>
            <div class="detail-value">
              {{ displayData.mobile || '-' }}
            </div>
          </div>
        </div>

        <!-- 地址信息 -->
        <div class="form-row">
          <div class="field">
            <label class="mb-2 block font-medium text-gray-700">Mailing Address</label>
            <div class="flex flex-col gap-2 w-3/5">
              <div class="detail-value">
                {{ displayData.street_address || '-' }}
              </div>
              <div class="detail-value">
                {{ displayData.city || '-' }}
              </div>
              <div class="flex gap-4">
                <div class="detail-value">
                  {{ displayData.state || '-' }}
                </div>
                <div class="detail-value">
                  {{ displayData.postcode || '-' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Property Details 部分 -->
    <div class="mt-8 bg-white p-8 py-6 rounded-2xl">
      <div class="flex items-center justify-between">
        <h3 class="sub-title">
          Rates Information
        </h3>
        <div class="flex gap-4 items-center">
          <!-- <Button label="INITIATE HOLD" class="rates-btn hold-btn" /> -->
          <Button label="RELEASE" class="rates-btn release-btn" />
        </div>
      </div>
      <div class="text-(--colors-gray) flex justify-between mt-8">
        <div class="flex flex-col gap-2">
          <div class="flex">
            <span class="font-bold">Property Number:</span>
            <span class="pl-4">{{ props?.data?.property?.property_number || '-' }}</span>
          </div>
          <div class="flex">
            <span class="font-bold">Address:</span>
            <span class="pl-4">{{ props?.data?.property?.street_address || '-' }}</span>
          </div>
        </div>
        <!-- <div class="flex gap-4">
          <Button label="Edit" severity="warn" />
        </div> -->
      </div>

      <div class="flex flex-col lg:flex-row lg:gap-8 text-(--colors-gray) mt-8">
        <div class="flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1">
          <div>
            <!-- 财年 -->
            Full Rate Amount for {{ props?.data?.fiscal_year }}
          </div>
          <div class="!mb-0 font-bold text-4xl ">
            {{ Format.formatAmount(props?.data?.statistics?.full_rate_amount) }}
          </div>
        </div>
        <div class="flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1">
          <div>
            Amount Paid to Date
          </div>
          <div class="!mb-0 font-bold text-4xl">
            {{ Format.formatAmount(props?.data?.statistics?.paid_amount) }}
          </div>
        </div>
        <div class="flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1">
          <div>
            Remaining Balance
          </div>
          <div class="!mb-0 font-bold text-4xl">
            {{ Format.formatAmount(props?.data?.statistics?.remaining_amount) }}
          </div>
        </div>
        <div class="flex flex-col justify-between gap-4 border border-(--colors-gray) rounded-xl p-6 flex-1">
          <div>
            Number of Remaining
            Scheduled Payments
          </div>
          <div class="!mb-0 font-bold text-4xl">
            {{ props?.data?.statistics?.remaining_schedule_number }}
          </div>
        </div>
      </div>

      <div class="flex flex-col lg:flex-row justify-between lg:gap-8 text-(--colors-gray) mt-8">
        <div class="p-6">
          <div class="text-lg font-bold">
            Schedule Plan
          </div>
          <div class="mt-2">
            {{ props.data?.frequency }} Amount of {{ Format.formatAmount(props?.data?.price?.amount_per_unit) }}
          </div>
          <Button label="SHOW SCHEDULE DETAILS" severity="primary" class="!mt-4" @click="handleShowScheduleDetails" />
        </div>
        <div v-if="props?.data?.last_payment" class="ml-6 p-6">
          <div class="text-lg font-bold">
            Payment
          </div>
          <div class="mt-2">
            Last {{ props?.data?.last_payment?.frequency }} Amount of
            {{ Format.formatAmount(props?.data?.last_payment?.payment_amount) }} received on
            {{ dayjs(props?.data?.last_payment?.payment_at).format('DD/MM/YYYY') }}
          </div>
          <Button
            label="VIEW ALL TRANSACTION HISTORY" severity="primary" class="!mt-4" :loading="isSkipPaymentLoading"
            @click="handleViewAllTransactionHistory"
          />
        </div>
        <div class="ml-6 p-6">
          <div class="text-lg font-bold">
            Next Payment
          </div>
          <div class="mt-2">
            {{ props.data?.frequency }} <span v-if="props.data?.frequency !== 'Full Amount'">Amount</span> of {{
              Format.formatAmount(props?.data?.next_payment_amount) }} due on
            {{ dayjs(props?.data?.next_process_date).format('DD/MM/YYYY') }}
          </div>
          <Button
            v-if="
              props.data?.final_payment_date && props.data?.next_process_date
                && dayjs(props.data?.final_payment_date).isAfter(dayjs(props.data?.next_process_date))"
            label="SKIP PAYMENT" class="!mt-4 rates-btn hold-btn" @click="handleSkipPayment"
          />
        </div>
      </div>
    </div>

    <!-- Payment Methods 部分 -->
    <div class="mt-8 bg-white p-8 py-6 rounded-2xl">
      <h3 class="sub-title">
        Payment Method
      </h3>
      <div class="space-y-3">
        <div v-for="payment in bankingList || []" :key="payment.id" class="payment-card transition-shadow">
          <div class="flex items-start justify-between">
            <div class="flex items-center space-x-3">
              <!-- Mastercard Logo -->
              <div class="relative w-12 h-8 flex items-center justify-center">
                <div class="absolute w-6 h-6 bg-red-500 rounded-full" />
                <div class="absolute w-6 h-6 bg-orange-400 rounded-full ml-3" />
              </div>

              <div class="flex flex-col">
                <span class="text-lg font-semibold text-gray-900 mb-1">MASTERCARD</span>
                <div class="flex items-center">
                  <span class="text-gray-600 text-sm">
                    {{ formatBankAccount(payment.account_no) }}
                  </span>
                  <span v-if="payment.expiration_month && payment.expiration_year" class="text-gray-600 text-sm ml-4">
                    Expires {{ payment.expiration_month }}/{{ payment.expiration_year }}
                  </span>
                </div>
              </div>
            </div>

            <div class="text-right">
              <div class="flex items-center justify-end space-x-1 mb-2">
                <template v-if="payment.weight === 0">
                  <span class="text-green-600 text-sm font-medium">Primary</span>
                  <i class="pi pi-star-fill text-yellow-400 text-sm" />
                </template>
                <template v-else-if="payment.weight === 1">
                  <span class="text-gray-600 text-sm font-medium">Secondary</span>
                </template>
              </div>
              <div class="text-sm font-medium text-gray-900">
                <template v-if="payment?.last_payment?.transaction?.payment_amount">
                  Last Payment: {{ Format.formatAmount(payment?.last_payment?.transaction?.payment_amount) }}
                </template>
              </div>
            </div>
          </div>
        </div>
        <!-- no payment method -->
        <div v-if="!bankingList?.length" class="payment-card transition-shadow">
          No Payment Method
        </div>
      </div>
    </div>

    <!-- Schedule 部分 -->
    <div class="mt-8 bg-white p-8 py-6 rounded-2xl">
      <div class="flex justify-between items-center">
        <h3 class="sub-title !mb-0">
          Notes and Remarks
        </h3>
        <Button
          class="w-40" type="button" label="ADD NOTE" severity="warn"
          @click="ratepayerDetailNotesRef?.openAddNoteModal()"
        />
      </div>
      <RatepayerDetailNotes ref="ratepayerDetailNotesRef" :customer-id="props.customerId" />
    </div>

    <!-- activity log -->
    <div class="mt-8 bg-white p-8 py-6 rounded-2xl">
      <div class="flex justify-between items-center">
        <h3 class="sub-title !mb-0">
          Activity Log
        </h3>
        <Button class="w-40" type="button" label="EXPORT" :loading="isExporting" @click="handleExport('csv')" />
      </div>
      <div class="activity-list">
        <activityLogs />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.ratepayer-detail {
  border-radius: 16px;

  .ratepayer-header {
    border-bottom: 2px solid var(--colors-gray);

    h1 {
      color: #031F73;
      margin: 1rem 0;
      margin-top: 0;
    }
  }

  .sub-title {
    color: #031F73;
    font-size: 1.5rem;
    font-weight: 600;
    // margin-bottom: 1rem;
  }

  .rates-btn {
    width: 150px;
  }

  .hold-btn {
    --p-button-primary-background: #ef4f27;
    --p-button-primary-border-color: #ef4f27;
    --p-button-primary-hover-background:#d64622;
    --p-button-primary-hover-border-color:#d64622;
    --p-button-primary-active-background:#c9411f;
    --p-button-primary-active-border-color:#c9411f;
  }

  .release-btn {
    --p-button-primary-background: #39b54a;
    --p-button-primary-border-color: #39b54a;
    --p-button-primary-hover-background:#33a342;
    --p-button-primary-hover-border-color:#33a342;
    --p-button-primary-active-background:#2c9139;
    --p-button-primary-active-border-color:#2c9139;
  }

  :deep(.p-datatable-table-container) {
    padding: 0;
  }
}

.form-row {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.field {
  flex: 1;
  display: flex;
  margin-bottom: 1rem;

  label {
    min-width: 130px;
    line-height: 1;
    padding: 0.5rem 0;
  }
}

.detail-value {
  flex: 1;
  padding: 0.5rem 1rem;
  border: 1px solid var(--colors-gray);
  border-radius: 0.75rem;
  color: var(--colors-gray);
  min-height: 2.5rem;
  display: flex;
  align-items: center;

  &:empty::before {
    content: '-';
    color: var(--colors-gray);
  }
}

// 占位符样式
.property-placeholder,
.payment-placeholder,
.schedule-placeholder {
  border: 2px dashed #e2e8f0;
  border-radius: 0.75rem;
  padding: 3rem 2rem;
  text-align: center;
  background-color: #fafbfc;
  transition: all 0.2s ease;

  &:hover {
    border-color: #cbd5e1;
    background-color: #f1f5f9;
  }

  .placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    i {
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 0.9rem;
      margin: 0;
    }
  }
}

.payment-card {
  background-color: var(--bg-colors-white);
  border-radius: 8px;
  padding: 1.5rem;
}

// 编辑模式样式
.edit-form {
  .field {
    // 保持与只读模式相同的布局
    display: flex;
    flex: 1;
    margin-bottom: 1rem;

    label {
      min-width: 130px;
      line-height: 1;
      padding: 0.5rem 0;
      font-weight: 500;
    }
  }

  .field-input-container {
    flex: 1;
  }

  .p-inputtext {
    border-radius: 0.75rem;
    border: 1px solid var(--colors-gray);
    padding: 0.5rem 1rem;
    min-height: 2.5rem;

    &:focus {
      border-color: #031F73;
      box-shadow: 0 0 0 2px rgba(3, 31, 115, 0.1);
    }

    &.p-invalid {
      border-color: #ef4444;
    }
  }

  .p-message {
    font-size: 0.875rem;
  }
}

// 响应式样式
@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-col {
    min-width: 100%;
  }

  .property-placeholder,
  .payment-placeholder,
  .schedule-placeholder {
    padding: 2rem 1rem;
  }
}

// 小型移动设备
@media screen and (max-width: 480px) {
  .ratepayer-detail {
    max-width: 100%;
  }

  .detail-value {
    font-size: 0.9rem;
    padding: 0.6rem 0.8rem;
  }

  .form-row {
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .property-placeholder,
  .payment-placeholder,
  .schedule-placeholder {
    padding: 1.5rem 1rem;

    .placeholder-content {
      i {
        font-size: 2rem;
      }

      p {
        font-size: 0.8rem;
      }
    }
  }
}

// 打印样式
@media print {
  .detail-value {
    background-color: transparent;
    border: 1px solid #ddd;
  }

  button {
    display: none;
  }

  .property-placeholder,
  .payment-placeholder,
  .schedule-placeholder {
    border-color: #ddd;
    background-color: transparent;
  }
}

.activity-list {
  padding: 1em;
  background-color: var(--bg-colors-white);
  margin-top: .8rem;
  border-radius: 16px;
}
</style>
