<script setup lang="ts">
import type { FormSubmitEvent } from '@primevue/forms'
import { Form } from '@primevue/forms'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { z } from 'zod'
import { publicRouterName } from '@/router/publicRouterName'
import { useUserStore } from '@/store/modules/user'

interface RegisterForm {
  email: string
  password: string
  confirmPassword: string
  agree: boolean
}

interface RegisterFormSubmitEvent extends FormSubmitEvent {
  values: RegisterForm
}

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)

const initialValues = ref<RegisterForm>({
  email: '',
  password: '',
  confirmPassword: '',
  agree: false,
})

const resolver = ref(
  zodResolver(
    z
      .object({
        email: z
          .string({ message: t('validation.emailRequired') })
          .min(1, { message: t('validation.emailRequired') })
          .email({
            message: t('validation.emailInvalid'),
          }),
        password: z
          .string()
          .min(8, { message: t('validation.passwordMinLength') })
          .regex(/[A-Z]/, { message: t('validation.passwordUppercase') })
          .regex(/[a-z]/, { message: t('validation.passwordLowercase') })
          .regex(/\d/, { message: t('validation.passwordNumber') }),
        confirmPassword: z
          .string()
          .min(1, { message: t('validation.confirmPasswordRequired') }),
        agree: z
          .boolean()
          .refine(val => val === true, { message: t('validation.agreeTerms') }),
      })
      .refine(data => data.password === data.confirmPassword, {
        message: t('validation.passwordsNotMatch'),
        path: ['confirmPassword'],
      }),
  ),
)

const onFormSubmit = async ({ values, valid }: RegisterFormSubmitEvent) => {
  if (!valid) {
    return
  }
  try {
    loading.value = true
    await userStore.register(values.email, values.password)
    router.push({ name: publicRouterName.LOGIN })
  }
  catch {
    // Error is handled by the store
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="auth-container">
    <!-- Logo -->
    <div class="logo-wrap">
      <div class="logo">
        <Image src="/placeholder.svg" alt="Image" width="100%" />
      </div>
    </div>

    <div class="auth-content-wrap">
      <div class="auth-content">
        <h1 class="title">
          {{ t("register.title") }}
        </h1>
        <p class="subtitle">
          {{ t("register.subtitle") }}
        </p>

        <Form
          v-slot="$form"
          :initial-values
          :resolver="resolver"
          class="flex flex-col gap-6 w-full"
          @submit="onFormSubmit as unknown as FormSubmitEvent"
        >
          <div class="flex flex-col gap-4">
            <label for="email">{{ t("register.email") }}</label>
            <InputText name="email" type="text" :placeholder="t('register.email')" />
            <Message
              v-if="$form.email?.invalid"
              severity="error"
              size="small"
              variant="simple"
            >
              {{ $form.email.error.message }}
            </Message>
          </div>
          <div class="flex flex-col gap-4">
            <label for="password">{{ t("register.password") }}</label>
            <Password
              name="password"
              type="text"
              :placeholder="t('register.password')"
              toggle-mask
              fluid
              :feedback="true"
            />
            <Message
              v-if="$form.password?.invalid"
              severity="error"
              size="small"
              variant="simple"
            >
              {{ $form.password.error?.message }}
            </Message>
          </div>
          <div class="flex flex-col gap-4">
            <label for="confirmPassword">{{ t("register.confirmPassword") }}</label>
            <Password
              name="confirmPassword"
              type="text"
              :placeholder="t('register.confirmPassword')"
              toggle-mask
              fluid
              :feedback="false"
            />
            <Message
              v-if="$form.confirmPassword?.invalid"
              severity="error"
              size="small"
              variant="simple"
            >
              {{ $form.confirmPassword.error?.message }}
            </Message>
          </div>

          <div class="flex flex-col gap-4">
            <div class="flex items-center gap-2">
              <Checkbox v-model="$form.agree" name="agree" binary />
              <label for="agree">{{ t("register.agreeTerms") }}</label>
            </div>
            <Message
              v-if="$form.agree?.invalid"
              severity="error"
              size="small"
              variant="simple"
            >
              {{ $form.agree.error?.message }}
            </Message>
          </div>

          <div class="flex gap-4 justify-between">
            <Button
              class="!p-0"
              text
              :label="t('register.haveAccount')"
              @click="$router.push({ name: publicRouterName.LOGIN })"
            />
          </div>
          <Button
            :loading="loading"
            class="!mt-6 w-full"
            type="submit"
            severity="secondary"
            :label="t('button.submit')"
          />
        </Form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/common/auth-layout.scss';
</style>
