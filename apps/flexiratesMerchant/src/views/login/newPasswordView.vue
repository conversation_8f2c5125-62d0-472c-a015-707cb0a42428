<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { z } from 'zod'
import logo from '@/assets/flexiratesMerchant/logo.png'
import loginBg from '@/assets/merchant/login-bg.png'
import { publicRouterName } from '@/router/publicRouterName'
import { user as userApi } from '@/services/api'

// interface ForgotPasswordForm {
//   email: string
// }

// interface ForgotPasswordFormSubmitEvent extends FormSubmitEvent {
//   values: ForgotPasswordForm
// }

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const isUpdated = ref(false)

const newPasswordFormRef = ref()

const model = ref({
  password: '',
  reEnterPassword: '',
})

const schema = toTypedSchema(z.object({
  password: z.string()
    .min(8, { message: 'Use at least 8 characters. One uppercase letter, one lowercase letter, one number.' })
    .regex(/\d/, { message: 'Use at least 8 characters. One uppercase letter, one lowercase letter, one number.' })
    .regex(/[a-z]/, { message: 'Use at least 8 characters. One uppercase letter, one lowercase letter, one number.' })
    .regex(/[A-Z]/, { message: 'Use at least 8 characters. One uppercase letter, one lowercase letter, one number.' }),
  reEnterPassword: z.string()
    .refine(value => value === model.value.password, {
      message: 'Password do not match',
    }),
}))

const onFormSubmit = async (values: any) => {
  try {
    loading.value = true
    const token = route.params?.token as string
    const res = await userApi.setNewPassword({ new_password: values.password, token })
    if (res.code === 0) {
      isUpdated.value = true
      setTimeout(() => {
        router.push({ name: publicRouterName.LOGIN })
      }, 2000)
    }
  }
  catch {

  }
  finally {
    loading.value = false
  }
}

const getPasswordStrength = (password: string): string => {
  const strength = {
    hasLower: /[a-z]/.test(password),
    hasUpper: /[A-Z]/.test(password),
    hasNumber: /\d/.test(password),
    isLongEnough: password.length >= 12,
  }
  const score = Object.values(strength).filter(Boolean).length

  if (score <= 1) { return 'Low' }
  if (score <= 3) { return 'Average' }
  return 'Excellent'
}
const getStrengthColor = (password: string) => {
  const strength = getPasswordStrength(password)
  const colors = {
    Low: '#eb001b',
    Average: '#FFD700',
    Excellent: '#7ed956',
  }
  return colors[strength as keyof typeof colors]
}
const getStrengthSegments = (password: string) => {
  const strength = getPasswordStrength(password)
  const colors = {
    Low: '#eb001b',
    Average: '#FFD700',
    Excellent: '#7ed956',
  }

  return [
    {
      active: strength !== 'None',
      color: colors[strength as keyof typeof colors],
    },
    {
      active: ['Average', 'Excellent'].includes(strength),
      color: ['Average', 'Excellent'].includes(strength) ? colors[strength as keyof typeof colors] : '#fff',
    },
    {
      active: strength === 'Excellent',
      color: strength === 'Excellent' ? colors[strength as keyof typeof colors] : '#fff',
    },
  ]
}
</script>

<template>
  <div class="auth-container">
    <!-- Logo -->
    <div class="logo-wrap">
      <div class="logo">
        <Image :src="logo" width="230px" alt="Image" />
      </div>
    </div>
    <div class="auth-bg-wrap" :style="{ backgroundImage: `url(${loginBg})`, backgroundSize: '100% 100%' }" />
    <div class="auth-content-wrap">
      <div class="auth-content">
        <div v-if="!isUpdated">
          <h1 class="title">
            <!-- {{ t("forgotPassword.title") }} -->
            New Password
          </h1>
          <p class="forgot-subtitle">
            <!-- {{ t("forgotPassword.subtitle") }} -->
            Enter a new password.
          </p>
          <VeeForm
            ref="newPasswordFormRef" :validation-schema="schema"
            class="flex flex-col gap-6 w-full new-password-form" @submit="onFormSubmit"
          >
            <div class="flex flex-col gap-4">
              <Field
                v-slot="{ field, errorMessage }" v-model="model.password" as="div"
                class="flex flex-col gap-4" name="password"
              >
                <label class="form-label">New Password<span class="text-[#ff3131]">*</span></label>
                <Password
                  v-bind="field" type="text" placeholder="Password" toggle-mask fluid
                  :feedback="false"
                />
                <div v-if="field.value?.length > 0" class="strength-container">
                  <div class="strength-bars">
                    <div
                      v-for="(segment, index) in getStrengthSegments(field.value)" :key="index"
                      class="strength-segment" :class="{ active: segment.active }"
                      :style="{ backgroundColor: segment.color }"
                    />
                  </div>
                  <small class="strength-indicator">
                    Password strength: <span :style="{ color: getStrengthColor(field.value) }">{{
                      getPasswordStrength(field.value) }}</span>
                  </small>
                </div>
                <Message v-if="errorMessage" class="px-[1rem]" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </Field>
              <Field
                v-slot="{ field, errorMessage }" v-model="model.reEnterPassword" as="div"
                class="flex flex-col gap-4 mt-4" name="reEnterPassword"
              >
                <label class="form-label">Re-enter Password<span class="text-[#ff3131]">*</span></label>
                <Password
                  v-bind="field" type="text" placeholder="Re-enter Password" toggle-mask fluid
                  :feedback="false"
                />
                <Message v-if="errorMessage" class="px-[1rem]" severity="error" variant="simple">
                  {{ errorMessage }}
                </Message>
              </Field>
            </div>

            <Button
              :loading="loading" class="!mt-6 w-full login-submit" type="submit" severity="warn"
              label="SUBMIT"
            />
          </VeeForm>
        </div>
        <div v-else class="password-updated flex flex-col justify-center items-center gap-4">
          <div class="img">
            <img src="@/assets/merchant/isUpdated.png" alt="" style="width: 136px;">
          </div>
          <div class="title">
            Password Updated!
          </div>
          <div class="forgot-subtitle" style="margin-top: 0;">
            Please wait.You will be redirected to the Login Page.
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/common/auth-layout.scss';
</style>
