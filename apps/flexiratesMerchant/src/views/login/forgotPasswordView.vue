<script setup lang="ts">
import type { FormSubmitEvent } from '@primevue/forms'
import { Form } from '@primevue/forms'
import { yupResolver } from '@primevue/forms/resolvers/yup'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import * as yup from 'yup'
import logo from '@/assets/flexiratesMerchant/logo.png'
import loginBg from '@/assets/merchant/login-bg.png'
import { useUserStore } from '@/store/modules/user'

interface ForgotPasswordForm {
  email: string
}

interface ForgotPasswordFormSubmitEvent extends FormSubmitEvent {
  values: ForgotPasswordForm
}

const { t } = useI18n()
const userStore = useUserStore()
const loading = ref(false)

const initialValues = ref<ForgotPasswordForm>({
  email: '',
})

const resolver = ref(
  yupResolver(
    yup.object({
      email: yup
        .string()
        .required(t('validation.emailRequired'))
        .matches(
          /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/,
          t('validation.emailInvalid'),
        ),
    }),
  ),
)

const isSubmit = ref(false)
const onFormSubmit = async ({ values, valid }: ForgotPasswordFormSubmitEvent) => {
  if (!valid) {
    return
  }
  try {
    loading.value = true
    await userStore.forgotPassword(values.email)
    isSubmit.value = true
    // router.push('/login')
  }
  catch {
    isSubmit.value = false
    // Error is handled by the store
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="auth-container">
    <!-- Logo -->
    <div class="logo-wrap">
      <div class="logo">
        <Image :src="logo" width="230px" alt="Image" />
      </div>
    </div>
    <div class="auth-bg-wrap" :style="{ backgroundImage: `url(${loginBg})`, backgroundSize: '100% 100%' }" />
    <div class="auth-content-wrap">
      <div class="auth-content">
        <div v-if="!isSubmit" class="forgot-password">
          <h1 class="title">
            {{ t("forgotPassword.title") }}
          </h1>
          <p class="forgot-subtitle">
            {{ t("forgotPassword.subtitle") }}
          </p>

          <Form
            v-slot="$form" :initial-values :resolver="resolver" class="flex flex-col gap-6 w-full"
            @submit="(onFormSubmit as unknown as FormSubmitEvent)"
          >
            <div class="flex flex-col gap-4">
              <InputText name="email" type="text" :placeholder="t('forgotPassword.email')" class="form-input" />
              <Message v-if="$form.email?.invalid" severity="error" size="small" variant="simple">
                {{ $form.email.error.message }}
              </Message>
            </div>

            <Button
              :loading="loading" class="!mt-6 w-full login-submit" type="submit" severity="warn"
              :label="t('button.submit')"
            />

            <div class="flex gap-4 justify-center">
              <Button class="!p-0" text :label="t('forgotPassword.backToLogin')" @click="$router.back()" />
            </div>
          </Form>
        </div>
        <div v-else class="is-submit">
          <h1 class="title">
            Check Your Inbox
          </h1>
          <p class="forgot-subtitle">
            We have sent password recovery instructions to your email.
          </p>
          <div class="flex gap-4 justify-center">
            <Button
              class="!mt-6 w-full back-to-login" severity="info"
              label="BACK TO LOGIN" @click="$router.back()"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/common/auth-layout.scss';
</style>
