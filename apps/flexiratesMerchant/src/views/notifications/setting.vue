<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { getDictByType } from '@/services/api/dict'
import { getPreferencesList, updatePreferences } from '@/services/api/notifications'
import SettingList from './components/settingList.vue'

const loading = ref(false)

const currentCategory = ref<string | number>(0)
const categoriesList = ref<any[]>([])
const eventsList = ref<any[]>([
  { label: 'Email', value: 'is_send_email' },
  { label: 'SMS', value: 'is_send_sms' },
  { label: 'In_app', value: 'is_send_in_app' },
])
const preferencesList = ref<any[]>([])

const initFn = () => {
  loading.value = true
  getPreferencesList().then((resp) => {
    const records = resp?.data
    preferencesList.value = records
  }).finally(() => {
    loading.value = false
  })
}

const updateData = (value: { id: string, [key: string]: string }) => {
  loading.value = true
  updatePreferences(value).then((resp) => {
    const records = resp?.data
    preferencesList.value = records
  }).finally(() => {
    loading.value = false
  })
}

const getCategoryList = () => getDictByType('notification_flexirates_category ').then((resp) => {
  categoriesList.value = resp?.data || []
  currentCategory.value = resp?.data?.[0]?.value || 0
})

// const getEvents = () => getDictByType('notification_merchant_events').then((resp) => {
//   console.log('communication_type', resp)
//   // eventsList.value = resp?.data || []
// })

const handleTabs = (value: string | number) => {
  currentCategory.value = value
}

getCategoryList()
// getEvents()
initFn()
onMounted(() => {
})
</script>

<template>
  <Card>
    <template #content>
      <div class="notification-setting bg-white">
        <div class="notification-preference">
          <div class="title">
            Notification Preferences
          </div>
          <div class="subtitle">
            Choose when and how we contact you. We may still send you important notifications about your
            account outside of your notification settings.
          </div>
        </div>
        <Tabs :value="currentCategory" @update:value="handleTabs">
          <TabList>
            <Tab v-for="tab in categoriesList" :key="tab.value" :value="tab.value">
              {{ tab.label }}
            </Tab>
          </TabList>
          <TabPanels>
            <TabPanel v-for="tabItem in categoriesList" :key="tabItem.value" :value="tabItem.value">
              <template #default>
                <SettingList
                  :key="tabItem.value" :tab="tabItem"
                  :data-list="preferencesList"
                  :events="eventsList" :loading="loading" @update-data="updateData"
                />
              </template>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </div>
    </template>
  </Card>
</template>

<style scoped lang='scss'>
.notification-setting {
  &.bg-white {
    background-color: var(--color-white);
  }

  display: flex;
  flex-direction: column;

  .notification-preference {
    margin-bottom: 1.31rem;

    .title {
      font-weight: 800;
      font-size: 1.5625rem;
      color: var(--color-indigo-950);
      margin-bottom: 0.25rem;
    }

    .subtitle {
      font-weight: 400;
      font-size: 0.8125rem;
      // color: #545454;
      color: var(--color-gray-500);
    }
  }

  :deep(.p-tab) {
    padding: 0.44rem;
    margin: 0 1.18rem;
    border-color: transparent;

    &:first-of-type {
      margin-left: 0;
    }
  }

  :deep(.p-tab-active) {
    border-color: var(--color-orange-500);
    color: var(--color-orange-500);
  }

  :deep(.p-tablist-active-bar) {
    background: var(--color-orange-500);
  }

  :deep(.p-tab:not(.p-tab-active):not(.p-disabled):hover) {
    color: var(--color-orange-500);
    border-color: var(--color-orange-500);
  }
}
</style>
