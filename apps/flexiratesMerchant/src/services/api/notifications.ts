import { GET, POST } from '@/services/http'

export const getNotifyList = (params: Api.FlexiratesNotifyListReq) => GET('/cardinia/notify/list', { params })

export const deleteNotify = (data: Api.FlexiratesNotifyDeleteReq) => POST('/cardinia/notify/delete', data)

export const updateNotify = (data: Api.FlexiratesNotifyDeleteReq) => POST('/cardinia/notify/updateRead', data)

export const getUnreadCount = () => GET('/cardinia/notify/unreadCount')

export const markNotify = (data: Api.EditNotifyRef) => POST('/cardinia/notify/markReadStatus', data)

export const getPreferencesList = (params?: any) => GET('/cardinia/notify/preferences', { params })

export const updatePreferences = (data?: Api.PreferencesUpdateRef) => POST('/cardinia/notify/preferencesUpdate', data)
