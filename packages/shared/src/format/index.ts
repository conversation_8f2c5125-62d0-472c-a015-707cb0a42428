// 格式化金额
export function formatAmount(amount?: number | string, currency: string = 'AUD') {
  if (!amount && amount !== 0) {
    return '0'
  }

  if (!currency) {
    return `$${amount}`
  }

  return Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD',
  }).format(Number(amount))
}

// 格式化数字 千位逗号
export function formatNumber(number?: number | string) {
  const num = Number(number)
  if (Number.isNaN(num)) {
    return '0'
  }

  // 如果是字符串且包含小数点，保留原始的小数位数
  if (typeof number === 'string' && number.includes('.')) {
    const decimalPlaces = number.split('.')[1].length
    return new Intl.NumberFormat('en-AU', {
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces,
    }).format(num)
  }

  return new Intl.NumberFormat('en-AU').format(num)
}
