<script setup lang="ts">
import ProgressSpinner from 'primevue/progressspinner'

interface Props {
  loading?: boolean
  error?: string | null
}

interface Emits {
  (event: 'retry'): void
}

withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
})

const emit = defineEmits<Emits>()

const handleRetry = () => {
  emit('retry')
}
</script>

<template>
  <div class="common-page">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <ProgressSpinner style="width: 50px; height: 50px;" stroke-width="4" />
      <p class="loading-text">
        Loading...
      </p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">
        ⚠️
      </div>
      <p class="error-message">
        {{ error }}
      </p>
      <button class="retry-button" @click="handleRetry">
        Retry
      </button>
    </div>

    <!-- Content -->
    <div v-else class="common-page-content">
      <slot />
    </div>
  </div>
</template>

<style scoped lang="scss">
.common-page {
  width: 100%;
  min-height: 100vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;

  .loading-text {
    margin: 0;
    color: #6b7280;
    font-size: 16px;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;
  text-align: center;

  .error-icon {
    font-size: 48px;
  }

  .error-message {
    margin: 0;
    color: #dc2626;
    font-size: 16px;
    max-width: 400px;
  }

  .retry-button {
    padding: 8px 16px;
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #2563eb;
    }
  }
}
</style>
