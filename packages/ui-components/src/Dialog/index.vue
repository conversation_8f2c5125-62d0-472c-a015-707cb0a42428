<script setup lang="ts">
import Dialog from 'primevue/dialog'

const model = defineModel<boolean>('modelValue', { required: true, default: false, type: Boolean })
</script>

<template>
  <!-- 点击遮罩层关闭 -->
  <Dialog v-model:visible="model" pt:root:class="m-dialog" :dismissable-mask="true" :modal="true" v-bind="$attrs">
    <template v-for="(_, name) in $slots" #[name]="scope">
      <slot :name="name" v-bind="scope || {}" />
    </template>
  </Dialog>
</template>

<style lang="scss">
.p-dialog,.m-dialog {
  border: none;
  padding: 0 24px;
  --p-dialog-background: #F5F5FF;
  .p-dialog-header {
    border-bottom: 1px solid #545454;
    color: #031f73;
    font-size: 18px;
    font-weight: 600;
    padding-bottom: 8px;
    padding-left: 0;
    padding-right: 0;
  }

  .p-dialog-content {
    padding: 16px 0;
  }
}
</style>
